
UI: ShadCN/UI	

<!-- 添加字段 -->
npx wrangler d1 execute datatool_d1 --local --command "ALTER TABLE t_user ADD COLUMN api_key TEXT;"
<!-- 更新数据 -->
npx wrangler d1 execute datatool_d1 --local --command "UPDATE t_user
SET api_key = LOWER(HEX(RANDOMBLOB(16)))
WHERE api_key IS NULL;"
<!-- 新增索引 -->
npx wrangler d1 execute datatool_d1 --local --command "CREATE UNIQUE INDEX IF NOT EXISTS idx_user_api_key ON t_user(api_key);"
