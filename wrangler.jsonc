/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "dataminer",
	"main": "src/index.ts",
	"compatibility_date": "2025-05-25",
	"compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"],
	"assets": {
		"binding": "ASSETS",
		"directory": "./public"
	},
	"observability": {
		"enabled": true
	},
	"durable_objects": {
		"bindings": [
			{
				"name": "USER_LOCK",
				"class_name": "UserLock",
				"script_name": "dataminer"
			}
		]
	},
	"placement": {
		"mode": "smart"
	},
	"migrations": [
		{
			"tag": "v1",
			"new_classes": ["UserLock"]
		}
	],
	"queues": {
		"producers": [{ "binding": "QUEUE", "queue": "datatool-queue" }],
		"consumers": [{ "queue": "datatool-queue" }]
	},
	"kv_namespaces": [
		{
			"binding": "datatool_kv",
			"id": "e551b13c80f8416bb2b27463cca31b19"
		}
	],
	"r2_buckets": [
		{
			"binding": "datatool",
			"bucket_name": "datatool"
		}
	],
	"d1_databases": [
		{
			"binding": "datatool_d1",
			"database_name": "datatool_d1",
			"database_id": "260cb5a5-bde8-456d-a109-9ed0025f8e4f"
		}
	],
	"vars": {
		"ENV": "prod",
		"JWT_SECRET": "dataminer",
		"JWT_EXPIRES_IN": "2592000",
		"GOOGLE_CLIENT_ID": "************-oncehsk1kcplno5tmadet0jm5fiq04ht.apps.googleusercontent.com",
		"GOOGLE_CLIENT_SECRET": "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",
		"BREVO_API_KEY": "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",
		"STRIPE_SECRET_KEY": "***********************************************************************************************************",
		"STRIPE_PUBLISHABLE_KEY": "pk_live_51RicHFCYEKfHEsUfEYRSaM8fiZrUyNcGaHKuSMQuWhoguPavEXbKLYh97eWgcTzrhQ29E2TOTD2HUZZ2N7O1Uu8K00FIICl6li",
		"VITE_STRIPE_PUBLISHABLE_KEY": "pk_live_51RicHFCYEKfHEsUfEYRSaM8fiZrUyNcGaHKuSMQuWhoguPavEXbKLYh97eWgcTzrhQ29E2TOTD2HUZZ2N7O1Uu8K00FIICl6li",
		"STRIPE_WEBHOOK_SECRET": "whsec_cKvgxN7awMDA59SniTDbiyyNmMZGIngu",
		"cloudflare": {
			"apiToken": "****************************************",
			"account_id": "2c2e00bb8965b410bb58f9aac22fe0d0"
		}
	},
	"env": {
		"dev": {
			"vars": {
				"ENV": "dev",
				"JWT_SECRET": "dataminer-dev",
				"JWT_EXPIRES_IN": "2592000",
				"GOOGLE_CLIENT_ID": "************-oncehsk1kcplno5tmadet0jm5fiq04ht.apps.googleusercontent.com",
				"GOOGLE_CLIENT_SECRET": "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",
				"BREVO_API_KEY": "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",
				"STRIPE_SECRET_KEY": "sk_test_51RicHSE6OVVxho368bGKEVfILFGpqxf77FZDjsqnG8o4aGPL74SocdDiCGBskzmX2HAu3C7yY2DVkbovlux3n4m800XLcFwnTA",
				"STRIPE_PUBLISHABLE_KEY": "pk_test_51RicHSE6OVVxho36oFGQKER3KqvEhMjrefsEcoZbkPjuU3KoC4KNXqx1La2kyjA3X2sZzYFMbzPpFX1p2fnLCHrv00nFyYCZTk",
				"VITE_STRIPE_PUBLISHABLE_KEY": "pk_test_51RicHSE6OVVxho36oFGQKER3KqvEhMjrefsEcoZbkPjuU3KoC4KNXqx1La2kyjA3X2sZzYFMbzPpFX1p2fnLCHrv00nFyYCZTk",
				"STRIPE_WEBHOOK_SECRET": "whsec_6kq0CoSWyn4dTTGANePp7nvy727BjmZM",
				"cloudflare": {
					"apiToken": "****************************************",
					"account_id": "2c2e00bb8965b410bb58f9aac22fe0d0"
				}
			},
			"kv_namespaces": [
				{
					"binding": "datatool_kv",
					"id": "70b0044fbb0b47089f45771477f7816e"
				}
			],
			"queues": {
				"producers": [{ "binding": "QUEUE", "queue": "datatool-queue-dev" }],
				"consumers": [{ "queue": "datatool-queue-dev" }]
			},
			"r2_buckets": [
				{
					"binding": "datatool",
					"bucket_name": "datatool-dev"
				}
			],
			"d1_databases": [
				{
					"binding": "datatool_d1",
					"database_name": "datatool_d1_dev",
					"database_id": "04a16b2f-3158-4181-a809-fb82439d8b6b"
				}
			],
			"placement": {
				"mode": "smart"
			},
			"migrations": [
				{
					"tag": "v1",
					"new_classes": ["UserLock"]
				}
			],
			"durable_objects": {
				"bindings": [
					{
						"name": "USER_LOCK",
						"class_name": "UserLock",
						"script_name": "dataminer-dev"
					}
				]
			}
		},
		"local": {
			"vars": {
				"ENV": "local",
				"JWT_SECRET": "dataminer-local",
				"JWT_EXPIRES_IN": "2592000",
				"GOOGLE_CLIENT_ID": "************-oncehsk1kcplno5tmadet0jm5fiq04ht.apps.googleusercontent.com",
				"GOOGLE_CLIENT_SECRET": "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",
				"BREVO_API_KEY": "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",
				"STRIPE_SECRET_KEY": "sk_test_51RicHSE6OVVxho368bGKEVfILFGpqxf77FZDjsqnG8o4aGPL74SocdDiCGBskzmX2HAu3C7yY2DVkbovlux3n4m800XLcFwnTA",
				"STRIPE_PUBLISHABLE_KEY": "pk_test_51RicHSE6OVVxho36oFGQKER3KqvEhMjrefsEcoZbkPjuU3KoC4KNXqx1La2kyjA3X2sZzYFMbzPpFX1p2fnLCHrv00nFyYCZTk",
				"VITE_STRIPE_PUBLISHABLE_KEY": "pk_test_51RicHSE6OVVxho36oFGQKER3KqvEhMjrefsEcoZbkPjuU3KoC4KNXqx1La2kyjA3X2sZzYFMbzPpFX1p2fnLCHrv00nFyYCZTk",
				"STRIPE_WEBHOOK_SECRET": "whsec_d50ce8d3449b5ce73b963b8d93787ac2ced058601fb966c7f2cb170edd435f02",
				"cloudflare": {
					"apiToken": "****************************************",
					"account_id": "2c2e00bb8965b410bb58f9aac22fe0d0"
				}
			},
			"kv_namespaces": [
				{
					"binding": "datatool_kv",
					"id": "70b0044fbb0b47089f45771477f7816e"
				}
			],

			"queues": {
				"producers": [{ "binding": "QUEUE", "queue": "datatool-queue" }],
				"consumers": [{ "queue": "datatool-queue" }]
			},
			"r2_buckets": [
				{
					"binding": "datatool",
					"bucket_name": "datatool"
				}
			],
			"d1_databases": [
				{
					"binding": "datatool_d1",
					"database_name": "datatool_d1",
					"database_id": "260cb5a5-bde8-456d-a109-9ed0025f8e4f"
				}
			],
			"migrations": [
				{
					"tag": "v1",
					"new_classes": ["UserLock"]
				}
			],
			"placement": {
				"mode": "smart"
			},
			"durable_objects": {
				"bindings": [
					{
						"name": "USER_LOCK",
						"class_name": "UserLock"
					}
				]
			}
		}
	}
}
