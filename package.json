{"name": "dataminer", "version": "0.1.0", "description": "A Cloudflare Worker application built with Hono framework", "private": true, "scripts": {"dev": "concurrently --kill-others --prefix \"[{name}]\" --names \"CLIENT,WRANGLER\" --prefix-colors \"blue,green\" \"npm run client:dev\" \"wrangler dev --env local --port 8205 \"", "deploy:dev": "npm run build && wrangler deploy --env dev", "deploy:prod": "npm run build && wrangler deploy", "deploy": "npm run deploy:prod", "migrate": "node src/migrations/migrate.js", "test": "vitest", "cf-typegen": "wrangler types", "client:dev": "vite", "client:build": "vite build", "build": "npm run client:build && wrangler build"}, "dependencies": {"@cloudflare/puppeteer": "^1.0.3", "@hono/node-server": "^1.14.3", "@hono/zod-validator": "^0.1.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/jsonwebtoken": "^9.0.9", "class-variance-authority": "^0.7.0", "cloudflare": "^4.5.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "hono": "^4.0.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "postcss-nesting": "^13.0.1", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "stripe": "^18.3.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "@cloudflare/workers-types": "^4.20250525.0", "@types/node": "^22.15.21", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^9.2.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.5.2", "vite": "^5.0.10", "vitest": "~3.0.7", "wrangler": "^4.16.1"}, "engines": {"node": ">=16"}}