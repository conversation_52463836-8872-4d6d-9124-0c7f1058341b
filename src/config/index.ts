import dotenv from 'dotenv';

// Load environment variables for local development
if (typeof process !== 'undefined' && process.env) {
  dotenv.config();
}

// 为Cloudflare Workers环境创建配置函数
export const createConfig = (env?: any) => {
  // 在Workers环境中使用env参数，在本地开发中使用process.env
  const getEnvVar = (key: string, defaultValue: string = '') => {
    if (env && env[key] !== undefined) {
      return env[key];
    }
    if (typeof process !== 'undefined' && process.env && process.env[key] !== undefined) {
      return process.env[key];
    }
    return defaultValue;
  };

  return {
    jwt: {
      secret: getEnvVar('JWT_SECRET', 'dataminder'),
      expiresIn: parseInt(getEnvVar('JWT_EXPIRES_IN', '2592000'), 10), // 30 days in seconds
    },
    stripe: {
      secretKey: getEnvVar('STRIPE_SECRET_KEY', ''),
      publishKey: getEnvVar('STRIPE_PUBLISHABLE_KEY', ''),
      webhookKey: getEnvVar('STRIPE_WEBHOOK_SECRET', '')
    }
    // Add other configuration sections as needed
  } as const;
};

// 默认配置（用于本地开发）
export const config = createConfig();