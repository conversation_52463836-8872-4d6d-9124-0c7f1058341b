import { productModel } from '../models/schema';
import { and, desc, eq, like, sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { User } from '../middleware/auth';
import { Context } from 'hono';
import { CreateProductSchema, GetProductListSchema, UpdateProductSchema, DeleteProductSchema } from '../validators/product';
import { StripeService } from './stripe';

export class ProductService {
	private readonly db: ReturnType<typeof drizzle>;
	private readonly loginUser: User;
	private readonly kv: KVNamespace;

	constructor(private readonly c: Context) {
		this.db = drizzle(c.env.datatool_d1);
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		
	}

	async createProduct(data: CreateProductSchema) {
		if (this.loginUser?.role !== 'admin') {
			throw new Error('Permission denied');
		}
		// 先创建Stripe商品和价格
		const stripeService = new StripeService(this.c);
		const stripeResult = await stripeService.createStripeProduct({
			name: data.name,
			description: data.description || undefined,
			price: data.price * 100, // 转换为分
			currency: data.currency,
			is_active: Boolean(data.is_active),
		});

		// Stripe创建成功后，创建本地商品记录
		const result = await this.db.insert(productModel).values({
			name: data.name,
			description: data.description,
			price: data.price * 100, // 转换为分
			currency: data.currency,
			is_active: data.is_active,
			credits: data.credits,
			stripe_product_id: stripeResult.stripe_product_id,
			stripe_price_id: stripeResult.stripe_price_id,
		}).returning();

		// 删除商品列表缓存
		await this.clearProductListCache();

		return result[0];
	}

	async updateProduct(data: UpdateProductSchema) {
		if (this.loginUser?.role !== 'admin') {
			throw new Error('Permission denied');
		}
		// 1. 获取原商品信息
		const existingProduct = await this.db
			.select()
			.from(productModel)
			.where(eq(productModel.id, data.id));

		if (!existingProduct[0]) {
			throw new Error('商品不存在');
		}

		const product = existingProduct[0];

		// 2. 先将原来的商品设为未激活
		await this.db.update(productModel)
			.set({ is_active: 0 })
			.where(eq(productModel.id, data.id));

		// 3. 处理Stripe请求 - 先停用原商品，再创建新商品
		const stripeService = new StripeService(this.c);
		const stripeResult = await stripeService.updateProductWithNewVersion(
			product.stripe_product_id!,
			product.stripe_price_id!,
			{
				name: data.name ?? product.name,
				description: data.description ?? (product.description || undefined),
				price: (data.price ?? (product.price / 100)) * 100, // 转换为分
				currency: data.currency ?? product.currency,
				is_active: Boolean(data.is_active ?? 1),
			}
		);

		// 4. 创建新商品记录
		const result = await this.db.insert(productModel).values({
			name: data.name ?? product.name,
			description: data.description ?? product.description,
			price: (data.price ?? (product.price / 100)) * 100, // 转换为分
			currency: data.currency ?? product.currency,
			is_active: data.is_active ?? 1,
			credits: data.credits ?? product.credits,
			stripe_product_id: stripeResult.stripe_product_id,
			stripe_price_id: stripeResult.stripe_price_id,
		}).returning();

		// 删除商品列表缓存
		await this.clearProductListCache();

		return result[0];
	}

	// 生成缓存键
	private generateCacheKey(data: GetProductListSchema): string {
		const { name, page, pageSize, credits, is_active, currency } = data;
		const keyParts = [
			'product_list',
			`page_${page}`,
			`size_${pageSize}`,
			name ? `name_${name}` : '',
			credits !== undefined ? `credits_${credits}` : '',
			is_active !== undefined ? `active_${is_active}` : '',
			currency ? `currency_${currency}` : ''
		].filter(Boolean);
		return keyParts.join('_');
	}

	// 清除所有商品列表缓存
	private async clearProductListCache(): Promise<void> {
		try {
			// 由于KV不支持通配符删除，我们使用一个版本号来使所有缓存失效
			const currentVersion = Date.now().toString();
			await this.kv.put('product_list_version', currentVersion);
		} catch (error) {
			console.error('清除商品列表缓存失败:', error);
		}
	}

	// 获取当前缓存版本
	private async getCacheVersion(): Promise<string> {
		try {
			const version = await this.kv.get('product_list_version');
			return version || '0';
		} catch (error) {
			console.error('获取缓存版本失败:', error);
			return '0';
		}
	}

	async getProductList(data: GetProductListSchema): Promise<{
		products: {
			id: number;
			name: string;
			description: string | null;
			price: number;
			credits: number;
			currency: string;
			stripe_product_id: string | null;
			stripe_price_id: string | null;
			is_active: number;
			created_at: string;
			updated_at: string;
		}[];
		total: number;
	}> {
		// 生成缓存键
		const cacheKey = this.generateCacheKey(data);
		const cacheVersion = await this.getCacheVersion();
		const versionedCacheKey = `${cacheKey}_v${cacheVersion}`;

		// 尝试从缓存获取数据
		try {
			const cachedData = await this.kv.get(versionedCacheKey);
			if (cachedData) {
				return JSON.parse(cachedData);
			}
		} catch (error) {
			console.error('获取缓存数据失败:', error);
		}

		// 缓存未命中，从数据库查询
		const { name, page, pageSize, credits, is_active, currency } = data;
		const offset = (page - 1) * pageSize;

		const whereConditions = [];
		if (is_active !== undefined) {
			whereConditions.push(eq(productModel.is_active, is_active));
		}
		if (credits !== undefined) {
			whereConditions.push(eq(productModel.credits, credits));
		}
		if (currency) {
			whereConditions.push(eq(productModel.currency, currency));
		}
		if (name) {
			whereConditions.push(like(productModel.name, `%${name}%`));
		}

		const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

		const products = await this.db
			.select()
			.from(productModel)
			.where(whereClause)
			.orderBy(desc(productModel.created_at))
			.limit(pageSize)
			.offset(offset);

		const totalResult = await this.db
			.select({ count: sql<number>`count(*)` })
			.from(productModel)
			.where(whereClause);

		for (const product of products) {
			product.price = product.price / 100;
		}

		const result = {
			products,
			total: totalResult[0]?.count || 0,
		};

		// 将结果存入缓存，设置5分钟过期时间
		try {
			await this.kv.put(versionedCacheKey, JSON.stringify(result), {
				expirationTtl: 3600
			});
		} catch (error) {
			console.error('存储缓存数据失败:', error);
		}

		return result;
	}

	async getProductById(id: number) {
		const product = await this.db
			.select()
			.from(productModel)
			.where(eq(productModel.id, id));
		
		if (product[0]) {
			product[0].price = product[0].price / 100;
		}
		return product[0] || null;
	}
}