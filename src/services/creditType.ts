import { D1Database } from '@cloudflare/workers-types';
import { creditTypeModel } from '../models/schema';
import { eq, desc } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { Context } from 'hono';
import { User } from '../middleware/auth';

export interface CreditTypeData {
	type_key: string;
	type_name: string;
	credit_amount: number;
	operation_type: 'add' | 'deduct';
	description?: string;
}

export class CreditTypeService {
	private readonly db: ReturnType<typeof drizzle>;
	private readonly loginUser: User;
	private readonly kv: KVNamespace;
	private readonly CACHE_PREFIX = 'credit_type:';
	private readonly CACHE_ALL_KEY = 'credit_types:all';
	private readonly CACHE_TTL = 3600; // 1小时缓存

	constructor(private readonly c: Context) {
		this.db = drizzle(c.env.datatool_d1);
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
	}

	// 缓存相关方法
	private getCacheKey(id?: number | string): string {
		return id ? `${this.CACHE_PREFIX}${id}` : this.CACHE_ALL_KEY;
	}

	private async getCachedCreditType(key: string) {
		try {
			const cached = await this.kv.get(key);
			return cached ? JSON.parse(cached) : null;
		} catch (error) {
			console.error('获取缓存失败:', error);
			return null;
		}
	}

	private async setCachedCreditType(key: string, data: any) {
		try {
			await this.kv.put(key, JSON.stringify(data), { expirationTtl: this.CACHE_TTL });
		} catch (error) {
			console.error('设置缓存失败:', error);
		}
	}

	private async deleteCachedCreditType(key: string) {
		try {
			await this.kv.delete(key);
		} catch (error) {
			console.error('删除缓存失败:', error);
		}
	}

	private async clearAllCache() {
		try {
			// 清除所有积分类型缓存
			await this.kv.delete(this.CACHE_ALL_KEY);
			// 注意：这里只清除了all缓存，单个缓存会自然过期
			// 如果需要立即清除所有单个缓存，需要维护一个缓存键列表
		} catch (error) {
			console.error('清除缓存失败:', error);
		}
	}

	// 获取所有积分类型
	async getAllCreditTypes() {
		// 先尝试从缓存获取
		const cached = await this.getCachedCreditType(this.CACHE_ALL_KEY);
		if (cached) {
			return cached;
		}

		// 缓存未命中，从数据库获取
		const creditTypes = await this.db.select().from(creditTypeModel).orderBy(desc(creditTypeModel.id));

		// 更新缓存
		await this.setCachedCreditType(this.CACHE_ALL_KEY, creditTypes);

		return creditTypes;
	}

	// 根据ID获取积分类型
	async getCreditTypeById(id: number) {
		// 先尝试从缓存获取
		const cacheKey = this.getCacheKey(id);
		const cached = await this.getCachedCreditType(cacheKey);
		if (cached) {
			return cached;
		}

		// 缓存未命中，从数据库获取
		const [creditType] = await this.db.select().from(creditTypeModel).where(eq(creditTypeModel.id, id));

		// 如果找到数据，更新缓存
		if (creditType) {
			await this.setCachedCreditType(cacheKey, creditType);
		}

		return creditType;
	}

	// 根据type_key获取积分类型
	async getCreditTypeByKey(typeKey: string) {
		// 先尝试从缓存获取
		const cacheKey = this.getCacheKey(`key:${typeKey}`);
		const cached = await this.getCachedCreditType(cacheKey);
		if (cached) {
			return cached;
		}

		// 缓存未命中，从数据库获取
		const [creditType] = await this.db.select().from(creditTypeModel).where(eq(creditTypeModel.type_key, typeKey));

		// 如果找到数据，更新缓存
		if (creditType) {
			await this.setCachedCreditType(cacheKey, creditType);
		}

		return creditType;
	}

	// 创建积分类型
	async createCreditType(data: CreditTypeData) {
		if (this.loginUser?.role !== 'admin') {
			throw new Error('权限不足');
		}
		// 检查type_key是否已存在
		const existing = await this.getCreditTypeByKey(data.type_key);
		if (existing) {
			throw new Error('积分类型键值已存在');
		}

		const [creditType] = await this.db
			.insert(creditTypeModel)
			.values({
				type_key: data.type_key,
				type_name: data.type_name,
				credit_amount: data.credit_amount,
				operation_type: data.operation_type,
				description: data.description,
			})
			.returning();

		// 更新缓存
		if (creditType) {
			await this.setCachedCreditType(this.getCacheKey(creditType.id), creditType);
			await this.setCachedCreditType(this.getCacheKey(`key:${creditType.type_key}`), creditType);
			await this.clearAllCache(); // 清除all缓存，下次查询时重新生成
		}

		return creditType;
	}

	// 更新积分类型
	async updateCreditType(id: number, data: Partial<CreditTypeData>) {
		if (this.loginUser?.role !== 'admin') {
			throw new Error('权限不足');
		}
		// 获取原始数据用于清除旧缓存
		const originalCreditType = await this.getCreditTypeById(id);

		// 如果更新type_key，检查是否已存在
		if (data.type_key) {
			const existing = await this.getCreditTypeByKey(data.type_key);
			if (existing && existing.id !== id) {
				throw new Error('积分类型键值已存在');
			}
		}

		const [creditType] = await this.db
			.update(creditTypeModel)
			.set({
				...data,
				updated_at: new Date().getTime(),
			})
			.where(eq(creditTypeModel.id, id))
			.returning();

		// 更新缓存
		if (creditType) {
			// 更新ID缓存
			await this.setCachedCreditType(this.getCacheKey(creditType.id), creditType);
			// 更新type_key缓存
			await this.setCachedCreditType(this.getCacheKey(`key:${creditType.type_key}`), creditType);

			// 如果type_key发生变化，删除旧的type_key缓存
			if (originalCreditType && originalCreditType.type_key !== creditType.type_key) {
				await this.deleteCachedCreditType(this.getCacheKey(`key:${originalCreditType.type_key}`));
			}

			await this.clearAllCache(); // 清除all缓存
		}

		return creditType;
	}
}
