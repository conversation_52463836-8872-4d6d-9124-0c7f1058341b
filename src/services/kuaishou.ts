import { Context } from 'hono';
import { User } from '../middleware/auth';
import { CreditsService } from './credits';
import { GetKuaishouVideoDetailSchema, GetKuaishouCommentsSchema, GetKuaishouRepliesSchema } from '../validators/kuaishou';
import { dataToolApiRequest } from '../utils/dataToolHelper';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
};

export class KuaishouService {
	private kv: KVNamespace;
	private loginUser: User;
	private creditsService: CreditsService;

	constructor(c: Context<{ Variables: { user: User }; Bindings: Bindings }>) {
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		this.creditsService = new CreditsService(c);
	}

	/**
	 * 获取快手视频详情
	 */
	async getVideoDetail(data: GetKuaishouVideoDetailSchema) {
		const { url } = data;
		const creditType = 'kuaishou_video_detail_deduct';

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, creditType);

		// 使用URL的hash作为缓存键
		const cacheKey = `kuaishou_detail_${Buffer.from(url).toString('base64')}`;
		const cachedDetail = (await this.kv.get(cacheKey, 'json')) as any;
		if (cachedDetail) {
			await this.deductCreditsForVideoDetail();
			return cachedDetail;
		}

		try {
			// 调用快手详情接口，直接传递完整URL
			const responseData = await dataToolApiRequest('/ks/detail', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ url }),
			});

			// 缓存结果到KV (有效期24小时)
			await this.kv.put(cacheKey, JSON.stringify(responseData), { expirationTtl: 86400 });

			// 扣除用户积分
			await this.deductCreditsForVideoDetail();

			return responseData;
		} catch (error) {
			console.error('获取快手视频详情失败:', error);
			throw error;
		}
	}

	/**
	 * 获取快手评论列表
	 */
	async getComments(data: GetKuaishouCommentsSchema) {
		const { photoId, authorId, pcursor } = data;
		const creditType = 'kuaishou_comments_deduct';

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, creditType);

		// 构建缓存键
		const cacheKey = `kuaishou_comments_${photoId}_${pcursor || 'first'}`;
		const cachedComments = (await this.kv.get(cacheKey, 'json')) as any;
		if (cachedComments) {
			await this.deductCreditsForComments();
			return cachedComments;
		}

		try {
			// 构建API路径
			let apiPath = `/ks/comment/list?photoId=${encodeURIComponent(photoId)}&authorId=${encodeURIComponent(authorId)}`;
			if (pcursor) {
				apiPath += `&pcursor=${encodeURIComponent(pcursor)}`;
			}

			// 调用快手评论接口
			const responseData = await dataToolApiRequest(apiPath);

			// 缓存结果到KV (有效期1小时)
			await this.kv.put(cacheKey, JSON.stringify(responseData), { expirationTtl: 3600 });

			// 扣除用户积分
			await this.deductCreditsForComments();

			return responseData;
		} catch (error) {
			console.error('获取快手评论列表失败:', error);
			throw error;
		}
	}

	/**
	 * 获取快手回复列表
	 */
	async getReplies(data: GetKuaishouRepliesSchema) {
		const { photoId, authorId, rootCommentId, pcursor } = data;
		const creditType = 'kuaishou_replies_deduct';

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, creditType);

		// 构建缓存键
		const cacheKey = `kuaishou_replies_${rootCommentId}_${pcursor || 'first'}`;
		const cachedReplies = (await this.kv.get(cacheKey, 'json')) as any;
		if (cachedReplies) {
			await this.deductCreditsForReplies();
			return cachedReplies;
		}

		try {
			// 构建API路径
			let apiPath = `/ks/reply/list?photoId=${encodeURIComponent(photoId)}&authorId=${encodeURIComponent(
				authorId
			)}&rootCommentId=${encodeURIComponent(rootCommentId)}`;
			if (pcursor) {
				apiPath += `&pcursor=${encodeURIComponent(pcursor)}`;
			}

			// 调用快手回复接口
			const responseData = await dataToolApiRequest(apiPath);

			// 缓存结果到KV (有效期1小时)
			await this.kv.put(cacheKey, JSON.stringify(responseData), { expirationTtl: 3600 });

			// 扣除用户积分
			await this.deductCreditsForReplies();

			return responseData;
		} catch (error) {
			console.error('获取快手回复列表失败:', error);
			throw error;
		}
	}

	/**
	 * 扣除视频详情积分
	 */
	private async deductCreditsForVideoDetail() {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'kuaishou_video_detail_deduct', '获取快手视频详情', this.loginUser.id);
	}

	/**
	 * 扣除评论列表积分
	 */
	private async deductCreditsForComments() {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'kuaishou_comments_deduct', '获取快手评论列表', this.loginUser.id);
	}

	/**
	 * 扣除回复列表积分
	 */
	private async deductCreditsForReplies() {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'kuaishou_replies_deduct', '获取快手回复列表', this.loginUser.id);
	}
}
