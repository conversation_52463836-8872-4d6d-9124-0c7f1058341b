import { D1Database } from '@cloudflare/workers-types';
import { userCreditModel, userCreditRecordModel } from '../models/schema';
import { eq, sql, desc, and } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { ChangeType } from '../const/creditsType';
import { GetUserCreditRecordSchema } from '../validators/user';
import { CreditTypeService } from './creditType';
import { LockService } from './userLock';
import { Context } from 'hono';
import { InsufficientCreditsError } from '../utils/response';

export class CreditsService {
	private readonly db: ReturnType<typeof drizzle>;
	private readonly creditTypeService?: CreditTypeService;
	private readonly userLockService: LockService;
	constructor(private readonly c: Context) {
		this.db = drizzle(c.env.datatool_d1);
		this.creditTypeService = new CreditTypeService(c);
		this.userLockService = new LockService(c);
	}

	async getUserCreditRecord(userId: number, params: GetUserCreditRecordSchema) {
		const userCreditRecord = await this.db
			.select()
			.from(userCreditRecordModel)
			.where(
				and(
					eq(userCreditRecordModel.user_id, userId),
					params.changeType ? eq(userCreditRecordModel.change_type, params.changeType) : undefined
				)
			)
			.orderBy(desc(userCreditRecordModel.id))
			.limit(Number(params.pageSize))
			.offset((Number(params.page) - 1) * Number(params.pageSize));
		return userCreditRecord;
	}

	async getUserCredit(userId: number) {
		const [userCredit] = await this.db.select().from(userCreditModel).where(eq(userCreditModel.user_id, userId));
		return userCredit;
	}

	async addUserCredit(
		userId: number,
		changeType: ChangeType,
		changeReason: string,
		actUserId: number,
		customCredit?: number
	): Promise<typeof userCreditModel.$inferSelect> {
		// 使用分布式锁防止并发操作
		return await this.userLockService.withLock(
			userId.toString(),
			async () => {
				// 获取积分数量
				let credit: number;
				if (changeType === 'stripe_payment_credit' || changeType === 'system_add_credit') {
					if (customCredit === undefined) {
						throw new Error('stripe_payment_credit 和 system_add_credit 需要传入具体积分数量');
					}
					credit = customCredit;
				} else {
					if (!this.creditTypeService) {
						throw new Error('Context 未提供，无法获取积分类型配置');
					}
					const creditType = await this.creditTypeService.getCreditTypeByKey(changeType);
					if (!creditType) {
						throw new Error(`未找到积分类型配置: ${changeType}`);
					}
					if (creditType.operation_type !== 'add') {
						throw new Error(`积分类型 ${changeType} 不支持增加操作`);
					}
					credit = creditType.credit_amount;
				}

				// 先获取当前积分余额
				const currentUserCredit = await this.getUserCredit(userId);
				if (!currentUserCredit) {
					throw new Error('User credit not found');
				}

				const changeBefore = currentUserCredit.credit;
				const changeAfter = changeBefore + credit;

				// 更新积分
				const [userCredit] = await this.db
					.update(userCreditModel)
					.set({
						credit: changeAfter,
					})
					.where(eq(userCreditModel.user_id, userId))
					.returning();

				if (!userCredit) {
					throw new Error('Failed to update user credit');
				}

				// 记录积分变更历史
				await this.db.insert(userCreditRecordModel).values({
					user_id: userId,
					change_before: changeBefore,
					change_after: changeAfter,
					change_credit: credit,
					change_type: changeType,
					act_user_id: actUserId,
					change_reason: changeReason,
					created_at: sql`datetime('now', '+8 hours')`,
					updated_at: sql`datetime('now', '+8 hours')`,
				});

				return userCredit;
			},
			'credit_add_operation'
		);
	}
	// 检查用户积分是否足够（传入具体积分数量）
	async checkUserCredit(userId: number, credit: number) {
		const userCredit = await this.getUserCredit(userId);
		if (!userCredit) {
			throw new InsufficientCreditsError();
		}
		if (userCredit.credit < credit) {
			throw new InsufficientCreditsError();
		}
		return true;
	}

	// 获取积分类型对应的积分数量
	async getCreditAmountByType(changeType: ChangeType): Promise<number> {
		if (!this.creditTypeService) {
			throw new Error('Context 未提供，无法获取积分类型配置');
		}
		const creditType = await this.creditTypeService.getCreditTypeByKey(changeType);
		if (!creditType) {
			throw new Error(`未找到积分类型配置: ${changeType}`);
		}
		return creditType.credit_amount;
	}

	// 检查用户积分是否足够（根据积分类型）
	async checkUserCreditByType(userId: number, changeType: ChangeType) {
		// 获取积分数量
		const credit = await this.getCreditAmountByType(changeType);

		// 验证操作类型
		if (!this.creditTypeService) {
			throw new Error('Context 未提供，无法获取积分类型配置');
		}
		const creditType = await this.creditTypeService.getCreditTypeByKey(changeType);
		if (creditType?.operation_type !== 'deduct') {
			throw new Error(`积分类型 ${changeType} 不支持扣减操作`);
		}

		return this.checkUserCredit(userId, credit);
	}

	async deductUserCredit(
		userId: number,
		changeType: ChangeType,
		changeReason: string,
		actUserId: number
	): Promise<typeof userCreditModel.$inferSelect> {
		// 使用分布式锁防止并发操作
		return await this.userLockService.withLock(
			userId.toString(),
			async () => {
				// 获取积分数量
				if (!this.creditTypeService) {
					throw new Error('Context 未提供，无法获取积分类型配置');
				}
				const creditType = await this.creditTypeService.getCreditTypeByKey(changeType);
				if (!creditType) {
					throw new Error(`未找到积分类型配置: ${changeType}`);
				}
				if (creditType.operation_type !== 'deduct') {
					throw new Error(`积分类型 ${changeType} 不支持扣减操作`);
				}
				const credit = creditType.credit_amount;

				const userCredit = await this.getUserCredit(userId);
				if (!userCredit) {
					throw new InsufficientCreditsError();
				}
				if (userCredit.credit < credit) {
					throw new InsufficientCreditsError();
				}
				const newCredit = userCredit.credit - credit;
				await this.db.insert(userCreditRecordModel).values({
					user_id: userId,
					change_before: userCredit.credit,
					change_after: newCredit,
					change_credit: -credit,
					change_type: changeType,
					act_user_id: actUserId,
					change_reason: changeReason,
					created_at: sql`datetime('now', '+8 hours')`,
					updated_at: sql`datetime('now', '+8 hours')`,
				});
				const [updatedUserCredit] = await this.db
					.update(userCreditModel)
					.set({
						credit: newCredit,
					})
					.where(eq(userCreditModel.user_id, userId))
					.returning();
				if (!updatedUserCredit) {
					throw new Error('Failed to update user credit');
				}
				return updatedUserCredit;
			},
			'credit_deduct_operation'
		);
	}
}
