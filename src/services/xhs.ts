import { Context } from 'hono';
import { GetNoteDetailSchema, GetUserNotesSchema, GetNoteInfoSchema } from '../validators/xhs';
import { User } from '../middleware/auth';
import { CreditsService } from './credits';
import { LockService } from './userLock';
import { dataToolApiRequest } from '../utils/dataToolHelper';
import { JustOneApiHelper } from '../utils/justoneapiHelper';
// changeTypeCredits 已废弃，现在从数据库读取积分配置

export class XhsService {
	private readonly kv: KVNamespace;
	private readonly loginUser: User;
	private readonly creditsService: CreditsService;
	private readonly lockService: LockService;
	constructor(private readonly c: Context) {
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		this.creditsService = new CreditsService(c);
		this.lockService = new LockService(c);
	}

	async getUserNotes(params: GetUserNotesSchema) {
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'xhs_user_note_deduct');
		const data = await dataToolApiRequest(`/fox/xhs/user/notes/v4?userId=${params.userId}&lastCursor=${params.lastCursor}&token=1ZZMyJWC`)
		// @ts-ignore
		if (data.code === 0) {
			await this.creditsService.deductUserCredit(this.loginUser.id, 'xhs_user_note_deduct', '搜索用户笔记', this.loginUser.id);
			// 更新token值到data中
			let notIdArr = [];
			// @ts-ignore
			for (const item of data.data.notes) {
				notIdArr.push(item.id);
			}
			const tokenData = await this.getXhsToken(notIdArr);
			// @ts-ignore
			for (const item of data.data.notes) {
				// @ts-ignore
				const token = tokenData.data[item.id] ?? '';
				const url = `https://www.xiaohongshu.com/explore/${item.id}?xsec_token=${token}`;
				item.xsec_token = token;
				item.share_url = url;
			}
		}

		return data;
	}

	async getXhsToken(noteIds: string[], refresh = false): Promise<{ code: number; data?: Record<string, string> }> {
		// 首先获取广告SSO cookie
		const adSso = await this.getAdSso(refresh);

		const headers = {
			accept: 'application/json, text/plain, */*',
			'accept-language': 'zh-CN,zh;q=0.9',
			'cache-control': 'no-cache',
			'content-type': 'application/json',
			origin: 'https://ad.xiaohongshu.com',
			pragma: 'no-cache',
			priority: 'u=1, i',
			cookie: adSso,
			referer:
				'https://ad.xiaohongshu.com/microapp/unite/%E5%B0%8F%E7%BA%A2%E4%B9%A6%E8%81%9A%E5%85%89-%E4%B8%80%E7%AB%99%E5%BC%8F%E5%B9%BF%E5%91%8A%E6%8A%95%E6%94%BE%E5%B9%B3%E5%8F%B0',
			'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
			'sec-ch-ua-mobile': '?0',
			'sec-ch-ua-platform': '"Windows"',
			'sec-fetch-dest': 'empty',
			'sec-fetch-mode': 'cors',
			'sec-fetch-site': 'same-origin',
			'user-agent':
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
			'x-b3-traceid': `28izohj7${Math.floor(Math.random() * 9900000) + 1000}`,
		};

		const jsonData = {
			noteIds,
			source: 'pc_ad',
		};

		const response = await fetch('https://ad.xiaohongshu.com/api/light/note/token', {
			method: 'POST',
			headers,
			body: JSON.stringify(jsonData),
		});

		const data = await response.json();
		// @ts-ignore
		if (data.code !== 0 && refresh == false) {
			return await this.getXhsToken(noteIds, true);
		}
		//@ts-ignore
		return data;
	}

	async getAdSso(refresh = false) {
		if (!refresh) {
			const adSso = await this.kv.get('xhs_ad_sso');
			if (adSso) {
				return adSso;
			}
		}
		const resp = await fetch('https://api.petslib.cn/plugin/index/ad/sso', {
			headers: {
				'content-type': 'application/json',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
			},
		});
		const data = await resp.json();
		// @ts-ignore
		await this.kv.put('xhs_ad_sso', data.data);
		// @ts-ignore
		return data.data;
	}
	/**
	 * 商业数据 - xhs
	 * @param form
	 */
	async getNoteBusinessData(form: GetNoteDetailSchema) {
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'xhs_note_business_data');
		if (!form.noteId) {
			const extractedId = this.extractNoteId(form.url!);
			form.noteId = extractedId ?? undefined;
		}
		if (!form.noteId) {
			throw new Error('无效的笔记链接');
		}
		const noteAdDetail = await this.getAdNoteDetail(form);
		if (!noteAdDetail.data.indexes) {
			return [];
		}
		let impNum = 0;
		let readNum = 0;
		for (const item of noteAdDetail.data.indexes) {
			if (item.name == '总曝光量') {
				impNum = item.value;
			}
			if (item.name == '总阅读量') {
				readNum = item.value;
			}
		}
		const notePgyDetail = await this.getPgyNoteDetail(form);
		await this.creditsService.deductUserCredit(this.loginUser.id, 'xhs_note_business_data', '提取笔记商业数据', this.loginUser.id);
		return {
			noteId: form.noteId,
			impNum: Math.max(impNum, notePgyDetail.data.impNum),
			readNum: Math.max(readNum, notePgyDetail.data.readNum),
			followCnt: notePgyDetail.data.followCnt,
			reportBrandName: notePgyDetail.data.reportBrandName,
			fansNum: notePgyDetail.data.userInfo.fansNum,
			videoPrice: notePgyDetail.data.userInfo.videoPrice,
			userId: notePgyDetail.data.userId,
			picturePrice: notePgyDetail.data.userInfo.picturePrice,
		};
	}

	async getAdNoteDetail(form: GetNoteDetailSchema) {
		// 从URL中提取笔记ID
		if (!form.noteId) {
			const extractedId = this.extractNoteId(form.url!);
			form.noteId = extractedId ?? undefined;
		}
		if (!form.noteId) {
			throw new Error('无效的笔记链接');
		}
		// 尝试从KV中获取缓存的笔记详情
		const cachedDetail = await this.kv.get(`xhs_note_ad_detail_${form.noteId}`, 'json');
		if (cachedDetail) {
			return cachedDetail;
		}

		try {
			// 获取广告SSO
			const adSSO = await this.getAdSSO();

			// 获取笔记详情
			const noteDetail = await this.fetchAdNoteDetail(form.noteId, adSSO);

			// 缓存结果到KV (有效期24小时)
			if (noteDetail.code !== 0) {
				throw new Error(noteDetail.msg ? noteDetail.msg : '获取笔记详情失败');
			}
			await this.kv.put(`xhs_note_ad_detail_${form.noteId}`, JSON.stringify(noteDetail), { expirationTtl: 86400 });
			// 扣除用户积分
			// await this.creditsService.deductUserCredit(
			// 	this.loginUser.id,
			// 	'xhs_note_detail_deduct',
			// 	'获取小红书笔记详情',
			// 	this.loginUser.id
			// );

			return noteDetail;
		} catch (error) {
			console.error('获取笔记详情失败:', error);
			throw error;
		}
	}

	async getPgyNoteDetail(form: GetNoteDetailSchema) {
		// 从URL中提取笔记ID
		if (!form.noteId) {
			const extractedId = this.extractNoteId(form.url!);
			form.noteId = extractedId ?? undefined;
		}
		if (!form.noteId) {
			throw new Error('无效的笔记链接');
		}

		// 尝试从KV中获取缓存的笔记详情
		const cachedDetail = await this.kv.get(`xhs_note_pgy_detail_${form.noteId}`, 'json');
		if (cachedDetail) {
			return cachedDetail;
		}

		try {
			// 获取蒲公英SSO
			const pgySSO = await this.getPgySSO();

			// 获取笔记详情
			const noteDetail = await this.fetchPgyNoteDetail(form.noteId, pgySSO);
			if (noteDetail.code !== 0) {
				throw new Error(noteDetail.msg ? noteDetail.msg : '获取笔记详情失败');
			}
			// 缓存结果到KV (有效期24小时)
			await this.kv.put(`xhs_note_pgy_detail_${form.noteId}`, JSON.stringify(noteDetail), { expirationTtl: 86400 });

			// 扣除用户积分
			// await this.creditsService.deductUserCredit(
			// 	this.loginUser.id,
			// 	'xhs_note_detail_deduct',
			// 	'获取小红书笔记详情',
			// 	this.loginUser.id
			// );

			return noteDetail;
		} catch (error) {
			console.error('获取笔记详情失败:', error);
			throw error;
		}
	}

	private extractNoteId(url: string): string | null {
		// 从URL中提取笔记ID的逻辑
		const regex = /explore\/([a-zA-Z0-9]+)|discovery\/item\/([a-zA-Z0-9]+)/;
		const match = url.match(regex);
		if (match) {
			return match[1] || match[2];
		}
		return null;
	}

	private async getAdSSO(refresh = false): Promise<string> {
		// 尝试从KV中获取缓存的SSO
		if (!refresh) {
			const sso = await this.kv.get('xhs_ad_sso');
			if (sso) {
				return sso;
			}
		}

		// 获取新的SSO
		const resp = await fetch('https://api.petslib.cn/plugin/index/ad/sso', {
			headers: {
				'content-type': 'application/json',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			},
		});

		const data = await resp.json();
		//@ts-ignore
		if (!data.data) {
			throw new Error('获取AD-SSO失败');
		}

		// 缓存SSO到KV (有效期2小时)
		//@ts-ignore
		await this.kv.put('xhs_ad_sso', data.data, { expirationTtl: 7200 });
		//@ts-ignore
		return data.data;
	}

	private async getPgySSO(refresh = false): Promise<string> {
		// 尝试从KV中获取缓存的SSO
		if (!refresh) {
			const sso = await this.kv.get('xhs_pgy_sso');
			if (sso) {
				return sso;
			}
		}

		// 获取新的SSO
		const resp = await fetch('https://api.petslib.cn/plugin/index/xhs/sso', {
			headers: {
				'content-type': 'application/json',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			},
		});

		const data = await resp.json();
		//@ts-ignore
		if (!data.data) {
			throw new Error('获取蒲公英SSO失败');
		}

		// 缓存SSO到KV (有效期2小时)
		//@ts-ignore
		await this.kv.put('xhs_pgy_sso', data.data, { expirationTtl: 7200 });
		//@ts-ignore
		return data.data;
	}

	private async fetchPgyNoteDetail(noteId: string, sso: string): Promise<any> {
		// 使用锁防止并发请求，锁ID使用笔记ID，锁键值使用'pgy_note_detail_fetch'
		return this.lockService.withLock<any>(
			`pgy-note`,
			async () => {
				const headers = {
					'sec-fetch-dest': 'empty',
					'sec-fetch-mode': 'cors',
					'sec-fetch-site': 'same-origin',
					'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
					referer: 'https://pgy.xiaohongshu.com/',
					cookie: sso,
				};

				const response = await fetch(`https://pgy.xiaohongshu.com/api/solar/note/${noteId}/detail`, {
					method: 'GET',
					headers,
				});

				const data = await response.json();
				return data;
			},
			'pgy_note_detail_fetch',
			30000 // 30秒超时
		);
	}

	private async fetchAdNoteDetail(noteId: string, sso: string): Promise<any> {
		// 使用锁防止并发请求，锁ID使用笔记ID，锁键值使用'ad_note_detail_fetch'
		return this.lockService.withLock<any>(
			`ad-note`,
			async () => {
				const headers = {
					accept: 'application/json, text/plain, */*',
					'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
					'cache-control': 'no-cache',
					'content-type': 'application/json;charset=UTF-8',
					origin: 'https://ad.xiaohongshu.com',
					pragma: 'no-cache',
					priority: 'u=1, i',
					'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
					'sec-ch-ua-mobile': '?0',
					'sec-ch-ua-platform': '"Windows"',
					'sec-fetch-dest': 'empty',
					'sec-fetch-mode': 'cors',
					'sec-fetch-site': 'same-origin',
					'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
					cookie: sso,
				};

				const response = await fetch('https://ad.xiaohongshu.com/api/leona/spotlight_data/auroraDataNoteInspiration/noteDetail', {
					method: 'POST',
					body: JSON.stringify({
						noteId: noteId,
					}),
					headers,
				});

				const data = await response.json();
				return data;
			},
			'ad_note_detail_fetch',
			30000 // 30秒超时
		);
	}
	/**
	 * 获取小红书笔记详情信息 - 使用 JustOneAPI
	 * @param form 包含笔记URL的表单数据
	 * @returns Promise<any> 笔记详情信息
	 */
	async getNoteInfo(form: GetNoteInfoSchema) {
		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'xhs_note_detail_deduct');

		// 从URL中提取笔记ID
		const noteId = await JustOneApiHelper.extractNoteIdFromUrl(form.url);
		if (!noteId) {
			throw new Error('无效的小红书笔记链接，无法提取笔记ID');
		}

		// 尝试从KV中获取缓存的笔记详情
		const cacheKey = `xhs_note_info_${noteId}`;
		const cachedDetail = await this.kv.get(cacheKey, 'json');
		if (cachedDetail) {
			return cachedDetail;
		}

		try {
			

			// 调用JustOneAPI获取笔记详情
			const noteDetail = await JustOneApiHelper.getXhsNoteDetail(
				noteId,
				30000 // 30秒超时
			);

			if (noteDetail.code !== 0) {
				throw new Error(noteDetail.message || '获取笔记详情失败');
			}

			// 缓存结果到KV (有效期24小时)
			await this.kv.put(cacheKey, JSON.stringify(noteDetail), { expirationTtl: 86400 });

			// 扣除用户积分
			await this.creditsService.deductUserCredit(
				this.loginUser.id,
				'xhs_note_detail_deduct',
				'获取小红书笔记详情信息',
				this.loginUser.id
			);

			return noteDetail;
		} catch (error) {
			console.error('获取笔记详情失败:', error);
			throw error;
		}
	}
}
