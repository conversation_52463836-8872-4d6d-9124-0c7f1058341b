import { userCreditModel, userCreditRecordModel, userModel } from '../models/schema';
import { and, desc, eq, like, sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { User } from '../middleware/auth';
import { GetUserListSchema, UpdateUserCreditSchema, UpdateUserRemarkSchema, UpdateUserStatusSchema } from '../validators/admin';
import { Context } from 'hono';
import { CreateUserSchema } from '../validators/user';
import { AuthService } from './auth';
import { generateApiKey, md5 } from '../utils/strHelper';

export class AdminService {
	private readonly db: ReturnType<typeof drizzle>;
	private readonly loginUser: User;

	constructor(private readonly c: Context) {
		this.db = drizzle(c.env.datatool_d1);
		this.loginUser = c.get('user');
		if (this.loginUser?.role !== 'admin') {
			throw new Error('Permission denied');
		}
	}

	async updateUserStatus(data: UpdateUserStatusSchema) {
		const { id, status } = data;
		await this.db.update(userModel).set({ status }).where(eq(userModel.id, id));
	}

	async updateUserRemark(data: UpdateUserRemarkSchema) {
		const { id, remark } = data;
		await this.db.update(userModel).set({ remark }).where(eq(userModel.id, id));
	}

	async createUser(user: CreateUserSchema): Promise<{
		id: number;
		username: string | null;
		role: string;
		status: number;
		created_at: number;
		last_login_at: string | null;
		credit: number | null;
	}> {
		// 判断用户名是否存在
		const exists = await new AuthService(this.c).getUserByUsername(user.username);
		if (exists) {
			throw new Error('User already exists');
		}

		const [newUser] = await this.db
			.insert(userModel)
			.values({
				username: user.username,
				password: md5(user.password),
				role: user.role ?? 'user',
				api_key: generateApiKey(),
				created_at: sql`datetime('now', '+8 hours')`,
				updated_at: sql`datetime('now', '+8 hours')`,
			})
			.returning();

		if (!newUser) {
			throw new Error('Failed to create user');
		}
		await this.db.insert(userCreditModel).values({
			user_id: newUser.id,
			credit: 0,
		});

		return {
			id: newUser.id,
			username: newUser.username,
			role: newUser.role,
			status: newUser.status,
			created_at: newUser.created_at,
			last_login_at: newUser.last_login_at,
			credit: 0,
		};
	}

	async updateUserCredit(data: UpdateUserCreditSchema) {
		const { id, changeType, changeCredit } = data;
		const userCredit = await this.db.select().from(userCreditModel).where(eq(userCreditModel.user_id, id));
		const currentCredit = userCredit[0]?.credit ?? 0;
		const newCredit = changeType.includes('deduct') ? currentCredit - changeCredit : currentCredit + changeCredit;
		let changeReason = data.changeReason;
		return await this.db.batch([
			this.db.update(userCreditModel).set({ credit: newCredit }).where(eq(userCreditModel.user_id, id)),
			this.db.insert(userCreditRecordModel).values({
				user_id: id,
				change_type: changeType,
				change_credit: changeCredit,
				change_reason: changeReason,
				act_user_id: this.loginUser?.id ?? 0,
				change_before: currentCredit,
				change_after: newCredit,
			}),
		]);
	}

	async getUserList(data: GetUserListSchema): Promise<{
		users: {
			id: number;
			api_key: string;
			username: string | null;
			role: string;
			status: number;
			created_at: number;
			last_login_at: string | null;
			credit: number | null;
			remark: string | null;
		}[];
		total: number;
	}> {
		const users = await this.db
			.select({
				id: userModel.id,
				username: userModel.username,
				api_key: userModel.api_key,
				role: userModel.role,
				status: userModel.status,
				created_at: userModel.created_at,
				last_login_at: userModel.last_login_at,
				credit: userCreditModel.credit,
				remark: userModel.remark,
			})
			.from(userModel)
			.leftJoin(userCreditModel, eq(userModel.id, userCreditModel.user_id))
			.where(and(like(userModel.username, `%${data.username ?? ''}%`), eq(userModel.status, data.status ?? 1)))
			.limit(data.pageSize)
			.offset((data.page - 1) * data.pageSize)
			.orderBy(desc(userModel.id));

		const totalResult = await this.db
			.select({
				count: sql<number>`count(*)`,
			})
			.from(userModel)
			.where(and(like(userModel.username, `%${data.username ?? ''}%`), eq(userModel.status, data.status ?? 1)));

		const total = totalResult[0]?.count ?? 0;
		return {
			users: users,
			total: total,
		};
	}
}