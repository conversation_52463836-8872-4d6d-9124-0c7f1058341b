import { Context } from 'hono';
import { TaskCreateSchema, TaskQuerySchema, TaskUpdateSchema } from '../validators/task';
import { drizzle } from 'drizzle-orm/d1';
import { taskModel } from '../models/schema';
import { v4 as uuidv4 } from 'uuid';
import { eq, inArray } from 'drizzle-orm/sql';
import dayjs from 'dayjs';

export class TaskService {
	private readonly kv: KVNamespace;
	private readonly db: ReturnType<typeof drizzle>;

	constructor(private readonly c: Context) {
		this.db = drizzle(c.env.datatool_d1);
		this.kv = c.env.datatool_kv;
	}

	async create(params: TaskCreateSchema) {
		const [newTask] = await this.db
			.insert(taskModel)
			.values({
				task_id: uuidv4(),
				task_type: params.task_type,
				language: params.language,
				media_url: params.media_url,
				proxy_media_url: params.proxy_media_url,
				model: params.model,
				country_code: params.country_code,
				extra: params.extra ? JSON.stringify(params.extra) : "",
			})
			.returning();
		if (params.message) {
			const msg = JSON.parse(params.message)
			const body = JSON.parse(JSON.stringify(newTask));
			body.callback_url = params.callback_url
			msg['http_request']['body'] = JSON.stringify(body)
			//推送Queue
			const messageObj = {
				id: crypto.randomUUID(),
				content: JSON.stringify(msg),
				timestamp: Date.now(),
				user: this.c.get('user').username,
			};

			// 发送消息到队列
			await this.c.env.QUEUE.send(messageObj);
		}
		return newTask;
	}

	async update(params: TaskUpdateSchema) {
		const task = await this.info(params.task_id);
		if (!task) {
			throw new Error('task not found');
		}
		const [updatedTask] = await this.db
			.update(taskModel)
			.set({
				status: params.status,
				percent: params.percent,
				result: params.result,
				error: params.error,
				sub_status: params.sub_status ? params.sub_status : task.sub_status,
				object_key: params.object_key,
				updated_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
			})
			.where(eq(taskModel.task_id, params.task_id))
			.returning();
		return updatedTask;
	}

	async info(taskId: string) {
		const task = await this.db.select().from(taskModel).where(eq(taskModel.task_id, taskId)).get();
		if (task && task.result) {
			task.result = JSON.parse(task.result);
		}
		return task;
	}

	async query(params: TaskQuerySchema) {
		const tasks = await this.db
			.select()
			.from(taskModel)
			.where(params.task_ids ? inArray(taskModel.task_id, params.task_ids) : undefined)
			.all();
		for (const task of tasks) {
			// 状态是 pending  currentTime - create >= 20分钟 标记失败
			if (task.status === 'pending' || task.status === 'processing') {
				const createTime = dayjs(task.created_at);
				const currentTime = dayjs();
				if (currentTime.diff(createTime, 'minute') >= 20) {
					task.status = 'failed';
					task.error = 'timeout';
					await this.update({
						task_id: task.task_id,
						status: 'failed',
						sub_status: 'failed',
						error: 'timeout',
						percent: task.percent,
						result: '',
					});
				}
			}
			if (task.result) {
				task.result = JSON.parse(task.result);
			}
		}
		return tasks;
	}

	async delete(taskId: string) {
		const task = await this.info(taskId);
		if (!task) {
			throw new Error('task not found');
		}
		await this.db.delete(taskModel).where(eq(taskModel.task_id, taskId));
		if (task.object_key) {
			//删除r2桶的数据
			await this.c.env.datatool.delete(task.object_key);
		}
		return task;
	}
}
