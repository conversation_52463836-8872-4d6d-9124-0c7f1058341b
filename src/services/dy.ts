import { Context } from 'hono';
import { User } from '../middleware/auth';
import { CreditsService } from './credits';
import { GetXtVideoInfoSchema, GetDouyinUserVideosSchema, GetDouyinVideoCommentsSchema, GetDouyinCommentRepliesSchema } from '../validators/dy';
import { formatDuration, formatTimestamp } from '../utils/strHelper';
import { dataToolApiRequest } from '../utils/dataToolHelper';

// 定义视频信息的接口
interface VideoInfoResponse {
	video_info?: {
		duration?: number;
		create_time?: string | number;
		formatted_duration?: string;
		formatted_create_time?: string;
		[key: string]: any;
	};
	[key: string]: any;
}

export class DyService {
	private readonly kv: KVNamespace;
	private readonly loginUser: User;
	private readonly creditsService: CreditsService;
	constructor(private readonly c: Context) {
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		this.creditsService = new CreditsService(c);
	}

	async getDyVideoInfo(data: GetXtVideoInfoSchema) {
		const { url } = data;
		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'dy_video_detail_deduct');

		// 从URL中提取aweme_id
		const awemeId = await this.extractVideoId(url);
		if (!awemeId) {
			throw new Error('无效的抖音视频链接');
		}

		// 尝试从KV中获取缓存的视频详情
		const cachedDetail = await this.kv.get(`dy_detail_${awemeId}`, 'json') as any;
		if (cachedDetail) {
			await this.deductCreditsForVideoDetail();
			return cachedDetail;
		}

		try {
			// 调用抖音详情接口
			const dyVideo = await dataToolApiRequest(`/douyin/detail?aweme_id=${awemeId}`);
			// 缓存结果到KV (有效期24小时)
			await this.kv.put(`dy_detail_${awemeId}`, JSON.stringify(dyVideo), { expirationTtl: 86400 });

			// 扣除用户积分
			await this.deductCreditsForVideoDetail();

			return dyVideo;
		} catch (error) {
			console.error('获取抖音视频详情失败:', error);
			throw error;
		}
	}

	async getVideoInfo(data: GetXtVideoInfoSchema) {
		const { url } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'dy_xt_video_deduct');

		// 从URL中提取视频ID
		const videoId = await this.extractVideoId(url);
		if (!videoId) {
			throw new Error('无效的视频链接');
		}

		// 尝试从KV中获取缓存的视频信息
		const cachedInfo = (await this.kv.get(`dy_video_${videoId}`, 'json')) as VideoInfoResponse;
		if (cachedInfo) {
			this.validateVideoResponse(cachedInfo, data.url);
			if (cachedInfo.video_info) {
				this.formatVideoTimeFields(cachedInfo.video_info);
				await this.deductCreditsForVideoInfo();
			}
			return cachedInfo;
		}

		try {
			// 获取星图SSO
			const xtSso = await this.getXtSso();

			// 获取视频信息
			const videoInfo = await this.fetchVideoInfo(videoId, xtSso);
			this.validateVideoResponse(videoInfo, data.url);

			if (videoInfo && videoInfo.video_info) {
				this.formatVideoTimeFields(videoInfo.video_info);
			}

			// 缓存结果到KV (有效期24小时)
			await this.kv.put(`dy_video_${videoId}`, JSON.stringify(videoInfo), { expirationTtl: 86400 });

			// 扣除用户积分
			await this.deductCreditsForVideoInfo();

			return videoInfo;
		} catch (error) {
			console.error('获取视频信息失败:', error);
			throw error;
		}
	}

	/**
	 * 验证视频响应数据
	 */
	private validateVideoResponse(videoResponse: VideoInfoResponse, url: string): void {
		const baseResp = videoResponse.base_resp;
		if (baseResp.status_code != 0) {
			console.info(`url: ${url} baseResp: ${JSON.stringify(baseResp)}`);
			throw new Error('获取失败，请联系客服');
		}
		if (!videoResponse.video_info) {
			throw new Error('请检查链接，视频不存在');
		}
	}

	/**
	 * 格式化视频时间相关字段
	 */
	private formatVideoTimeFields(videoInfo: VideoInfoResponse['video_info']): void {
		if (!videoInfo) return;

		// 格式化视频时长（如果没有格式化过）
		if (videoInfo.duration && !videoInfo.formatted_duration) {
			videoInfo.formatted_duration = formatDuration(videoInfo.duration);
		}

		// 格式化创建时间（如果没有格式化过）
		if (videoInfo.create_time && !videoInfo.formatted_create_time) {
			videoInfo.formatted_create_time = formatTimestamp(videoInfo.create_time);
		}
		if (!videoInfo.finish_rate) {
			videoInfo.finish_rate = '';
		}
	}

	/**
	 * 扣除视频信息查询积分
	 */
	private async deductCreditsForVideoInfo(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'dy_xt_video_deduct',
			'获取抖音视频信息',
			this.loginUser.id
		);
	}

	/**
	 * 扣除视频详情查询积分
	 */
	private async deductCreditsForVideoDetail(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'dy_video_detail_deduct',
			'获取抖音视频详情',
			this.loginUser.id
		);
	}

	private async extractVideoId(url: string): Promise<string | null> {
		// 从URL中提取视频ID的逻辑，支持视频、图片、笔记等多种内容类型
		const regex = /modal_id=([0-9]+)|vid=([0-9]+)|video\/([0-9]+)|note\/([0-9]+)|photo\/([0-9]+)/;
		const match = url.match(regex);
		if (match) {
			return match[1] || match[2] || match[3] || match[4] || match[5];
		}
		if (url.includes('v.douyin.com')) {
			// 如果是短链，解析获取真实URL
			try {
				const realUrl = await this.parseShortUrl(url);
				const realMatch = realUrl.match(regex);
				if (realMatch) {
					return realMatch[1] || realMatch[2] || realMatch[3] || realMatch[4] || realMatch[5];
				}
			} catch (error) {
				console.error('解析短链失败:', error);
			}
		}
		return null;
	}

	private async parseShortUrl(url: string): Promise<string> {
		try {
			const response = await fetch(url, {
				method: 'GET',
				headers: {
					Accept:
						'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
					'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
					'Cache-Control': 'no-cache',
					Pragma: 'no-cache',
					'Upgrade-Insecure-Requests': '1',
					'Accept-Encoding': 'gzip, deflate',
					'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
				},
				redirect: 'follow',
			});
			return response.url;
		} catch (error) {
			throw new Error(`无效的视频链接: ${error}`);
		}
	}

	private async getXtSso(refresh = false): Promise<string> {
		// 尝试从KV中获取缓存的SSO
		if (!refresh) {
			const sso = await this.kv.get('xt_sso');
			if (sso) {
				return sso;
			}
		}

		// 获取新的SSO
		const resp = await fetch('https://api.petslib.cn/plugin/index/xt/sso', {
			headers: {
				'content-type': 'application/json',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
			},
		});

		const data = await resp.json();
		//@ts-ignore
		if (!data.data) {
			throw new Error('获取星图SSO失败');
		}

		// 缓存SSO到KV (有效期2小时)
		//@ts-ignore
		await this.kv.put('xt_sso', data.data, { expirationTtl: 7200 });
		//@ts-ignore
		return data.data;
	}

	private async fetchVideoInfo(videoId: string, sso: string): Promise<VideoInfoResponse> {
		const headers = {
			accept: 'application/json, text/plain, */*',
			'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
			'agw-js-conv': 'str',
			'content-type': 'application/json',
			origin: 'https://www.xingtu.cn',
			priority: 'u=1, i',
			referer: `https://www.xingtu.cn/ad/creator/insight/hot-video-detail/${videoId}`,
			'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
			'sec-ch-ua-mobile': '?0',
			cookie: sso,
			'sec-ch-ua-platform': '"Windows"',
			'sec-fetch-dest': 'empty',
			'sec-fetch-mode': 'cors',
			'sec-fetch-site': 'same-origin',
			'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			'x-login-source': '1',
		};

		const response = await fetch('https://www.xingtu.cn/gw/api/data_sp/item_report_detail', {
			method: 'POST',
			headers,
			body: JSON.stringify({
				item_id: videoId,
			}),
		});

		const data = (await response.json()) as VideoInfoResponse;

		// 格式化视频信息中的时间相关字段
		if (data && data.video_info) {
			this.formatVideoTimeFields(data.video_info);
		}

		return data;
	}

	async getUserVideos(data: GetDouyinUserVideosSchema) {
		const { url, maxCursor } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'dy_user_videos_deduct');

		// 从URL中提取用户ID或sec_uid
		const userId = await this.extractUserIdFromUrl(url);
		if (!userId) {
			throw new Error('无效的抖音用户链接');
		}

		// 构建缓存键
		const cacheKey = `dy_user_videos_${userId}_${maxCursor || 'first'}`;
		const cachedVideos = await this.kv.get(cacheKey, 'json') as any;
		if (cachedVideos) {
			await this.deductCreditsForUserVideos();
			return cachedVideos;
		}

		try {
			// 调用抖音用户视频接口
			let apiUrl = `/douyin/user/videos?sec_user_id=${encodeURIComponent(userId)}`;
			if (maxCursor) {
				const sessionId = await this.kv.get('dy_sessionid') ?? '103f53b7ce2e45c99eed48311e37f965';	
				apiUrl += `&max_cursor=${maxCursor}&sessionid=${sessionId}`;
			}
			const response = await dataToolApiRequest(apiUrl);

			// 缓存结果到KV (有效期1小时)
			await this.kv.put(cacheKey, JSON.stringify(response), { expirationTtl: 3600 });

			// 扣除用户积分
			await this.deductCreditsForUserVideos();

			return response;
		} catch (error) {
			console.error('获取抖音用户视频失败:', error);
			throw error;
		}
	}

	private async extractUserIdFromUrl(url: string): Promise<string | null> {
		// 从URL中提取用户ID的逻辑
		const patterns = [
			/user\/([^/?]+)/,
			/profile\/([^/?]+)/,
			/sec_uid=([^&]+)/,
			/uid=([^&]+)/,
			/\/([A-Za-z0-9_-]+)$/
		];

		for (const pattern of patterns) {
			const match = url.match(pattern);
			if (match) {
				return match[1];
			}
		}

		// 如果是短链，解析获取真实URL
		if (url.includes('v.douyin.com')) {
			try {
				const realUrl = await this.parseShortUrl(url);
				for (const pattern of patterns) {
					const match = realUrl.match(pattern);
					if (match) {
						return match[1];
					}
				}
			} catch (error) {
				console.error('解析短链失败:', error);
			}
		}

		return null;
	}

	private async deductCreditsForUserVideos() {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'dy_user_videos_deduct', '获取抖音用户视频', this.loginUser.id);
	}

	async getVideoComments(data: GetDouyinVideoCommentsSchema) {
		const { url, video_id, cursor } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'dy_video_comments_deduct');

		// 从URL中提取aweme_id，如果提供了video_id则优先使用
		let awemeId: string | null = video_id || null;
		if (!awemeId) {
			awemeId = await this.extractVideoId(url);
		}

		if (!awemeId) {
			throw new Error('无效的抖音视频链接');
		}

		// 构建缓存键，包含cursor信息
		const cacheKey = `dy_comments_${awemeId}_${cursor || 'first'}`;
		const cachedComments = await this.kv.get(cacheKey, 'json') as any;
		if (cachedComments) {
			await this.deductCreditsForVideoComments();
			return cachedComments;
		}

		try {
			// 调用抖音评论接口，支持分页
			let apiUrl = `/douyin/comment/list?aweme_id=${awemeId}`;
			if (cursor) {
				apiUrl += `&cursor=${cursor}`;
			}
			console.info(apiUrl)
			const response = await dataToolApiRequest(apiUrl);

			// 缓存结果到KV (有效期30分钟)
			await this.kv.put(cacheKey, JSON.stringify(response), { expirationTtl: 1800 });

			// 扣除用户积分
			await this.deductCreditsForVideoComments();

			return response;
		} catch (error) {
			console.error('获取抖音视频评论失败:', error);
			throw error;
		}
	}

	private async deductCreditsForVideoComments() {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'dy_video_comments_deduct', '获取抖音视频评论', this.loginUser.id);
	}

	async getCommentReplies(data: GetDouyinCommentRepliesSchema) {
		const { aweme_id, comment_id, cursor } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'dy_comment_replies_deduct');

		// 构建缓存键，包含cursor信息
		const cacheKey = `dy_replies_${aweme_id}_${comment_id}_${cursor}`;
		const cachedReplies = await this.kv.get(cacheKey, 'json') as any;
		if (cachedReplies) {
			await this.deductCreditsForCommentReplies();
			return cachedReplies;
		}

		try {
			// 调用抖音回复接口
			const apiUrl = `/douyin/reply/list?aweme_id=${aweme_id}&comment_id=${comment_id}&cursor=${cursor}`;
			console.info(apiUrl);
			const response = await dataToolApiRequest(apiUrl);

			// 缓存结果到KV (有效期30分钟)
			await this.kv.put(cacheKey, JSON.stringify(response), { expirationTtl: 1800 });

			// 扣除用户积分
			await this.deductCreditsForCommentReplies();

			return response;
		} catch (error) {
			console.error('获取抖音评论回复失败:', error);
			throw error;
		}
	}

	private async deductCreditsForCommentReplies() {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'dy_comment_replies_deduct', '获取抖音评论回复', this.loginUser.id);
	}
}
