import { Context } from 'hono';

/**
 * LockService - 通用锁服务
 * 提供基于Durable Objects的分布式锁功能
 */
export class LockService {
  private lockNamespace: DurableObjectNamespace;

  constructor(private readonly c: Context) {
    this.lockNamespace = c.env.USER_LOCK;
  }

  /**
   * 获取锁的Durable Object实例
   * @param lockId 锁标识符（可以是用户ID、资源ID等）
   * @returns Durable Object Stub
   */
  private getLockStub(lockId: string) {
    const id = this.lockNamespace.idFromName(`lock:${lockId}`);
    return this.lockNamespace.get(id);
  }

  /**
   * 获取锁
   * @param lockId 锁标识符（可以是用户ID、资源ID等）
   * @param lockKey 锁的键值（如：'credit_operation'、'note_detail_fetch'）
   * @param timeoutMs 超时时间（毫秒），默认30秒
   * @returns Promise<boolean> 是否成功获取锁
   */
  async acquireLock(lockId: string, lockKey: string = 'default_operation', timeoutMs: number = 30000): Promise<boolean> {
    try {
      const stub = this.getLockStub(lockId);
      const response = await stub.fetch(`https://user-lock/acquire?lockKey=${encodeURIComponent(lockKey)}&timeout=${timeoutMs}`);
      const result = await response.json() as { success: boolean };
      return result.success;
    } catch (error) {
      console.error('Failed to acquire lock:', error);
      return false;
    }
  }

  /**
   * 释放锁
   * @param lockId 锁标识符
   * @param lockKey 锁的键值
   * @returns Promise<boolean> 是否成功释放锁
   */
  async releaseLock(lockId: string, lockKey: string = 'default_operation'): Promise<boolean> {
    try {
      const stub = this.getLockStub(lockId);
      const response = await stub.fetch(`https://user-lock/release?lockKey=${encodeURIComponent(lockKey)}`);
      const result = await response.json() as { success: boolean };
      return result.success;
    } catch (error) {
      console.error('Failed to release lock:', error);
      return false;
    }
  }

  /**
   * 使用锁执行操作
   * @param lockId 锁标识符
   * @param operation 要执行的操作
   * @param lockKey 锁的键值
   * @param timeoutMs 超时时间（毫秒）
   * @returns Promise<T> 操作结果
   */
  async withLock<T>(
    lockId: string, 
    operation: () => Promise<T>, 
    lockKey: string = 'default_operation', 
    timeoutMs: number = 30000
  ): Promise<T> {
    const acquired = await this.acquireLock(lockId, lockKey, timeoutMs);
    
    if (!acquired) {
      throw new Error('Failed to acquire lock: timeout or conflict');
    }

    try {
      return await operation();
    } finally {
      await this.releaseLock(lockId, lockKey);
    }
  }

  /**
   * 为了向后兼容，保留原有的用户锁方法
   */
  async acquireUserLock(userId: string, lockKey: string = 'credit_operation', timeoutMs: number = 30000): Promise<boolean> {
    return this.acquireLock(`user:${userId}`, lockKey, timeoutMs);
  }

  async releaseUserLock(userId: string, lockKey: string = 'credit_operation'): Promise<boolean> {
    return this.releaseLock(`user:${userId}`, lockKey);
  }

  async withUserLock<T>(
     userId: string, 
     operation: () => Promise<T>, 
     lockKey: string = 'credit_operation', 
     timeoutMs: number = 30000
   ): Promise<T> {
     return this.withLock(`user:${userId}`, operation, lockKey, timeoutMs);
   }
 }

 /**
  * 为了向后兼容，保留原有的类名
  */
 export const UserLockService = LockService;