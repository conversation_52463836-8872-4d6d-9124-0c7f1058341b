import { Context } from 'hono';
import { RenderRequestSchema } from '../validators/browser';
import Cloudflare from "cloudflare";

export class BrowserService {
	private cloudflare: Cloudflare;

	constructor(private readonly c: Context) {
		this.cloudflare = new Cloudflare({
			apiToken: c.env.cloudflare.apiToken,
		});
	}

	async handleRequest(
		form: RenderRequestSchema,
	): Promise<Response> {
		try {
			// 根据渲染类型调用不同的Cloudflare Browser Rendering API
			switch (form.renderType) {
				case 'pdf':
					return await this.renderPdf(form);
				case 'html':
					return await this.renderHtml(form);
				case 'markdown':
					return await this.renderMarkdown(form);
				default:
					throw new Error(`不支持的渲染类型: ${form.renderType}`);
			}
		} catch (error) {
			console.error('Browser rendering error:', error);
			throw error;
		}
	}

	// 使用Cloudflare Browser Rendering API渲染PDF
	private async renderPdf(form: RenderRequestSchema): Promise<Response> {
		const requestBody: any = {
			url: form.url,
		};

		// 添加PDF选项
		if (form.options) {
			if (form.options.format || form.options.landscape !== undefined || form.options.printBackground !== undefined || form.options.margin) {
				requestBody.pdfOptions = {};
				if (form.options.format) {
					requestBody.pdfOptions.format = form.options.format.toLowerCase();
				}
				if (form.options.landscape !== undefined) {
					requestBody.pdfOptions.landscape = form.options.landscape;
				}
				if (form.options.printBackground !== undefined) {
					requestBody.pdfOptions.printBackground = form.options.printBackground;
				}
				if (form.options.margin) {
					requestBody.pdfOptions.margin = form.options.margin;
				}
			}
			if (form.options.headers) {
				requestBody.setExtraHTTPHeaders = form.options.headers;
			}
			if (form.options.waitUntil || form.options.timeout) {
				requestBody.gotoOptions = {};
				if (form.options.waitUntil) {
					requestBody.gotoOptions.waitUntil = form.options.waitUntil;
				}
				if (form.options.timeout) {
					requestBody.gotoOptions.timeout = form.options.timeout;
				}
			}
		}
		const response = await this.cloudflare.browserRendering.pdf.create({
			...requestBody,
			account_id: this.c.env.cloudflare.account_id,
		});
	

		const pdfBuffer = await response.arrayBuffer();
		const filename = this.generateFilename(form.url, 'pdf');

		return new Response(pdfBuffer, {
			headers: {
				'Content-Type': 'application/pdf',
				'Content-Disposition': `attachment; filename="${filename}"`,
				'Cache-Control': 'no-cache',
			},
		});
	}

	// 使用Cloudflare Browser Rendering API渲染HTML
	private async renderHtml(form: RenderRequestSchema): Promise<Response> {
		const requestBody: any = {
			url: form.url,
		};

		// 添加HTML选项
		if (form.options) {
			if (form.options.headers) {
				requestBody.setExtraHTTPHeaders = form.options.headers;
			}
			if (form.options.waitUntil || form.options.timeout) {
				requestBody.gotoOptions = {};
				if (form.options.waitUntil) {
					requestBody.gotoOptions.waitUntil = form.options.waitUntil;
				}
				if (form.options.timeout) {
					requestBody.gotoOptions.timeout = form.options.timeout;
				}
			}
			// 如果不包含样式，阻止CSS加载
			if (!form.options.includeStyles) {
				requestBody.rejectResourceTypes = ['stylesheet'];
				requestBody.rejectRequestPattern = ['/^.*\\.(css)/'];
			}
		}

		const response = await this.cloudflare.browserRendering.content.create({
			...requestBody,
			account_id: this.c.env.cloudflare.account_id,
		});

		let htmlContent = response || '';

		// 如果启用内容清理
		if (form.options?.cleanContent) {
			htmlContent = htmlContent
				.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
				.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
				.replace(/<!--[\s\S]*?-->/g, '')
				.replace(/on\w+="[^"]*"/gi, '')
				.replace(/javascript:[^"']*/gi, '');
		}

		const filename = this.generateFilename(form.url, 'html');

		return new Response(htmlContent, {
			headers: {
				'Content-Type': 'text/html; charset=utf-8',
				'Content-Disposition': `attachment; filename="${filename}"`,
				'Cache-Control': 'no-cache',
			},
		});
	}

	// 使用Cloudflare Browser Rendering API渲染Markdown
	private async renderMarkdown(form: RenderRequestSchema): Promise<Response> {
		const requestBody: any = {
			url: form.url,
		};

		// 添加Markdown选项
		if (form.options) {
			if (form.options.headers) {
				requestBody.setExtraHTTPHeaders = form.options.headers;
			}
			if (form.options.waitUntil || form.options.timeout) {
				requestBody.gotoOptions = {};
				if (form.options.waitUntil) {
					requestBody.gotoOptions.waitUntil = form.options.waitUntil;
				}
				if (form.options.timeout) {
					requestBody.gotoOptions.timeout = form.options.timeout;
				}
			}
			// 阻止不必要的资源加载以提高性能
			requestBody.rejectResourceTypes = ['image', 'media', 'font'];
			requestBody.rejectRequestPattern = ['/^.*\\.(css)/', '/^.*\\.(js)/'];
		}

		const markdownContent = await this.cloudflare.browserRendering.markdown.create({
			...requestBody,
			account_id: this.c.env.cloudflare.account_id,
		});
		const filename = this.generateFilename(form.url, 'md');

		return new Response(markdownContent, {
			headers: {
				'Content-Type': 'text/markdown; charset=utf-8',
				'Content-Disposition': `attachment; filename="${filename}"`,
				'Cache-Control': 'no-cache',
			},
		});
	}

	// 生成文件名
	private generateFilename(url: string, extension: string): string {
		try {
			const urlObj = new URL(url);
			const hostname = urlObj.hostname.replace(/[^a-z0-9]/gi, '_');
			const pathname = urlObj.pathname.replace(/[^a-z0-9]/gi, '_').substring(0, 50);
			const timestamp = new Date().toISOString().slice(0, 19).replace(/[^0-9]/g, '');
			return `${hostname}${pathname}_${timestamp}.${extension}`;
		} catch {
			const timestamp = new Date().toISOString().slice(0, 19).replace(/[^0-9]/g, '');
			return `webpage_${timestamp}.${extension}`;
		}
	}


}
