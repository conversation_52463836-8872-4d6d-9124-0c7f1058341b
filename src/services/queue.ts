import { Queue } from '@cloudflare/workers-types';

// HTTP方法枚举
export enum HttpMethod {
	GET = 'GET',
	POST = 'POST',
	PUT = 'PUT',
	DELETE = 'DELETE',
	PATCH = 'PATCH',
}

// HTTP请求定义
export interface HttpRequest {
	http_method: HttpMethod;
	url: string;
	headers?: Record<string, string>;
	body?: string;
	oidc_token?: {
		service_account_email: string;
	};
}

// 任务定义
export interface Task {
	http_request: HttpRequest;
}

// 队列消息类型定义
export interface QueueMessage {
	id: string;
	content: string;
	timestamp: number;
	user: string;
}

// 队列处理服务
export class QueueService {
	// 默认超时时间：20分钟（1200000毫秒）
	private readonly DEFAULT_TIMEOUT = 20 * 60 * 1000;

	constructor(private readonly env: { QUEUE: Queue }) {}

	// 处理队列消息的方法
	async processMessage(message: QueueMessage): Promise<void> {
		try {
			console.log(`处理队列消息: ${message.id}`);
			console.log(`消息内容: ${message.content}`);
			console.log(`发送时间: ${new Date(message.timestamp).toISOString()}`);
			console.log(`发送用户: ${message.user}`);

			// 解析消息内容
			const content = JSON.parse(message.content) as Task;
			if (!content) {
				throw new Error('消息内容为空');
			}

			// 处理HTTP请求任务
			if (content.http_request) {
				await this.processHttpRequest(content.http_request);
			} else {
				console.log('未找到HTTP请求信息，跳过处理');
			}

			console.log(`消息 ${message.id} 处理完成`);
		} catch (error) {
			console.error(`处理消息 ${message.id} 时出错:`, error);
			throw error; // 重新抛出错误，让队列知道处理失败
		}
	}

	// 处理HTTP请求
	private async processHttpRequest(httpRequest: HttpRequest): Promise<Response> {
		const maxRetries = 3;
		const retryDelay = 1000; // 每次重试间隔（毫秒）

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				console.log(`第 ${attempt} 次尝试发送HTTP请求到: ${httpRequest.url}`);
				console.log(`请求方法: ${httpRequest.http_method}`);
				console.log(`设置请求超时时间: ${this.DEFAULT_TIMEOUT}毫秒`);

				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), this.DEFAULT_TIMEOUT);

				const requestInit: RequestInit = {
					method: 'post',
					headers: {
						'Connection': 'keep-alive',
						'Keep-Alive': 'timeout=1200, max=1000',
						...httpRequest.headers || {}
					},
					signal: controller.signal,
				};

				if (httpRequest.body) {
					requestInit.body = httpRequest.body;
				}

				console.log('发送HTTP请求...',requestInit.body);
				const response = await fetch(httpRequest.url, requestInit);
				clearTimeout(timeoutId);

				const responseStatus = response.status;
				const responseBody = await response.text();
				console.log(`收到响应: 状态码 ${responseStatus}`);
				console.log(`响应体: ${responseBody.substring(0, 200)}${responseBody.length > 200 ? '...' : ''}`);

				if (!response.ok) {
					throw new Error(`HTTP请求失败: ${responseStatus} ${response.statusText}`);
				}

				return response;
			} catch (error: any) {
				console.error(`请求失败（第 ${attempt} 次）:`, error);

				const isLastAttempt = attempt === maxRetries;

				const isRetryable =
					error.name === 'AbortError' ||
					error.name === 'FetchError' ||
					error.code === 'ECONNRESET' ||
					error.code === 'ENOTFOUND' ||
					error.message?.includes('network') ||
					error.message?.includes('Failed to fetch');

				if (isLastAttempt || !isRetryable) {
					console.error('已达最大重试次数或错误不可重试，抛出异常');
					throw error;
				}

				console.log(`将在 ${retryDelay}ms 后重试...`);
				await new Promise((res) => setTimeout(res, retryDelay));
			}
		}

		throw new Error('未知错误：到达循环末尾');
	}
}
