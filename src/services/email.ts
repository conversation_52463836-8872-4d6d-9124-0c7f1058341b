import { Context } from 'hono';
import { createConfig } from '../config';

interface BrevoEmailRequest {
	sender: {
		name: string;
		email: string;
	};
	to: Array<{ email: string; name?: string }>;
	subject: string;
	htmlContent: string;
	textContent?: string;
}

interface VerificationCode {
	email: string;
	code: string;
	expires: number;
	createdAt: number;
}

export class EmailService {
	private readonly BREVO_API_KEY: string;
	private readonly BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';
	private readonly CODE_EXPIRY_MINUTES = 10;
	private readonly kv: KVNamespace;

	constructor(private readonly c: Context) {
		const config = createConfig(c.env);
		this.BREVO_API_KEY = c.env.BREVO_API_KEY;
		this.kv = c.env.datatool_kv;
	}

	/**
	 * 生成6位数字验证码
	 */
	private generateVerificationCode(): string {
		return Math.floor(100000 + Math.random() * 900000).toString();
	}

	/**
	 * 发送验证码邮件
	 */
	async sendVerificationCode(email: string): Promise<{ success: boolean; message: string }> {
		// 验证邮箱格式
		if (!this.isValidEmail(email)) {
			throw new Error('邮箱格式不正确');
		}

		// 检查是否在冷却期内
		const lastSent = await this.getLastSentTime(email);
		if (lastSent && Date.now() - lastSent < 60000) {
			// 1分钟冷却期
			throw new Error('请等待1分钟后再次发送验证码');
		}

		// 生成验证码
		const code = this.generateVerificationCode();
		const expires = Date.now() + this.CODE_EXPIRY_MINUTES * 60 * 1000;

		// 存储验证码到KV
		const verificationData: VerificationCode = {
			email,
			code,
			expires,
			createdAt: Date.now(),
		};

		await this.kv.put(`verification_code:${email}`, JSON.stringify(verificationData), { expirationTtl: this.CODE_EXPIRY_MINUTES * 60 });

		// 记录发送时间
		await this.kv.put(
			`last_sent:${email}`,
			Date.now().toString(),
			{ expirationTtl: 60 } // 1分钟过期
		);

		// 发送邮件
		const emailRequest: BrevoEmailRequest = {
			sender: {
				name: "DataMinder",
				email: "<EMAIL>"
			},
			to: [{ email, name: email }],
			subject: 'DataMiner 登录验证码',
			htmlContent: this.generateEmailTemplate(code),
			textContent: `您的验证码是: ${code}，有效期${this.CODE_EXPIRY_MINUTES}分钟。`,
		};

		const response = await fetch(this.BREVO_API_URL, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'api-key': this.BREVO_API_KEY,
			},
			body: JSON.stringify(emailRequest),
		});

		if (!response.ok) {
			const errorData = await response.text();
			console.error('Brevo API error:', errorData);
			throw new Error('邮件发送失败，请稍后重试');
		}

		return { success: true, message: '验证码已发送到您的邮箱' };
	}

	/**
	 * 验证验证码
	 */
	async verifyCode(email: string, code: string): Promise<boolean> {
		const storedData = await this.kv.get(`verification_code:${email}`);
		if (!storedData) {
			throw new Error('验证码不存在或已过期');
		}

		const verificationData: VerificationCode = JSON.parse(storedData);

		// 检查是否过期
		if (Date.now() > verificationData.expires) {
			// 删除过期的验证码
			await this.kv.delete(`verification_code:${email}`);
			throw new Error('验证码已过期');
		}

		// 验证码匹配
		if (verificationData.code === code) {
			// 验证成功，删除验证码
			await this.kv.delete(`verification_code:${email}`);
			return true;
		} else {
			throw new Error('验证码错误');
		}
	}

	/**
	 * 验证邮箱格式
	 */
	private isValidEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	/**
	 * 获取上次发送时间
	 */
	private async getLastSentTime(email: string): Promise<number | null> {
		const lastSent = await this.kv.get(`last_sent:${email}`);
		return lastSent ? parseInt(lastSent) : null;
	}

	/**
	 * 生成邮件模板
	 */
	private generateEmailTemplate(code: string): string {
		return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="utf-8">
				<title>DataMiner 登录验证码</title>
				<style>
					body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
					.container { max-width: 600px; margin: 0 auto; padding: 20px; }
					.header { background: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
					.content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
					.code { font-size: 32px; font-weight: bold; color: #4F46E5; text-align: center; margin: 20px 0; padding: 20px; background: white; border-radius: 8px; letter-spacing: 4px; }
					.footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
				</style>
			</head>
			<body>
				<div class="container">
					<div class="header">
						<h1>DataMiner</h1>
						<p>登录验证码</p>
					</div>
					<div class="content">
						<p>您好！</p>
						<p>您正在使用邮箱登录 DataMiner，请使用以下验证码完成登录：</p>
						<div class="code">${code}</div>
						<p>验证码有效期为 ${this.CODE_EXPIRY_MINUTES} 分钟，请及时使用。</p>
						<p>如果这不是您的操作，请忽略此邮件。</p>
					</div>
					<div class="footer">
						<p>此邮件由系统自动发送，请勿回复。</p>
					</div>
				</div>
			</body>
			</html>
		`;
	}
}
