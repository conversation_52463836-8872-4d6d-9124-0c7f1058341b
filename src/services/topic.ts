import { KVNamespace } from '@cloudflare/workers-types';
import { v4 as uuidv4 } from 'uuid';
import { Context } from 'hono';
import { User } from '../middleware/auth';
import { AuthService } from './auth';
import { CreditsService } from './credits';
import { dataToolApiRequest } from '../utils/dataToolHelper';
// changeTypeCredits 已废弃，现在从数据库读取积分配置
export interface TopicSearchParams {
	keyword: string;
}

export interface TopicNotesParams {
	pageId: string;
	cursor?: string;
	sort?: 'hot' | 'time' | 'trend';
	lastNoteId?: string;
	lastNoteCt?: string;
}

export interface Topic {
	id: string;
	title: string;
	description?: string;
	noteCount: number;
	createdAt: string;
	updatedAt: string;
}

export interface TopicNote {
	id: string;
	title: string;
	content: string;
	images: string[];
	userId: string;
	createdAt: string;
	updatedAt: string;
}

export class TopicService {
	private readonly kv: KVNamespace;
	private readonly loginUser: User;
	private readonly creditsService: CreditsService;
	constructor(private readonly c: Context) {
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		this.creditsService = new CreditsService(c);
	}

	async searchTopics(params: TopicSearchParams) {
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'xhs_topic_deduct');
		const data = await dataToolApiRequest('/xhs/topic/search', {
			method: 'POST',
			headers: {
					'Content-Type': 'application/json',
				},
			body: JSON.stringify({
				"keyword": params.keyword,
				"cookie": (await this.kv.get('xhs_creator_cookie')) ??
					'acw_tc=0a0d0eb817528094403658165e4ce68cb273af93147332fa079f8642f3b14a; websectiga=3fff3a9f9f07284b62c0f2ebf91a3b10193175c06e4f71492b60e056edcdebb2; customerClientId=149435390385229; galaxy.creator.beaker.session.id=1751609938829001316939; a1=197d4166d9by97sdzz66wjijs1fvftny644hne6qg40000321780; x-user-id-creator.xiaohongshu.com=63ecd189000000002801ae96; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517523107399385421297wwlezhrxuluseopn; webId=3d407d9196d3776d31066dfb78802572; galaxy_creator_session_id=uj7ysndCgc3rllnVr8RwmtUjbjzzoAErYkBi; gid=yjWf4yKWj2K2yjWf4yKKf4dKjDvjWMfuuKKE4AkV3VMyix48hi9IvI888qJyWY882diDyjf4; sec_poison_id=61f2ad00-0eb1-4ac1-98e6-68d5e4adaf14; xsecappid=ugc',
			}),
		});
		// @ts-ignore
		if (data.code === 0) {
			await this.creditsService.deductUserCredit(this.loginUser.id, 'xhs_topic_deduct', '搜索话题', this.loginUser.id);
		}
		return data;
	}

	async getTopicNotes(params: TopicNotesParams) {
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'xhs_topic_note_deduct');
		const now = Date.now();
		if (!params.sort) {
			params.sort = 'hot';
		}
		let refreshType = 1;
		if (params.lastNoteId) {
			refreshType = 3;
		}
		// 获取笔记列表
		let url = `https://edith.xiaohongshu.com/api/sns/v5/topic/hot/topicfeed?view_type=1&page_size=10&refresh_type=${refreshType}&source=search_topic_tab&first_load_time=${now}&page_id=${
			params.pageId
		}&sort=${params.sort}&prefetch=1&session_id=${uuidv4()}`;
		if (params.cursor) {
			url += `&cursor_score=${params.cursor}`;
		}
		if (params.lastNoteId) {
			url += `&last_note_id=${params.lastNoteId}`;
			url += `&insert_note_id=`;
		}
		if (params.lastNoteCt) {
			url += `&last_note_ct=${params.lastNoteCt}`;
		}
		const response = await fetch(url, {
			headers: {
				'User-Agent':
					'discover/8.84 (iPhone; iOS 18.4.1; Scale/3.00) Resolution/1170*2532 Version/8.84 Build/8840703 Device/(Apple Inc.;iPhone14,5) NetType/WiFi',
				'x-b3-traceid': '12c2f2bfc9f3bc96',
				authorization: (await this.kv.get('xhs_topic_note_cookie')) ?? 'session.1744945307746111208463',
				accept: 'application/json, text/plain, */*',
				referer: 'https://app.xhs.cn/',
			},
		});
		const data = await response.json();
		// @ts-ignore
		if (data.code === 0) {
			await this.creditsService.deductUserCredit(this.loginUser.id, 'xhs_topic_note_deduct', '搜索话题笔记', this.loginUser.id);
		}
		return data;
	}
}
