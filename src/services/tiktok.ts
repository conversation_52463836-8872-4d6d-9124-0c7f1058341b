import { Context } from 'hono';
import { User } from '../middleware/auth';
import { CreditsService } from './credits';
import {
	GetTikTokUserInfoSchema,
	GetTikTokUserPostsSchema,
	GetTikTokUserFansSchema,
	GetTikTokVideoDetailSchema,
} from '../validators/tiktok';
import { dataToolApiRequest } from '../utils/dataToolHelper';

// 定义TikTok用户信息的接口
interface TikTokUserInfoResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: {
		userInfo?: {
			user?: {
				id?: string;
				shortId?: string;
				uniqueId?: string;
				nickname?: string;
				avatarLarger?: string;
				avatarMedium?: string;
				avatarThumb?: string;
				signature?: string;
				createTime?: number;
				verified?: boolean;
				secUid?: string;
				ftc?: boolean;
				relation?: number;
				openFavorite?: boolean;
				commentSetting?: number;
				commerceUserInfo?: {
					commerceUser?: boolean;
				};
				duetSetting?: number;
				stitchSetting?: number;
				privateAccount?: boolean;
				secret?: boolean;
				isADVirtual?: boolean;
				roomId?: string;
				uniqueIdModifyTime?: number;
				ttSeller?: boolean;
				region?: string;
				downloadSetting?: number;
				profileTab?: {
					showMusicTab?: boolean;
					showQuestionTab?: boolean;
					showPlayListTab?: boolean;
				};
				followingVisibility?: number;
				recommendReason?: string;
				nowInvitationCardUrl?: string;
				nickNameModifyTime?: number;
				isEmbedBanned?: boolean;
				canExpPlaylist?: boolean;
				profileEmbedPermission?: number;
				language?: string;
				eventList?: any[];
				suggestAccountBind?: boolean;
				isOrganization?: number;
				UserStoryStatus?: number;
				[key: string]: any;
			};
			stats?: {
				followerCount?: number;
				followingCount?: number;
				heart?: number;
				heartCount?: number;
				videoCount?: number;
				diggCount?: number;
				friendCount?: number;
				[key: string]: any;
			};
			statsV2?: {
				followerCount?: string;
				followingCount?: string;
				heart?: string;
				heartCount?: string;
				videoCount?: string;
				diggCount?: string;
				friendCount?: string;
				[key: string]: any;
			};
			itemList?: any[];
			[key: string]: any;
		};
		shareMeta?: {
			title?: string;
			desc?: string;
			[key: string]: any;
		};
		statusCode?: number;
		statusMsg?: string;
		needFix?: boolean;
		[key: string]: any;
	};
	[key: string]: any;
}

// 定义TikTok用户粉丝的接口
export interface TikTokUserFansResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: {
		extra?: {
			fatal_item_ids?: any[];
			logid?: string;
			now?: number;
			[key: string]: any;
		};
		hasMore?: boolean;
		minCursor?: string;
		maxCursor?: string;
		total?: number;
		userList?: Array<{
			id?: string;
			shortId?: string;
			uniqueId?: string;
			nickname?: string;
			avatarLarger?: string;
			avatarMedium?: string;
			avatarThumb?: string;
			signature?: string;
			createTime?: number;
			verified?: boolean;
			secUid?: string;
			ftc?: boolean;
			relation?: number;
			openFavorite?: boolean;
			commentSetting?: number;
			duetSetting?: number;
			stitchSetting?: number;
			privateAccount?: boolean;
			secret?: boolean;
			isADVirtual?: boolean;
			roomId?: string;
			[key: string]: any;
		}>;
		statusCode?: number;
		statusMsg?: string;
		[key: string]: any;
	};
	[key: string]: any;
}

// 定义TikTok视频详情的接口
interface TikTokVideoDetailResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: {
		extra?: {
			fatal_item_ids?: any[];
			logid?: string;
			now?: number;
			[key: string]: any;
		};
		itemInfo?: {
			itemStruct?: {
				AIGCDescription?: string;
				CategoryType?: number;
				id?: string;
				desc?: string;
				createTime?: number;
				digged?: boolean;
				diversificationId?: number;
				duetDisplay?: number;
				forFriend?: boolean;
				isAd?: boolean;
				isReviewing?: boolean;
				itemCommentStatus?: number;
				officalItem?: boolean;
				originalItem?: boolean;
				privateItem?: boolean;
				secret?: boolean;
				shareEnabled?: boolean;
				stitchDisplay?: number;
				textLanguage?: string;
				textTranslatable?: boolean;
				collected?: boolean;
				backendSourceEventTracking?: string;
				contents?: Array<{
					desc?: string;
					[key: string]: any;
				}>;
				item_control?: {
					can_repost?: boolean;
					[key: string]: any;
				};
				author?: {
					UserStoryStatus?: number;
					avatarLarger?: string;
					avatarMedium?: string;
					avatarThumb?: string;
					commentSetting?: number;
					downloadSetting?: number;
					duetSetting?: number;
					ftc?: boolean;
					id?: string;
					isADVirtual?: boolean;
					isEmbedBanned?: boolean;
					nickname?: string;
					openFavorite?: boolean;
					privateAccount?: boolean;
					relation?: number;
					secUid?: string;
					secret?: boolean;
					signature?: string;
					stitchSetting?: number;
					uniqueId?: string;
					verified?: boolean;
					[key: string]: any;
				};
				authorStats?: {
					diggCount?: number;
					followerCount?: number;
					followingCount?: number;
					friendCount?: number;
					heart?: number;
					heartCount?: number;
					videoCount?: number;
					[key: string]: any;
				};
				authorStatsV2?: {
					diggCount?: string;
					followerCount?: string;
					followingCount?: string;
					friendCount?: string;
					heart?: string;
					heartCount?: string;
					videoCount?: string;
					[key: string]: any;
				};
				music?: {
					authorName?: string;
					coverLarge?: string;
					coverMedium?: string;
					coverThumb?: string;
					duration?: number;
					id?: string;
					isCopyrighted?: boolean;
					original?: boolean;
					playUrl?: string;
					private?: boolean;
					title?: string;
					tt2dsp?: {
						[key: string]: any;
					};
					[key: string]: any;
				};
				stats?: {
					collectCount?: number;
					commentCount?: number;
					diggCount?: number;
					playCount?: number;
					shareCount?: number;
					[key: string]: any;
				};
				statsV2?: {
					collectCount?: string;
					commentCount?: string;
					diggCount?: string;
					playCount?: string;
					repostCount?: string;
					shareCount?: string;
					[key: string]: any;
				};
				video?: {
					PlayAddrStruct?: {
						DataSize?: number;
						FileCs?: string;
						FileHash?: string;
						Height?: number;
						Uri?: string;
						UrlKey?: string;
						UrlList?: string[];
						Width?: number;
						[key: string]: any;
					};
					VQScore?: string;
					bitrate?: number;
					bitrateInfo?: Array<{
						Bitrate?: number;
						BitrateFPS?: number;
						CodecType?: string;
						Format?: string;
						GearName?: string;
						MVMAF?: string;
						PlayAddr?: {
							DataSize?: number;
							FileCs?: string;
							FileHash?: string;
							Height?: number;
							Uri?: string;
							UrlKey?: string;
							UrlList?: string[];
							Width?: number;
							[key: string]: any;
						};
						QualityType?: number;
						VideoExtra?: string;
						[key: string]: any;
					}>;
					[key: string]: any;
				};
				[key: string]: any;
			};
			[key: string]: any;
		};
		[key: string]: any;
	};
	[key: string]: any;
}

// 定义TikTok用户作品的接口
interface TikTokUserPostsResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: {
		extra?: {
			fatal_item_ids?: any[];
			logid?: string;
			now?: number;
			[key: string]: any;
		};
		hasMorePrevious?: boolean;
		itemList?: Array<{
			AIGCDescription?: string;
			CategoryType?: number;
			id?: string;
			desc?: string;
			createTime?: number;
			digged?: boolean;
			diversificationId?: number;
			duetDisplay?: number;
			forFriend?: boolean;
			isAd?: boolean;
			isReviewing?: boolean;
			itemCommentStatus?: number;
			officalItem?: boolean;
			originalItem?: boolean;
			privateItem?: boolean;
			secret?: boolean;
			shareEnabled?: boolean;
			stitchDisplay?: number;
			textLanguage?: string;
			textTranslatable?: boolean;
			collected?: boolean;
			backendSourceEventTracking?: string;
			contents?: Array<{
				desc?: string;
				[key: string]: any;
			}>;
			item_control?: {
				can_repost?: boolean;
				[key: string]: any;
			};
			author?: {
				UserStoryStatus?: number;
				avatarLarger?: string;
				avatarMedium?: string;
				avatarThumb?: string;
				commentSetting?: number;
				downloadSetting?: number;
				duetSetting?: number;
				ftc?: boolean;
				id?: string;
				isADVirtual?: boolean;
				isEmbedBanned?: boolean;
				nickname?: string;
				openFavorite?: boolean;
				privateAccount?: boolean;
				relation?: number;
				secUid?: string;
				secret?: boolean;
				signature?: string;
				stitchSetting?: number;
				uniqueId?: string;
				verified?: boolean;
				[key: string]: any;
			};
			authorStats?: {
				diggCount?: number;
				followerCount?: number;
				followingCount?: number;
				friendCount?: number;
				heart?: number;
				heartCount?: number;
				videoCount?: number;
				[key: string]: any;
			};
			authorStatsV2?: {
				diggCount?: string;
				followerCount?: string;
				followingCount?: string;
				friendCount?: string;
				heart?: string;
				heartCount?: string;
				videoCount?: string;
				[key: string]: any;
			};
			music?: {
				authorName?: string;
				coverLarge?: string;
				coverMedium?: string;
				coverThumb?: string;
				duration?: number;
				id?: string;
				isCopyrighted?: boolean;
				original?: boolean;
				playUrl?: string;
				private?: boolean;
				title?: string;
				tt2dsp?: {
					tt_to_dsp_song_infos?: Array<{
						meta_song_id?: string;
						platform?: number;
						song_id?: string;
						token?: any;
						[key: string]: any;
					}>;
					[key: string]: any;
				};
				[key: string]: any;
			};
			stats?: {
				collectCount?: number;
				commentCount?: number;
				diggCount?: number;
				playCount?: number;
				shareCount?: number;
				[key: string]: any;
			};
			statsV2?: {
				collectCount?: string;
				commentCount?: string;
				diggCount?: string;
				playCount?: string;
				repostCount?: string;
				shareCount?: string;
				[key: string]: any;
			};
			video?: {
				PlayAddrStruct?: {
					DataSize?: number;
					FileCs?: string;
					FileHash?: string;
					Height?: number;
					Uri?: string;
					UrlKey?: string;
					UrlList?: string[];
					Width?: number;
					[key: string]: any;
				};
				VQScore?: string;
				bitrate?: number;
				bitrateInfo?: Array<{
					Bitrate?: number;
					BitrateFPS?: number;
					CodecType?: string;
					Format?: string;
					GearName?: string;
					MVMAF?: string;
					PlayAddr?: {
						DataSize?: number;
						FileCs?: string;
						FileHash?: string;
						Height?: number;
						Uri?: string;
						UrlKey?: string;
						UrlList?: string[];
						Width?: number;
						[key: string]: any;
					};
					QualityType?: number;
					VideoExtra?: string;
					[key: string]: any;
				}>;
				[key: string]: any;
			};
			[key: string]: any;
		}>;
		statusCode?: number;
		statusMsg?: string;
		[key: string]: any;
	};
	[key: string]: any;
}

export class TikTokService {
	private readonly kv: KVNamespace;
	private readonly loginUser: User;
	private readonly creditsService: CreditsService;

	constructor(private readonly c: Context) {
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		this.creditsService = new CreditsService(c);
	}

	/**
	 * 获取TikTok用户信息
	 */
	async getUserInfo(data: GetTikTokUserInfoSchema): Promise<TikTokUserInfoResponse> {
		const { url: uniqueId } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'tiktok_user_info_deduct');

		// 尝试从KV中获取缓存的用户信息
		const cacheKey = `tiktok_user_info_${uniqueId}`;
		const cachedInfo = (await this.kv.get(cacheKey, 'json')) as TikTokUserInfoResponse;
		if (cachedInfo) {
			await this.deductCreditsForUserInfo();
			return cachedInfo;
		}

		try {
			// 调用API获取用户信息
			const userInfo = await this.fetchUserInfo(uniqueId);

			// 缓存结果到KV (有效期1小时)
			await this.kv.put(cacheKey, JSON.stringify(userInfo), { expirationTtl: 3600 });

			// 扣除用户积分
			await this.deductCreditsForUserInfo();

			return userInfo;
		} catch (error) {
			console.error('获取TikTok用户信息失败:', error);
			throw error;
		}
	}

	/**
	 * 获取TikTok用户发布的作品
	 */
	async getUserPosts(data: GetTikTokUserPostsSchema): Promise<TikTokUserPostsResponse> {
		const { secUserId, cursor, count = 15 } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'tiktok_user_posts_deduct');

		// 构建缓存键（包含分页参数）
		const cacheKey = `tiktok_user_posts_${secUserId}_${cursor || 'first'}_${count}`;
		const cachedPosts = (await this.kv.get(cacheKey, 'json')) as TikTokUserPostsResponse;
		if (cachedPosts) {
			await this.deductCreditsForUserPosts();
			return cachedPosts;
		}

		try {
			// 直接使用传入的secUserId调用API获取用户作品
			const userPosts = await this.fetchUserPosts(secUserId, cursor, count);

			// 缓存结果到KV (有效期30分钟)
			await this.kv.put(cacheKey, JSON.stringify(userPosts), { expirationTtl: 1800 });

			// 扣除用户积分
			await this.deductCreditsForUserPosts();

			return userPosts;
		} catch (error) {
			console.error('获取TikTok用户作品失败:', error);
			throw error;
		}
	}

	/**
	 * 获取TikTok用户粉丝列表
	 */
	async getUserFans(data: GetTikTokUserFansSchema): Promise<TikTokUserFansResponse> {
		const { secUid, minCursor } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'tiktok_user_fans_deduct');

		// 构建缓存键（包含分页参数）
		const cacheKey = `tiktok_user_fans_${secUid}_${minCursor || 'first'}`;
		const cachedFans = (await this.kv.get(cacheKey, 'json')) as TikTokUserFansResponse;
		if (cachedFans) {
			await this.deductCreditsForUserFans();
			return cachedFans;
		}

		try {
			// 调用API获取用户粉丝列表
			const userFans = await this.fetchUserFans(secUid, minCursor);

			// 缓存结果到KV (有效期30分钟)
			await this.kv.put(cacheKey, JSON.stringify(userFans), { expirationTtl: 1800 });

			// 扣除用户积分
			await this.deductCreditsForUserFans();

			return userFans;
		} catch (error) {
			console.error('获取TikTok用户粉丝失败:', error);
			throw error;
		}
	}

	async getVideoDetail(data: GetTikTokVideoDetailSchema): Promise<TikTokVideoDetailResponse> {
		const { url } = data;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'tiktok_video_detail_deduct');

		// 构建缓存键（包含分页参数）
		const cacheKey = `tiktok_video_detail_${url}`;
		const cachedDetail = (await this.kv.get(cacheKey, 'json')) as TikTokVideoDetailResponse;
		if (cachedDetail) {
			await this.deductCreditsForVideoDetail();
			return cachedDetail;
		}

		try {
			// 调用API获取用户粉丝列表
			const itemId = await this.extractItemId(url);
			const videoDetail = await this.fetchVideoDetail(itemId);

			// 缓存结果到KV (有效期30分钟)
			await this.kv.put(cacheKey, JSON.stringify(videoDetail), { expirationTtl: 1800 });

			// 扣除用户积分
			await this.deductCreditsForVideoDetail();

			return videoDetail;
		} catch (error) {
			console.error('获取TikTok用户粉丝失败:', error);
			throw error;
		}
	}

	/**
	 * 从URL中提取itemId
	 */
	private async extractItemId(url: string): Promise<string> {
		// 先尝试直接匹配 video/photo/embed/ve 形式的 URL
		const itemIdMatch = url.match(/(?:video|photo|embed\/v2)\/(\d+)/);
		if (itemIdMatch) {
			return itemIdMatch[1]; // 返回 ID（第2个分组）
		}

		// 如果是短链或跳转型链接，尝试访问获取真实 URL
		if (url.includes('tiktok.com')) {
			const resp = await fetch(url, {
				method: 'GET',
				redirect: 'follow',
				headers: {
					Accept: 'application/json',
					'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				},
			});

			if (!resp.ok) {
				throw new Error('无效的 TikTok 链接');
			}

			const finalUrl = resp.url;
			const match = finalUrl.match(/(?:video|photo|embed\/v2)\/(\d+)/);
			if (match) {
				return match[1]; // 返回 ID
			}

			throw new Error('未能解析有效的 TikTok 链接');
		}

		throw new Error('无效的 TikTok 链接');
	}

	/**
	 * 扣除用户信息查询积分
	 */
	private async deductCreditsForUserInfo(): Promise<void> {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'tiktok_user_info_deduct', '获取TikTok用户信息', this.loginUser.id);
	}

	/**
	 * 扣除用户作品查询积分
	 */
	private async deductCreditsForUserPosts(): Promise<void> {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'tiktok_user_posts_deduct', '获取TikTok用户作品', this.loginUser.id);
	}

	/**
	 * 扣除用户粉丝查询积分
	 */
	private async deductCreditsForUserFans(): Promise<void> {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'tiktok_user_fans_deduct', '获取TikTok用户粉丝', this.loginUser.id);
	}

	private async deductCreditsForVideoDetail(): Promise<void> {
		await this.creditsService.deductUserCredit(this.loginUser.id, 'tiktok_video_detail_deduct', '获取TikTok视频详情', this.loginUser.id);
	}

	/**
	 * 调用API获取用户信息
	 */
	private async fetchUserInfo(uniqueId: string): Promise<TikTokUserInfoResponse> {
		return await dataToolApiRequest(`/tiktok/user/info?unique_id=${encodeURIComponent(uniqueId)}`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			},
		});
	}

	/**
	 * 调用API获取用户作品
	 */
	private async fetchUserPosts(secUid: string, cursor?: string, count: number = 15): Promise<TikTokUserPostsResponse> {
		let path = `/tiktok/user/post?sec_uid=${encodeURIComponent(secUid)}&count=${count}`;
		if (cursor) {
			path += `&cursor=${encodeURIComponent(Number(cursor) * 1000)}`;
		}

		return await dataToolApiRequest(path, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			},
		});
	}

	/**
	 * 调用API获取用户粉丝
	 */
	private async fetchUserFans(secUid: string, minCursor?: string, retry: number = 3): Promise<TikTokUserFansResponse> {
		let path = `/tiktok/user/fans?secUid=${encodeURIComponent(secUid)}`;
		if (minCursor) {
			path += `&minCursor=${encodeURIComponent(minCursor)}`;
		}

		const resp = await dataToolApiRequest(path, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			},
		});
		return resp;
	}

	private async fetchVideoDetail(itemId: string) {
		console.info(`/tiktok/detail?itemId=${encodeURIComponent(itemId)}`)
		return await dataToolApiRequest(`/tiktok/detail?itemId=${encodeURIComponent(itemId)}`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
			},
		});
	}
}
