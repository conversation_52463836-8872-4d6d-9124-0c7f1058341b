import Stripe from 'stripe';
import { Context } from 'hono';
import { drizzle } from 'drizzle-orm/d1';
import { eq, and } from 'drizzle-orm';
import { StripeCheckoutModel } from '../models/schema';
import { User } from '../middleware/auth';
import { sql } from 'drizzle-orm';
import { createConfig } from '../config';
import { CreatePaymentIntentSchema } from '../validators/stripe';
import { ProductService } from './product';
import { CreditsService } from './credits';
import { FeishuHelper } from '../utils/feishuHelper';
import { AuthService } from './auth';

export interface StripeCheckoutSession {
	sessionId: string;
	url: string;
}

export interface StripePaymentIntent {
	clientSecret: string;
	paymentIntentId: string;
	amount: number;
	currency: string;
}

export interface StripeProduct {
	id: string;
	name: string;
	description?: string;
	active: boolean;
	default_price?: string;
}

export interface StripePrice {
	id: string;
	product: string;
	unit_amount: number;
	currency: string;
	active: boolean;
}

export class StripeService {
	private stripe: Stripe;
	private db: any;
	private loginUser: User | null;
	private config;
	private kv: KVNamespace;

	constructor(private readonly c: Context) {
		this.db = drizzle(c.env.datatool_d1);
		this.loginUser = c.get('user') || null;
		this.kv = c.env.datatool_kv;

		// 使用Cloudflare Workers环境变量创建配置
		this.config = createConfig(c.env);

		if (!this.config.stripe.secretKey) {
			throw new Error('STRIPE_SECRET_KEY is not configured');
		}

		this.stripe = new Stripe(this.config.stripe.secretKey);
	}

	/**
	 * 直接在Stripe中创建商品和价格
	 */
	async createStripeProduct(productData: {
		name: string;
		description?: string;
		price: number; // 以分为单位
		currency: string;
		is_active: boolean;
	}) {
		try {
			// 创建Stripe商品
			const stripeProduct = await this.stripe.products.create({
				name: productData.name,
				description: productData.description || undefined,
				active: productData.is_active,
			});

			// 创建Stripe价格
			const stripePrice = await this.stripe.prices.create({
				product: stripeProduct.id,
				unit_amount: productData.price, // 已经是分为单位
				currency: productData.currency.toLowerCase(),
				active: productData.is_active,
			});

			return {
				stripe_product_id: stripeProduct.id,
				stripe_price_id: stripePrice.id,
				product: stripeProduct,
				price: stripePrice,
			};
		} catch (error: any) {
			console.error('创建Stripe商品失败:', error);
			throw new Error(`创建Stripe商品失败: ${error.message}`);
		}
	}

	/**
	 * 更新商品时先停用旧商品再创建新商品
	 */
	async updateProductWithNewVersion(
		stripeProductId: string,
		stripePriceId: string,
		productData: {
			name: string;
			description?: string;
			price: number; // 以分为单位
			currency: string;
			is_active: boolean;
		}
	) {
		try {
			// 1. 先将原来的Stripe商品设为未激活
			await this.stripe.products.update(stripeProductId, {
				active: false,
			});

			// 2. 停用原来的价格
			await this.stripe.prices.update(stripePriceId, {
				active: false,
			});

			// 3. 创建新的Stripe商品
			const newStripeProduct = await this.stripe.products.create({
				name: productData.name,
				description: productData.description || undefined,
				active: productData.is_active,
			});

			// 4. 为新商品创建价格
			const newStripePrice = await this.stripe.prices.create({
				product: newStripeProduct.id,
				unit_amount: productData.price, // 已经是分为单位
				currency: productData.currency.toLowerCase(),
				active: productData.is_active,
			});

			return {
				stripe_product_id: newStripeProduct.id,
				stripe_price_id: newStripePrice.id,
				product: newStripeProduct,
				price: newStripePrice,
			};
		} catch (error: any) {
			console.error('更新Stripe商品失败:', error);
			throw new Error(`更新Stripe商品失败: ${error.message}`);
		}
	}
	/**
	 * 创建Payment Intent（用于嵌入式支付）
	 */
	async createPaymentIntent(form: CreatePaymentIntentSchema): Promise<StripePaymentIntent> {
		try {
			// 获取商品信息
			const product = await new ProductService(this.c).getProductById(form.productId);

			if (!product || !product.is_active) {
				throw new Error('商品不存在或已下架');
			}

			if (!this.loginUser) {
				throw new Error('未登录');
			}

			// 计算总金额（以分为单位）
			const amount = Math.round(product.price * form.quantity * 100);

			// 创建Payment Intent
			const paymentIntent = await this.stripe.paymentIntents.create({
				amount,
				payment_method_types: ['card', 'alipay', 'wechat_pay'],
				currency: product.currency.toLowerCase(),
				metadata: {
					user_id: this.loginUser.id.toString(),
					product_id: form.productId.toString(),
					quantity: form.quantity.toString(),
					credits: product.credits.toString(),
				},
			});
			// 获取客户端IP地址
			const clientIP =
				this.c?.req?.header('CF-Connecting-IP') || this.c?.req?.header('X-Forwarded-For') || this.c?.req?.header('X-Real-IP') || 'unknown';

			// 这里可以根据需要添加数据库记录逻辑
			await this.db.insert(StripeCheckoutModel).values({
				ipAddress: clientIP,
				userId: this.loginUser.id,
				amount: paymentIntent.amount || 0,
				paymentIntent: paymentIntent.id || '',
				paymentStatus: 'unpay',
				currency: paymentIntent.currency || '',
				paymentMethodTypes: JSON.stringify(paymentIntent.payment_method_types) || '',
				sessionData: JSON.stringify(paymentIntent),
				metaData: JSON.stringify(paymentIntent.metadata),
				expiresAt: Date.now() + 24 * 60 * 60 * 1000,
			});

			return {
				clientSecret: paymentIntent.client_secret!,
				paymentIntentId: paymentIntent.id,
				amount: paymentIntent.amount,
				currency: paymentIntent.currency,
			};
		} catch (error: any) {
			console.error('创建Payment Intent失败:', error);
			throw new Error(`创建支付意图失败: ${error.message}`);
		}
	}

	/**
	 * 处理Stripe Webhook回调
	 */
	async handleWebhook(body: string, signature: string) {
		try {
			if (!this.config.stripe.webhookKey) {
				throw new Error('STRIPE_WEBHOOK_SECRET is not configured');
			}

			// 验证webhook签名（异步方式）
			let event;
			try {
				event = await this.stripe.webhooks.constructEventAsync(body, signature, this.config.stripe.webhookKey);
			} catch (err: any) {
				console.error('Webhook签名验证失败:', err.message);
				throw new Error(`Webhook签名验证失败: ${err.message}`);
			}

			console.log('收到Stripe事件:', event.type);

			// 根据事件类型处理
			switch (event.type) {
				case 'checkout.session.completed':
					await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
					break;
				case 'payment_intent.succeeded':
					await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
					break;
				case 'payment_intent.payment_failed':
					await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
					break;
				case 'charge.updated':
					await this.handleChargeUpdated(event.data.object as Stripe.Charge);
				default:
					console.log(`未处理的事件类型: ${event.type}`);
			}

			return { success: true, message: '事件处理成功' };
		} catch (error: any) {
			console.error('处理Webhook失败:', error);
			throw new Error(`处理Webhook失败: ${error.message}`);
		}
	}

	/**
	 * 处理 Charge 更新事件，主要用于更新支付方式详情
	 */
	private async handleChargeUpdated(charge: Stripe.Charge) {
		try {
			console.log('处理 Charge 更新事件:', charge.id);

			// 检查是否已处理过
			if (await this.isEventProcessed(charge.id, 'charge_updated')) {
				console.log(`Charge 更新事件 ${charge.id} 已经处理过，跳过重复处理`);
				return;
			}

			// 获取支付方式详情
			const paymentMethodDetails = {
				type: charge.payment_method_details?.type || 'unknown',
				brand: charge.payment_method_details?.card?.brand || null,
				last4: charge.payment_method_details?.card?.last4 || null,
				exp_month: charge.payment_method_details?.card?.exp_month || null,
				exp_year: charge.payment_method_details?.card?.exp_year || null,
				funding: charge.payment_method_details?.card?.funding || null,
				country: charge.payment_method_details?.card?.country || null,
				wallet: charge.payment_method_details?.card?.wallet?.type || null,
				// 支付宝相关信息
				alipay_buyer_id: charge.payment_method_details?.alipay?.buyer_id || null,
				// 微信支付相关信息
				wechat_pay_transaction_id: charge.payment_method_details?.wechat_pay?.transaction_id || null,
				// 其他支付方式的详情可以根据需要添加
			};

			// 更新数据库中的支付方式详情
			const updateResult = await this.db
				.update(StripeCheckoutModel)
				.set({
					paymentMethodDetails: JSON.stringify(paymentMethodDetails),
					updatedAt: new Date().toISOString(),
				})
				.where(eq(StripeCheckoutModel.paymentIntent, charge.payment_intent as string))
				.run();

			if (updateResult.changes > 0) {
				console.log(`成功更新支付方式详情，Payment Intent: ${charge.payment_intent}`);
			} else {
				console.log(`未找到对应的支付记录，Payment Intent: ${charge.payment_intent}`);
			}

			// 标记为已处理
			await this.markEventAsProcessed(charge.id, 'charge_updated');
		} catch (error: any) {
			console.error('处理 Charge 更新事件失败:', error);
			// 不抛出错误，避免影响 webhook 处理
		}
	}

	/**
	 * 检查事件是否已处理（使用KV缓存）
	 */
	private async isEventProcessed(eventId: string, eventType: string): Promise<boolean> {
		if (!eventId) {
			return false;
		}
		const cacheKey = `stripe_event_${eventType}_${eventId}`;
		const cached = await this.kv.get(cacheKey);
		return cached !== null;
	}

	/**
	 * 标记事件为已处理
	 */
	private async markEventAsProcessed(eventId: string, eventType: string): Promise<void> {
		if (!eventId) {
			return;
		}
		const cacheKey = `stripe_event_${eventType}_${eventId}`;
		// 设置24小时过期时间
		await this.kv.put(cacheKey, 'processed', { expirationTtl: 24 * 60 * 60 });
	}

	/**
	 * 验证和解析支付元数据
	 */
	private validatePaymentMetadata(metadata: any): { userId: number; productId: number; credits: number } {
		const userId = parseInt(metadata?.user_id || '0');
		const productId = parseInt(metadata?.product_id || '0');
		const credits = parseFloat(metadata?.credits || '0');

		if (!userId || !productId || !credits) {
			throw new Error('缺少必要的元数据');
		}

		return { userId, productId, credits };
	}

	/**
	 * 处理积分奖励的通用方法
	 */
	private async processCreditsReward(userId: number, credits: number, reason: string): Promise<void> {
		const creditsService = new CreditsService(this.c);
		await creditsService.addUserCredit(
			userId,
			'stripe_payment_credit',
			reason,
			0, // 系统操作
			credits
		);
	}

	/**
	 * 处理支付成功事件
	 */
	private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
		try {
			console.info(session, 'session');
			// 检查是否已处理过（KV缓存）
			if (await this.isEventProcessed((session.payment_intent || '').toString(), 'payment_intent_succeeded')) {
				console.log(`支付意图 ${session.payment_intent} 已经处理过，跳过重复处理`);
				return;
			}
			const { userId, productId, credits } = this.validatePaymentMetadata(session.metadata);
			// 检查数据库中是否已经处理过这个支付
			const existingRecord = await this.db
				.select()
				.from(StripeCheckoutModel)
				.where(
					and(
						eq(StripeCheckoutModel.isHandled, true),
						eq(StripeCheckoutModel.userId, userId),
						eq(StripeCheckoutModel.paymentIntent, (session.payment_intent || '').toString())
					)
				)
				.get();

			if (existingRecord) {
				console.log(`支付意图 ${session.payment_intent} 在数据库中已标记为已处理，跳过重复处理`);
				// 仍然标记KV缓存，避免下次重复检查
				await this.markEventAsProcessed((session.payment_intent || '').toString(), 'payment_intent_succeeded');
				return;
			}

			// 处理积分奖励
			await this.processCreditsReward(userId, credits, `购买商品获得积分 - 订单: ${session.payment_intent}`);

			// 获取实际使用的支付方式
			let actualPaymentMethodTypes: string[] = [];
			if (session.payment_method_types && session.payment_method_types.length > 0) {
				actualPaymentMethodTypes = session.payment_method_types;
			}

			// 更新checkout表
			await this.db
				.update(StripeCheckoutModel)
				.set({
					status: session.status,
					paymentStatus: session.payment_status,
					isHandled: true,
					remark: `积分充值，获得 ${credits} 积分`,
					paymentMethodTypes: JSON.stringify(actualPaymentMethodTypes),
				})
				.where(
					and(
						eq(StripeCheckoutModel.userId, userId),
						eq(StripeCheckoutModel.paymentIntent, session.payment_intent?.toString() || ''),
						eq(StripeCheckoutModel.isHandled, false)
					)
				);

			// 标记为已处理
			await this.markEventAsProcessed(session.id, 'checkout_session_completed');
			const userRecord = await (new AuthService(this.c)).getUserById(userId)
			await FeishuHelper.sendMsg(this.c, `用户 ${userId} - ${userRecord?.username} 支付成功 金额¥${session.amount_total! / 100}，获得 ${credits} 积分`, 'stripe 支付回调');
			console.log(`用户 ${userId} 通过嵌入式支付成功，获得 ${credits} 积分`);
			console.log(`用户 ${userId} 支付成功，获得 ${credits} 积分`);
		} catch (error: any) {
			console.error('处理支付成功事件失败:', error);
			throw error;
		}
	}

	/**
	 * 处理支付成功事件（Payment Intent）
	 */
	private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
		try {
			console.log('支付意图成功:', paymentIntent.id);

			// 检查是否已处理过（KV缓存）
			if (await this.isEventProcessed(paymentIntent.id, 'payment_intent_succeeded')) {
				console.log(`支付意图 ${paymentIntent.id} 已经处理过，跳过重复处理`);
				return;
			}

			// 验证元数据
			let paymentData;
			try {
				paymentData = this.validatePaymentMetadata(paymentIntent.metadata);
			} catch (error) {
				console.log('Payment Intent缺少必要的元数据，跳过处理');
				return;
			}

			const { userId, productId, credits } = paymentData;

			// 检查数据库中是否已经处理过这个支付
			const existingRecord = await this.db
				.select()
				.from(StripeCheckoutModel)
				.where(
					and(
						eq(StripeCheckoutModel.isHandled, true),
						eq(StripeCheckoutModel.userId, userId),
						eq(StripeCheckoutModel.paymentIntent, paymentIntent.id || '')
					)
				)
				.get();

			if (existingRecord) {
				console.log(`支付意图 ${paymentIntent.id} 在数据库中已标记为已处理，跳过重复处理`);
				// 仍然标记KV缓存，避免下次重复检查
				await this.markEventAsProcessed(paymentIntent.id, 'payment_intent_succeeded');
				return;
			}

			// 处理积分奖励
			await this.processCreditsReward(userId, credits, `购买商品获得积分 - 订单: ${paymentIntent.id}`);

			// 更新数据库状态
			await this.db
				.update(StripeCheckoutModel)
				.set({
					isHandled: true,
					status: 'complete',
					paymentStatus: 'paid',
					remark: `积分充值，获得 ${credits} 积分`,
				})
				.where(
					and(
						eq(StripeCheckoutModel.userId, userId),
						eq(StripeCheckoutModel.paymentIntent, paymentIntent.id || ''),
						eq(StripeCheckoutModel.isHandled, false)
					)
				);

			// 标记为已处理
			await this.markEventAsProcessed(paymentIntent.id, 'payment_intent_succeeded');
			const userRecord = await (new AuthService(this.c)).getUserById(userId)
			await FeishuHelper.sendMsg(this.c, `用户 ${userId} - ${userRecord?.username} 支付成功 金额¥${paymentIntent.amount / 100}，获得 ${credits} 积分`, 'stripe 支付回调');
			console.log(`用户 ${userId} 通过嵌入式支付成功，获得 ${credits} 积分`);
		} catch (error: any) {
			console.error('处理Payment Intent成功事件失败:', error);
			// 不抛出错误，避免影响webhook处理
		}
	}

	/**
	 * 处理支付失败事件
	 */
	private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
		try {
			console.log('支付意图失败:', paymentIntent.id);

			// 检查是否已处理过
			if (await this.isEventProcessed(paymentIntent.id, 'payment_intent_failed')) {
				console.log(`支付失败事件 ${paymentIntent.id} 已经处理过，跳过重复处理`);
				return;
			}

			// 验证元数据
			let paymentData;
			try {
				paymentData = this.validatePaymentMetadata(paymentIntent.metadata);
			} catch (error) {
				console.log('Payment Intent缺少必要的元数据，跳过处理');
				return;
			}

			const { userId, productId, credits } = paymentData;

			// 构建失败原因
			const failureReason = this.buildFailureReason(paymentIntent);

			// 更新数据库状态
			await this.db
				.update(StripeCheckoutModel)
				.set({
					isHandled: true,
					status: 'failed',
					paymentStatus: 'failed',
					remark: failureReason,
					updatedAt: new Date().toISOString(),
				})
				.where(
					and(
						eq(StripeCheckoutModel.userId, userId),
						eq(StripeCheckoutModel.paymentIntent, paymentIntent.id || ''),
						eq(StripeCheckoutModel.isHandled, false)
					)
				);

			// 标记为已处理
			await this.markEventAsProcessed(paymentIntent.id, 'payment_intent_failed');

			console.log(`用户 ${userId} 支付失败，原因: ${failureReason}`);
		} catch (error: any) {
			console.error('处理支付失败事件失败:', error);
			// 不抛出错误，避免影响webhook处理
		}
	}

	/**
	 * 构建支付失败原因
	 */
	private buildFailureReason(paymentIntent: Stripe.PaymentIntent): string {
		const lastPaymentError = paymentIntent.last_payment_error;
		if (!lastPaymentError) {
			return '支付失败，原因未知';
		}

		const errorType = lastPaymentError.type || 'unknown';
		const errorCode = lastPaymentError.code || 'unknown';
		const errorMessage = lastPaymentError.message || '未知错误';
		const declineCode = lastPaymentError.decline_code;

		// 构建详细的错误信息
		let reason = `支付失败 - 类型: ${errorType}`;
		if (errorCode !== 'unknown') {
			reason += `, 错误码: ${errorCode}`;
		}
		if (declineCode) {
			reason += `, 拒绝码: ${declineCode}`;
		}
		reason += `, 详情: ${errorMessage}`;

		// 限制长度，避免数据库字段溢出
		return reason.length > 500 ? reason.substring(0, 497) + '...' : reason;
	}

	/**
	 * 获取支付会话状态
	 */
	async getPaymentStatus(paymentIntent: string) {
		try {
			return await this.db
				.select()
				.from(StripeCheckoutModel)
				.where(
					and(
						eq(StripeCheckoutModel.isHandled, true),
						eq(StripeCheckoutModel.userId, this.loginUser?.id ?? 0),
						eq(StripeCheckoutModel.paymentIntent, paymentIntent || '')
					)
				)
				.get();
		} catch (error: any) {
			console.error('获取支付会话状态失败:', error);
			throw new Error(`获取支付会话状态失败: ${error.message}`);
		}
	}

	/**
	 * 格式化支付方式信息
	 */
	private formatPaymentMethodInfo(paymentMethodDetails: any, paymentMethodTypes: string[]) {
		if (!paymentMethodDetails) {
			// 如果没有详细信息，使用基础类型
			return {
				type: paymentMethodTypes.length > 0 ? paymentMethodTypes[0] : 'card',
			};
		}

		const paymentMethod: any = {
			type: paymentMethodDetails.type || (paymentMethodTypes.length > 0 ? paymentMethodTypes[0] : 'card'),
		};

		// 根据支付方式类型添加详细信息
		switch (paymentMethodDetails.type) {
			case 'card':
				if (paymentMethodDetails.brand || paymentMethodDetails.last4) {
					paymentMethod.card = {
						brand: paymentMethodDetails.brand || 'unknown',
						last4: paymentMethodDetails.last4 || '****',
						exp_month: paymentMethodDetails.exp_month,
						exp_year: paymentMethodDetails.exp_year,
						funding: paymentMethodDetails.funding,
						country: paymentMethodDetails.country,
						wallet: paymentMethodDetails.wallet,
					};
				}
				break;
			case 'alipay':
				if (paymentMethodDetails.alipay_buyer_id) {
					paymentMethod.alipay = {
						buyer_id: paymentMethodDetails.alipay_buyer_id,
					};
				}
				break;
			case 'wechat_pay':
				if (paymentMethodDetails.wechat_pay_transaction_id) {
					paymentMethod.wechat_pay = {
						transaction_id: paymentMethodDetails.wechat_pay_transaction_id,
					};
				}
				break;
			default:
				// 对于其他支付方式，保持基础信息
				break;
		}

		return paymentMethod;
	}

	/**
	 * 获取用户付款记录
	 */
	async getPaymentHistory() {
		try {
			if (!this.loginUser) {
				throw new Error('未登录');
			}

			// 从数据库获取用户的付款记录
			const payments = await this.db
				.select({
					id: StripeCheckoutModel.paymentIntent,
					amount: StripeCheckoutModel.amount,
					currency: StripeCheckoutModel.currency,
					status: StripeCheckoutModel.paymentStatus,
					created: StripeCheckoutModel.createdAt,
					description: StripeCheckoutModel.remark,
					payment_method_types: StripeCheckoutModel.paymentMethodTypes,
					payment_method_details: StripeCheckoutModel.paymentMethodDetails,
				})
				.from(StripeCheckoutModel)
				.where(and(eq(StripeCheckoutModel.userId, this.loginUser.id), eq(StripeCheckoutModel.paymentStatus, 'paid')))
				.orderBy(sql`${StripeCheckoutModel.createdAt} DESC`)
				.limit(50);

			// 转换数据格式以匹配前端期望的格式
			const formattedPayments = payments.map((payment: any) => {
				let paymentMethodTypes;
				let paymentMethodDetails;

				try {
					paymentMethodTypes = payment.payment_method_types ? JSON.parse(payment.payment_method_types) : [];
				} catch {
					paymentMethodTypes = [];
				}

				try {
					paymentMethodDetails = payment.payment_method_details ? JSON.parse(payment.payment_method_details) : null;
				} catch {
					paymentMethodDetails = null;
				}

				// 构建支付方式信息
				const paymentMethod = this.formatPaymentMethodInfo(paymentMethodDetails, paymentMethodTypes);

				return {
					id: payment.id,
					amount: payment.amount,
					currency: payment.currency || 'usd',
					status: payment.status === 'paid' ? 'succeeded' : payment.status,
					created: Math.floor(new Date(payment.created).getTime() / 1000),
					description: payment.description || '积分充值',
					payment_method: paymentMethod,
				};
			});

			return {
				payments: formattedPayments,
			};
		} catch (error: any) {
			console.error('获取付款记录失败:', error);
			throw new Error(`获取付款记录失败: ${error.message}`);
		}
	}
}
