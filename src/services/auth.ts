import jwt, { SignOptions, Secret } from 'jsonwebtoken';
import { createConfig } from '../config';
import { userCreditModel, userModel } from '../models/schema';
import { eq, sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { User } from '../middleware/auth';
import { md5, generateApiKey } from '../utils/strHelper';
import { CreditsService } from './credits';
import { Context } from 'hono';

interface GoogleUserInfo {
	email: string;
	name: string;
	picture?: string;
	verified_email: boolean;
}
interface LoginCredentials {
	loginType: 'password' | 'google' | 'api_key' | 'email';
	username?: string;
	password?: string;
	api_key?: string;
	google_token?: string;
	email?: string; // 邮箱地址，用于邮箱验证码登录
	code?: string; // 验证码，用于邮箱验证码登录
}

interface LoginResponse {
	token: string;
	user: User;
}

export class AuthService {
	private readonly JWT_SECRET: Secret;
	private readonly JWT_EXPIRES_IN: number;
	private readonly db: ReturnType<typeof drizzle>;

	constructor(private readonly c: Context) {
		// 使用Cloudflare Workers环境变量创建配置
		const config = createConfig(c.env);
		this.JWT_SECRET = config.jwt.secret;
		this.JWT_EXPIRES_IN = config.jwt.expiresIn;
		this.db = drizzle(c.env.datatool_d1);
	}

	async getUserByUsername(username: string): Promise<typeof userModel.$inferSelect | null> {
		const [user] = await this.db.select().from(userModel).where(eq(userModel.username, username));
		return user ?? null;
	}

	async getUserById(id: number): Promise<typeof userModel.$inferSelect | null> {
		const [user] = await this.db.select().from(userModel).where(eq(userModel.id, id));
		return user ?? null;
	}

	async getUserByApiKey(apiKey: string): Promise<typeof userModel.$inferSelect | null> {
		const [user] = await this.db.select().from(userModel).where(eq(userModel.api_key, apiKey));
		return user ?? null;
	}

	async getUserCredit(userId: number): Promise<number> {
		const [userCredit] = await this.db.select().from(userCreditModel).where(eq(userCreditModel.user_id, userId));
		return userCredit?.credit ?? 0;
	}

	async login(credentials: LoginCredentials): Promise<LoginResponse> {
		let user;

		switch (credentials.loginType) {
			case 'password':
				if (!credentials.username || !credentials.password) {
					throw new Error('用户名和密码不能为空');
				}
				user = await this.getUserByUsername(credentials.username);
				if (!user) {
					throw new Error('用户不存在');
				}
				if (user.password !== md5(credentials.password)) {
					throw new Error('密码错误');
				}
				break;

			case 'api_key':
				if (!credentials.api_key) {
					throw new Error('API Key 不能为空');
				}
				user = await this.getUserByApiKey(credentials.api_key);
				if (!user) {
					throw new Error('无效的 API Key');
				}
				break;

			case 'google':
				if (!credentials.google_token) {
					throw new Error('Google Token 不能为空');
				}
				// 验证Google token并获取用户信息
				const googleUser = await this.verifyGoogleToken(credentials.google_token);
				if (!googleUser) {
					throw new Error('无效的 Google Token');
				}
				// 查找或创建用户
				user = await this.findOrCreateGoogleUser(googleUser);
				break;

			case 'email':
				if (!credentials.email || !credentials.code) {
					throw new Error('邮箱地址和验证码不能为空');
				}
				// 验证邮箱验证码
				const { EmailService } = await import('./email');
				const emailService = new EmailService(this.c);
				await emailService.verifyCode(credentials.email, credentials.code);
				
				// 验证成功后，查找或创建用户
				user = await this.getUserByUsername(credentials.email);
				if (!user) {
					// 如果用户不存在，创建新用户
					user = await this.createUserByEmail(credentials.email);
				}
				break;

			default:
				throw new Error('不支持的登录类型');
		}

		if (!user || user.status === 0) {
			throw new Error('User is disabled');
		}
		const userCredit = await this.getUserCredit(user.id);

		await this.db
			.update(userModel)
			.set({
				last_login_at: new Date().toISOString(),
			})
			.where(eq(userModel.id, user.id));

		const payload: User = {
			id: user.id,
			username: user.username ?? '',
			role: user.role,
			api_key: user.api_key ?? '',
			credits: userCredit ?? '',
		};

		const options: SignOptions = {
			expiresIn: this.JWT_EXPIRES_IN,
		};

		// Generate JWT token
		const token = jwt.sign(payload, this.JWT_SECRET, options);

		return {
			token,
			user: payload,
		};
	}

	// Add method to verify JWT token
	async verifyToken(token: string): Promise<User> {
		try {
			const decoded = jwt.verify(token, this.JWT_SECRET) as User;
			// return decoded;
			const user = await this.getUserByUsername(decoded.username);
			if (!user) {
				throw new Error('Invalid token');
			}
			const userCredit = await this.getUserCredit(user.id);
			return {
				id: user.id,
				username: user.username ?? '',
				role: user.role,
				api_key: user.api_key ?? '',
				credits: userCredit,
			};
		} catch (error) {
			throw new Error('Invalid token');
		}
	}

	async refreshApiKey(userId: number): Promise<string> {
		const newApiKey = generateApiKey();

		await this.db
			.update(userModel)
			.set({
				api_key: newApiKey,
			})
			.where(eq(userModel.id, userId));

		return newApiKey;
	}

	// 验证Google token
	async verifyGoogleToken(token: string): Promise<GoogleUserInfo | null> {
		try {
			// 调用Google API验证token
			const response = await fetch(`https://www.googleapis.com/oauth2/v1/userinfo?access_token=${token}`);

			if (!response.ok) {
				return null;
			}

			const userInfo: GoogleUserInfo = await response.json();

			// 验证邮箱是否已验证
			if (!userInfo.verified_email) {
				throw new Error('Google account email not verified');
			}

			return userInfo;
		} catch (error) {
			console.error('Google token verification failed:', error);
			return null;
		}
	}

	// 查找或创建Google用户
	async findOrCreateGoogleUser(googleUser: GoogleUserInfo): Promise<typeof userModel.$inferSelect> {
		// 首先尝试通过邮箱查找用户
		const [existingUser] = await this.db.select().from(userModel).where(eq(userModel.username, googleUser.email));

		if (existingUser) {
			return existingUser;
		}

		// 如果用户不存在，创建新用户
		const newUser = await this.createUserByEmail(googleUser.email);

		return newUser;
	}

	// 通过邮箱创建用户（用于邮箱验证码登录）
	async createUserByEmail(email: string): Promise<typeof userModel.$inferSelect> {
		// 生成新的API Key
		const newApiKey = generateApiKey();

		const [newUser] = await this.db
			.insert(userModel)
			.values({
				username: email, // 将邮箱存储到username字段中
				password: '', // 邮箱验证码用户不需要密码
				role: 'user',
				api_key: newApiKey,
				status: 1,
				created_at: sql`datetime('now', '+8 hours')`,
				updated_at: sql`datetime('now', '+8 hours')`,
			})
			.returning();

		// 为新用户创建积分记录
		await this.db.insert(userCreditModel).values({
			user_id: newUser.id,
			credit: 0,
		});

		// 赠送注册积分
		try {
			const creditsService = new CreditsService(this.c);
			await creditsService.addUserCredit(
				newUser.id,
				'register_send_credit',
				'用户注册赠送积分',
				newUser.id // 操作者为用户自己
			);
		} catch (error) {
			console.error('赠送注册积分失败:', error);
			// 不抛出错误，避免影响用户注册流程
		}

		return newUser;
	}

	// 生成JWT token
	generateToken(payload: User): string {
		const options: SignOptions = {
			expiresIn: this.JWT_EXPIRES_IN,
		};
		return jwt.sign(payload, this.JWT_SECRET, options);
	}
}
