import { Context } from 'hono';
import { User } from '../middleware/auth';
import { CreditsService } from './credits';
import { GetYouTubeUserVideosSchema } from '../validators/youtube';

// 定义不包含 type 字段的参数类型
// changeTypeCredits 已废弃，现在从数据库读取积分配置
import { dataToolApiRequest } from '../utils/dataToolHelper';

// 定义YouTube用户信息的接口
// 定义YouTube用户视频列表的接口
interface YouTubeUserVideosResponse {
	id?: string;
	channel?: string;
	channel_id?: string;
	title?: string;
	availability?: string | null;
	channel_follower_count?: number;
	description?: string;
	tags?: string[];
	thumbnails?: Array<{
		url?: string;
		height?: number;
		width?: number;
		preference?: number;
		id?: string;
		resolution?: string;
	}>;
	uploader_id?: string;
	uploader_url?: string;
	modified_date?: string | null;
	view_count?: number | null;
	playlist_count?: number | null;
	uploader?: string;
	channel_url?: string;
	_type?: string;
	entries?: Array<{
		_type?: string;
		ie_key?: string;
		id?: string;
		url?: string;
		title?: string;
		description?: string;
		duration?: number;
		channel_id?: string | null;
		channel?: string | null;
		channel_url?: string | null;
		uploader?: string | null;
		uploader_id?: string | null;
		uploader_url?: string | null;
		thumbnails?: Array<{
			url?: string;
			height?: number;
			width?: number;
		}>;
		timestamp?: number | null;
		release_timestamp?: number | null;
		availability?: string | null;
		view_count?: number;
		live_status?: string | null;
		channel_is_verified?: boolean;
		__x_forwarded_for_ip?: string | null;
	}>;
	extractor_key?: string;
	extractor?: string;
	webpage_url?: string;
	original_url?: string;
	webpage_url_basename?: string;
	webpage_url_domain?: string;
	release_year?: number | null;
	requested_entries?: number[];
	epoch?: number;
}

// 定义YouTube用户Shorts列表的接口
interface YouTubeUserShortsResponse {
	id?: string;
	channel?: string;
	channel_id?: string;
	title?: string;
	availability?: string | null;
	channel_follower_count?: number;
	description?: string;
	tags?: string[];
	thumbnails?: Array<{
		url?: string;
		height?: number;
		width?: number;
		preference?: number;
		id?: string;
		resolution?: string;
	}>;
	uploader_id?: string;
	uploader_url?: string;
	modified_date?: string | null;
	view_count?: number | null;
	playlist_count?: number | null;
	uploader?: string;
	channel_url?: string;
	_type?: string;
	entries?: Array<{
		title?: string;
		view_count?: number;
		thumbnails?: Array<{
			url?: string;
			height?: number;
			width?: number;
		}>;
		ie_key?: string;
		id?: string;
		_type?: string;
		url?: string;
		__x_forwarded_for_ip?: string | null;
	}>;
	extractor_key?: string;
	extractor?: string;
	webpage_url?: string;
	original_url?: string;
	webpage_url_basename?: string;
	webpage_url_domain?: string;
	release_year?: number | null;
	requested_entries?: number[];
	epoch?: number;
}

// 定义YouTube视频详情的接口
interface YouTubeVideoInfoResponse {
	id?: string;
	title?: string;
	formats?: Array<{
		format_id?: string;
		format_note?: string;
		ext?: string;
		protocol?: string;
		acodec?: string;
		vcodec?: string;
		url?: string;
		width?: number | null;
		height?: number | null;
		fps?: number | null;
		audio_channels?: number | null;
		quality?: number;
		has_drm?: boolean;
		tbr?: number;
		filesize_approx?: number | null;
		resolution?: string;
		aspect_ratio?: number | null;
		format?: string;
	}>;
	thumbnails?: Array<{
		url?: string;
		height?: number;
		width?: number;
		id?: string;
		resolution?: string;
	}>;
	description?: string;
	channel_id?: string;
	channel_url?: string;
	duration?: number;
	view_count?: number;
	age_limit?: number;
	webpage_url?: string;
	categories?: string[];
	tags?: string[];
	playable_in_embed?: boolean;
	live_status?: string;
	release_timestamp?: number;
	comment_count?: number;
	like_count?: number;
	channel?: string;
	channel_follower_count?: number;
	uploader?: string;
	uploader_id?: string;
	uploader_url?: string;
	upload_date?: string;
	availability?: string;
	original_url?: string;
	webpage_url_basename?: string;
	webpage_url_domain?: string;
	extractor?: string;
	extractor_key?: string;
	playlist?: string | null;
	playlist_index?: number | null;
	thumbnail?: string;
	display_id?: string;
	fulltitle?: string;
	duration_string?: string;
	is_live?: boolean;
	was_live?: boolean;
	epoch?: number;
}

export class YouTubeService {
	private readonly kv: KVNamespace;
	private readonly loginUser: User;
	private readonly creditsService: CreditsService;

	constructor(private readonly c: Context) {
		this.kv = c.env.datatool_kv;
		this.loginUser = c.get('user');
		this.creditsService = new CreditsService(c);
	}

	/**
	 * 扣除用户信息查询积分
	 */
	private async deductCreditsForUserInfo(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'youtube_user_info_deduct' as const,
			'YouTube用户信息查询',
			this.loginUser.id
		);
	}

	/**
	 * 扣除用户视频查询积分
	 */
	private async deductCreditsForUserVideos(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'youtube_user_videos_deduct' as const,
			'YouTube用户视频查询',
			this.loginUser.id
		);
	}

	/**
	 * 扣除用户Shorts查询积分
	 */
	private async deductCreditsForUserShorts(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'youtube_user_shorts_deduct' as const,
			'YouTube用户Shorts查询',
			this.loginUser.id
		);
	}

	/**
	 * 扣除用户播放列表查询积分
	 */
	private async deductCreditsForUserPlaylists(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'youtube_user_playlists_deduct' as const,
			'YouTube用户播放列表查询',
			this.loginUser.id
		);
	}

	/**
	 * 扣除视频详情查询积分
	 */
	private async deductCreditsForVideoInfo(): Promise<void> {
		await this.creditsService.deductUserCredit(
			this.loginUser.id,
			'youtube_video_info_deduct' as const,
			'YouTube视频详情查询',
			this.loginUser.id
		);
	}

	/**
	 * 获取用户信息
	 */
	private async fetchUserInfo(url: string) {
		return await dataToolApiRequest('/youtube/user/info', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ url }),
		});
	}

	/**
	 * 获取用户视频列表
	 */
	private async fetchUserVideos(url: string, start?: number, end?: number): Promise<YouTubeUserVideosResponse> {
		const requestBody: any = { url };
		if (start !== undefined) {
			requestBody.start = start;
		}
		if (end !== undefined) {
			requestBody.end = end;
		}

		return (await dataToolApiRequest('/youtube/user/video', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody),
		})) as YouTubeUserVideosResponse;
	}

	/**
	 * 获取视频详情
	 */
	private async fetchVideoInfo(url: string): Promise<YouTubeVideoInfoResponse> {
		return (await dataToolApiRequest('/youtube/video/info', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ url }),
		})) as YouTubeVideoInfoResponse;
	}

	/**
	 * 获取YouTube用户信息
	 */
	async getUserInfo(url: string) {
		const cacheKey = `youtube:user:info:${url}`;

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, 'youtube_user_info_deduct');
		// 尝试从缓存获取
		const cached = await this.kv.get(cacheKey);
		if (cached) {
			await this.deductCreditsForUserInfo();
			return JSON.parse(cached);
		}
		// 获取数据
		const response = await this.fetchUserInfo(url);

		// 缓存结果（缓存1小时）
		await this.kv.put(cacheKey, JSON.stringify(response), { expirationTtl: 3600 });
		// 扣除积分
		await this.deductCreditsForUserInfo();
		return response;
	}

	/**
	 * 通用的获取用户内容方法（视频或Shorts）
	 */
	private async getUserContent(form: GetYouTubeUserVideosSchema): Promise<YouTubeUserVideosResponse | YouTubeUserShortsResponse> {
		const cacheKey = `youtube:user:${form.type}:${form.url}${form.start !== undefined ? `:start_${form.start}` : ''}${
			form.end !== undefined ? `:end_${form.end}` : ''
		}`;

		// 根据内容类型确定积分类型和扣除方法
		let creditType: 'youtube_user_videos_deduct' | 'youtube_user_shorts_deduct' | 'youtube_user_playlists_deduct';
		let deductMethod: () => Promise<void>;
		let errorMessage: string;

		switch (form.type) {
			case 'videos':
				creditType = 'youtube_user_videos_deduct';
				deductMethod = this.deductCreditsForUserVideos;
				errorMessage = '获取YouTube用户视频失败';
				break;
			case 'shorts':
				creditType = 'youtube_user_shorts_deduct';
				deductMethod = this.deductCreditsForUserShorts;
				errorMessage = '获取YouTube用户Shorts失败';
				break;
			case 'playlists':
				creditType = 'youtube_user_playlists_deduct';
				deductMethod = this.deductCreditsForUserPlaylists;
				errorMessage = '获取YouTube用户播放列表失败';
				break;
			default:
				throw new Error('不支持的内容类型');
		}

		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, creditType);

		// 尝试从缓存获取
		const cached = await this.kv.get(cacheKey);
		if (cached) {
			await deductMethod.call(this);
			return JSON.parse(cached);
		}

		// 获取数据
		const response = await this.fetchUserVideos(form.url, form.start, form.end);

		// 缓存结果（缓存30分钟）
		await this.kv.put(cacheKey, JSON.stringify(response), { expirationTtl: 1800 });

		// 扣除积分
		await deductMethod.call(this);

		return response;
	}

	/**
	 * 获取YouTube用户内容（视频或Shorts）
	 */
	async getUserContentByType(form: GetYouTubeUserVideosSchema): Promise<YouTubeUserVideosResponse | YouTubeUserShortsResponse> {
		return this.getUserContent(form);
	}

	/**
	 * 获取YouTube视频详情
	 */
	async getVideoInfo(url: string): Promise<YouTubeVideoInfoResponse> {
		const cacheKey = `youtube:video:info:${url}`;
		const creditType = 'youtube_video_info_deduct';
		// 检查用户积分
		await this.creditsService.checkUserCreditByType(this.loginUser.id, creditType);
		// 尝试从缓存获取
		const cached = await this.kv.get(cacheKey);
		if (cached) {
			await this.deductCreditsForVideoInfo();
			return JSON.parse(cached);
		}

		// 扣除积分
		await this.deductCreditsForVideoInfo();

		// 获取数据
		const response = await this.fetchVideoInfo(url);

		// 缓存结果（缓存1小时）
		await this.kv.put(cacheKey, JSON.stringify(response), { expirationTtl: 3600 });

		return response;
	}
}
