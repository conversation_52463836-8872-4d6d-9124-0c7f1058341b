import { apiRequest } from './api';

export interface Product {
  id: number;
  stripe_product_id?: string;
  stripe_price_id?: string;
  name: string;
  description: string;
  price: number;
  credits: number;
  currency: string;
  is_active: number;
  created_at: string;
  updated_at: string;
}

export interface ProductListResponse {
  products: Product[];
  total: number;
}

export const productApi = {
  // 获取商品列表
  getProducts: async (name: string = '', page: number = 1, pageSize: number = 10, is_active?: number, currency?: string, credits?: number) => {
    const params: Record<string, any> = { name, page, pageSize };
    if (is_active !== undefined) params.is_active = is_active;
    if (currency !== undefined) params.currency = currency;
    if (credits !== undefined) params.credits = credits;
    
    return apiRequest<ProductListResponse>('/product/list', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  },

  // 获取商品详情
  getProductById: async (id: number) => {
    return apiRequest<Product>(`/product/${id}`, {
      method: 'GET',
    });
  },

  // 创建商品
  createProduct: async (product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
    return apiRequest<Product>('/product/create', {
      method: 'POST',
      body: JSON.stringify(product),
    });
  },

  // 更新商品
  updateProduct: async (id: number, product: Partial<Omit<Product, 'id' | 'created_at' | 'updated_at'>>) => {
    return apiRequest<Product>(`/product/${id}`, {
      method: 'PUT',
      body: JSON.stringify(product),
    });
  },
};