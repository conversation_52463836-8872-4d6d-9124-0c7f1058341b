import { eventBus } from './eventBus';

const API_BASE_URL = '/api';

export interface ApiResponse<T = any> {
	code: number;
	message: string;
	success: boolean;
	data: T | null;
	timestamp: number;
}

export class ApiError extends Error {
	code: number;
	success: boolean;
	data: null;

	constructor(code: number, message: string) {
		super(message);
		this.name = 'ApiError';
		this.code = code;
		this.success = false;
		this.data = null;
		
		// 确保堆栈跟踪正确指向这个构造函数
		if (Error.captureStackTrace) {
			Error.captureStackTrace(this, ApiError);
		}
	}
}

// Topic related interfaces
export interface Topic {
	id: string;
	name: string;
	link: string;
	view_num: number;
	type: string;
	smart: boolean;
}

export interface Note {
	id: string;
	cursor_score: string;
	title: string;
	desc: string;
	user: {
		user_id: string;
		nickname: string;
		avatar_url: string;
	};
	images_list: Array<{
		url: string;
		width: number;
		height: number;
	}>;
	interaction_info: {
		like_count: number;
		collect_count: number;
		comment_count: number;
		share_count: number;
	};
	share_info: {
		link: string;
	};
	create_time: number;
}

interface TopicSearchResponse {
	code: number;
	success: boolean;
	msg: string;
	data: {
		result: {
			success: boolean;
		};
		topic_info_dtos: Topic[];
	};
}

export interface TopicNotesResponse {
	code: number;
	success: boolean;
	msg: string;
	data: {
		items: Note[];
		has_more: boolean;
	};
}

export interface UserNotesResponse {
	code: number;
	msg: string;
	data: {
		notes: UserNote[];
		has_more: boolean;
	};
}

export interface UserNote {
	id: string;
	create_time: number;
	share_count: number;
	likes: number;
	desc: string;
	title: string;
	images_list: Array<{
		url: string;
		width: number;
		height: number;
	}>;
	user: {
		userid: string;
		nickname: string;
		images: string;
	};
	collected_count: number;
	comments_count: number;
	level: number;
	type: string;
	share_url: string;
	has_music: boolean;
	price: number;
	last_update_time: number;
	cursor: string;
	is_goods_note: boolean;
	nice_count: number;
}

async function handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
	const data = (await response.json()) as ApiResponse<T>;

	if (!response.ok) {
		const error = new ApiError(
			data.code || response.status,
			data.message || '请求失败'
		);

		// Handle unauthorized error
		if (response.status === 401) {
			eventBus.emit('unauthorized');
		}
		console.log(error, 'error');
		throw error;
	}

	return data;
}

export async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
	const token = localStorage.getItem('token');
	const headers = {
		'Content-Type': 'application/json',
		...(token ? { Authorization: `Bearer ${token}` } : {}),
		...options.headers,
	};

	const response = await fetch(`${API_BASE_URL}${endpoint}`, {
		...options,
		headers,
	});

	return handleResponse<T>(response);
}

// Auth API
export const authApi = {
	login: async (loginData: {
		loginType: 'password' | 'google' | 'api_key' | 'email';
		username?: string;
		password?: string;
		api_key?: string;
		google_token?: string;
		email_verified?: string;
	}) => {
		return apiRequest<{ token: string; user: any }>('/auth/login', {
			method: 'POST',
			body: JSON.stringify(loginData),
		});
	},

	getCurrentUser: async () => {
		return apiRequest<{ user: any }>('/auth/me');
	},

	refreshApiKey: async () => {
		return apiRequest<{ api_key: string; message: string }>('/auth/refresh-api-key', {
			method: 'POST',
		});
	},
};

// Email API
export const emailApi = {
	sendVerificationCode: async (email: string) => {
		return apiRequest<{ message: string }>('/email/send-code', {
			method: 'POST',
			body: JSON.stringify({ email }),
		});
	},


};

// XHS Business Data interfaces
export interface XhsBusinessData {
	noteId: string;
	impNum: number;
	readNum: number;
	followCnt: number;
	reportBrandName: string;
	fansNum: number;
	videoPrice: number;
	userId: string;
	picturePrice: number;
}

export interface XhsBusinessDataResponse {
	code: number;
	message: string;
	success: boolean;
	data: XhsBusinessData;
}

// Topic API
export const topicApi = {
	search: async (keyword: string) => {
		return apiRequest<TopicSearchResponse>('/xhs/topic/search', {
			method: 'POST',
			body: JSON.stringify({ keyword }),
		});
	},

	getNotes: async (pageId: string, sort: string, cursor: string = '', lastNoteId: string = '', lastNoteCt: number | null = null) => {
		return apiRequest<TopicNotesResponse>('/xhs/topic/notes', {
			method: 'POST',
			body: JSON.stringify({ pageId, sort, cursor, lastNoteId, lastNoteCt: lastNoteCt ? lastNoteCt.toString() : '' }),
		});
	},
};

// XHS API
export const xhsApi = {
	getNoteBusinessData: async (noteId: string) => {
		return apiRequest<XhsBusinessDataResponse>('/xhs/note/business', {
			method: 'POST',
			body: JSON.stringify({ noteId }),
		});
	},

	getNoteInfo: async (url: string) => {
		return apiRequest<any>('/xhs/note/info', {
			method: 'POST',
			body: JSON.stringify({ url }),
		});
	},

	searchNotes: async (params: {
		keyword: string;
		page?: number;
		sort?: string;
		noteType?: string;
		noteTime?: string;
	}) => {
		return apiRequest<any>('/xhs/note/search', {
			method: 'POST',
			body: JSON.stringify(params),
		});
	},
};

// User API
export const userApi = {
	getNotes: async (userId: string, lastCursor: string = '') => {
		return apiRequest<UserNotesResponse>('/xhs/user/notes', {
			method: 'POST',
			body: JSON.stringify({ userId, lastCursor }),
		});
	},

	getCreditRecords: async (page: number = 1, pageSize: number = 20, changeType?: string, username?: string) => {
		const params = new URLSearchParams();
		params.append('page', page.toString());
		params.append('pageSize', pageSize.toString());
		if (changeType) params.append('changeType', changeType);
		if (username) params.append('username', username);

		return apiRequest<CreditRecord[]>(`/auth/credit/record?${params.toString()}`, {
			method: 'POST',
			body: JSON.stringify({ page, pageSize, changeType, username }),
		});
	},

	getCreditTypes: async () => {
		return apiRequest<Array<{value: string, label: string}>>('/auth/credit/types');
	},
};

export interface CreditRecord {
	id: number;
	user_id: number;
	change_before: number;
	change_after: number;
	change_credit: number;
	change_type: string;
	change_reason: string;
	act_user_id: number;
	created_at: string;
	updated_at: string;
}

export interface CreditRecordResponse {
	code: number;
	success: boolean;
	message: string;
	data: CreditRecord[];
}

// Admin API
export interface AdminUser {
	id: number;
	username: string;
	api_key: string;
	role: string;
	credit: number;
	status: number;
	created_at: string;
	last_login_at: string;
	remark: string;
}

export interface AdminUserListResponse {
	users: AdminUser[];
	total: number;
}

export const adminApi = {
	getUsers: async (username: string = '', page: number = 1, pageSize: number = 10, status: number = 1) => {
		return apiRequest<AdminUserListResponse>(`/admin/users`, {
			method: 'POST',
			body: JSON.stringify({ username, page, pageSize, status }),
		});
	},

	createUser: async (username: string, password: string, role: string, credit: number = 0) => {
		return apiRequest<{ success: boolean; data: AdminUser }>('/admin/users/create', {
			method: 'POST',
			body: JSON.stringify({ username, password, role, credit }),
		});
	},

	updateUserStatus: async (userId: number, status: number) => {
		return apiRequest<{ success: boolean }>(`/admin/users/${userId}/status`, {
			method: 'PUT',
			body: JSON.stringify({ status }),
		});
	},

	updateUserCredit: async (userId: number, changeType: string, changeCredit: number, changeReason: string) => {
		return apiRequest<{ success: boolean }>(`/admin/users/${userId}/credit`, {
			method: 'PUT',
			body: JSON.stringify({ changeType, changeCredit, changeReason }),
		});
	},

	updateUserRemark: async (userId: number, remark: string) => {
		return apiRequest<{ success: boolean }>(`/admin/users/${userId}/remark`, {
			method: 'PUT',
			body: JSON.stringify({ remark }),
		});
	},
};

// Config API
export interface PublicConfig {
	stripe: {
		publishKey: string;
	};
	google: {
		clientId: string;
		clientSecret: string;
	};
}

export const configApi = {
	getPublicConfig: async () => {
		return apiRequest<PublicConfig>('/config/public');
	},
};

// YouTube API
export interface YouTubeUserInfo {
	id: string;
	name: string;
	description: string;
	subscriberCount: number;
	videoCount: number;
	thumbnail: string;
	banner?: string;
}

export interface YouTubeVideo {
	id?: string;
	title?: string;
	description?: string;
	thumbnails?: YouTubeVideoThumbil[];
	view_count?: number;
	like_count?: number;
	comment_count?: number;
	duration?: string | number;
	url?: string;
	categories?: string[];
	tags?: string[];
	channel?: {
		id?: string;
		name?: string;
	};
	channel_id?: string;
	channel_url?: string;
	age_limit?: number;
	webpage_url?: string;
	playable_in_embed?: boolean;
	live_status?: string;
	timestamp?: number;
	release_timestamp?: number;
	channel_follower_count?: number;
	uploader?: string;
	uploader_id?: string;
	uploader_url?: string;
	upload_date?: string;
	availability?: string;
	original_url?: string;
	webpage_url_basename?: string;
	webpage_url_domain?: string;
	extractor?: string;
	extractor_key?: string;
	playlist?: string | null;
	playlist_index?: number | null;
	thumbnail?: string;
	display_id?: string;
	fulltitle?: string;
	duration_string?: string;
	is_live?: boolean;
	was_live?: boolean;
	epoch?: number;
	formats?: any[];
	subtitles?: any;
}

export interface YouTubeVideoThumbil {
	url?:string;
	height?: number;
	width?: number;
}

export interface YouTubeUserInfoResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: YouTubeUserInfo;
}

export interface YouTubeUserVideosResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: YouTubeVideo[];
}

export interface YouTubeUserShortsResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: YouTubeVideo[];
}

export interface YouTubeVideoInfoResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: YouTubeVideo;
}

export const youtubeApi = {
	getUserInfo: async (url: string) => {
		return apiRequest<YouTubeUserInfoResponse>('/youtube/user/info', {
			method: 'POST',
			body: JSON.stringify({ url }),
		});
	},

	getUserVideos: async (url: string, start: number = 1, end: number = 20, type: string = 'videos') => {
		return apiRequest<YouTubeUserVideosResponse>('/youtube/user/videos', {
			method: 'POST',
			body: JSON.stringify({ url, start, end, type }),
		});
	},

	getVideoInfo: async (url: string) => {
		return apiRequest<YouTubeVideoInfoResponse>('/youtube/video/info', {
			method: 'POST',
			body: JSON.stringify({ url }),
		});
	},
};

// Douyin API
export interface DouyinVideoDetail {
	aweme_id?: string;
	desc?: string;
	author?: {
		nickname?: string;
		unique_id?: string;
		sec_uid?: string;
		avatar_thumb?: {
			url_list?: string[];
		};
	};
	statistics?: {
		digg_count?: number;
		comment_count?: number;
		share_count?: number;
		collect_count?: number;
		recommend_count?: number;
	};
	video?: {
		duration?: number;
		formatted_duration?: string;
		play_addr?: {
			url_list?: string[];
		};
	};
	create_time?: number;
	formatted_create_time?: string;
}

export interface DouyinVideoDetailResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: {
		aweme_detail?: DouyinVideoDetail;
	};
}

// Douyin Star Data API interfaces
export interface DouyinStarDataResponse {
	code?: number;
	message?: string;
	success?: boolean;
	data?: {
		video_info?: {
			vv_cnt?: number;
			finish_rate?: number;
			[key: string]: any;
		};
		[key: string]: any;
	};
}

// Douyin User Videos API interfaces
export interface DouyinUserVideo {
	aweme_id: string;
	desc: string;
	create_time: number;
	author: {
		uid: string;
		nickname: string;
		sec_uid: string;
		avatar_thumb?: {
			url_list?: string[];
		};
	};
	statistics?: {
		digg_count?: number;
		comment_count?: number;
		share_count?: number;
		collect_count?: number;
		recommend_count?: number;
	};
	video?: {
		duration?: number;
		cover?: {
			url_list?: string[];
		};
		play_addr?: {
			url_list?: string[];
		};
	};
	music?: {
		title?: string;
		author?: string;
	};
}

export interface DouyinUserVideosResponse {
	code: number;
	message: string;
	success: boolean;
	data: {
		status_code: number;
		min_cursor: number;
		max_cursor: number;
		has_more: number;
		aweme_list: DouyinUserVideo[];
	};
}

export const douyinApi = {
	getVideoDetail: async (url: string) => {
		return apiRequest<DouyinVideoDetailResponse>('/dy/detail', {
			method: 'POST',
			body: JSON.stringify({ url }),
		});
	},

	getStarData: async (url: string) => {
		return apiRequest<DouyinStarDataResponse>('/dy/xt/info', {
			method: 'POST',
			body: JSON.stringify({ url }),
		});
	},

	getUserVideos: async (url: string, maxCursor?: number) => {
		return apiRequest<DouyinUserVideosResponse>('/dy/user/videos', {
			method: 'POST',
			body: JSON.stringify({ url, maxCursor }),
		});
	},
};

// 快手API响应类型
export interface KuaishouVideoDetailResponse {
	code: number;
	message: string;
	success: boolean;
	data?: {
		result: number;
		counts: {
			fanCount: number;
			followCount: number;
			collectionCount: number;
			photoCount: number;
		};
		photo: {
			photoId: string;
			caption: string;
			userName: string;
			userId: number;
			userEid: string;
			likeCount: number;
			commentCount: number;
			forwardCount: number;
			viewCount: number;
			shareCount: number;
			duration: number;
			timestamp: number;
			width: number;
			height: number;
			mainMvUrls: Array<{
				cdn: string;
				url: string;
			}>;
			coverUrls: Array<{
				cdn: string;
				url: string;
			}>;
			headUrls: Array<{
				cdn: string;
				url: string;
			}>;
			verified: boolean;
			headUrl: string;
			userSex: string;
			photoType: string;
			photoStatus: number;
		};
	};
}

export const kuaishouApi = {
	getVideoDetail: async (url: string) => {
		return apiRequest<KuaishouVideoDetailResponse>('/kuaishou/video/detail', {
			method: 'POST',
			body: JSON.stringify({ url }),
		});
	},
	getComments: async (photoId: string, authorId: string, pcursor?: string) => {
		return apiRequest<any>('/kuaishou/comments', {
			method: 'POST',
			body: JSON.stringify({ photoId, authorId, pcursor }),
		});
	},
	getReplies: async (photoId: string, authorId: string, rootCommentId: string, pcursor?: string) => {
		return apiRequest<any>('/kuaishou/replies', {
			method: 'POST',
			body: JSON.stringify({ photoId, authorId, rootCommentId, pcursor }),
		});
	}
};
