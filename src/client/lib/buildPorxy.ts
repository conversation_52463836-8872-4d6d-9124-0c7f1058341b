// 生成签名
async function generateSignature(url: string, expire: number): Promise<string> {
	const message = `${url}&&${expire}`;
	const encoder = new TextEncoder();
	const key = await crypto.subtle.importKey('raw', encoder.encode('6m9VM01vDwl9yz7L'), { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
	const signatureBuffer = await crypto.subtle.sign('HMAC', key, encoder.encode(message));
	const signatureArray = Array.from(new Uint8Array(signatureBuffer));
	return encodeURIComponent(signatureArray.map((b) => b.toString(16).padStart(2, '0')).join(''));
}

// 构建代理URL
export async function buildProxyUrl(
	url: string,
	expireAdd: number = 60 * 60,
	platform: string = '',
	ck: string = '',
	filename: string = ''
): Promise<string> {
	const expire = Math.floor(Date.now() / 1000) + expireAdd;
	const signature = await generateSignature(url, expire);

	const baseUrl = '/api/download/proxy?';
	const params = new URLSearchParams();
	params.append('url', url);
	params.append('signature', signature);
	params.append('expire', expire.toString());
	if (platform) {
		params.append('platform', platform);
	}
	if (ck) {
		params.append('ck', ck);
	}
    if (filename) {
        params.append('filename', filename);
    }


	return baseUrl + params.toString();
}
