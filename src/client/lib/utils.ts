import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export const sleep = (min: number, max: number) => {
	const delay = Math.floor(Math.random() * (max - min + 1)) + min;
	return new Promise((resolve) => setTimeout(resolve, delay));
};


export function exportArrayToCSV(
  rows: (string | number)[][],
  filename = 'data.csv'
) {
  if (!rows || rows.length === 0) {
    alert('无数据可导出');
    return;
  }

  const csvRows = rows.map(row =>
    row
      .map(cell => {
        const value = String(cell ?? '').replace(/"/g, '""');
        return `"${value}"`; // 用双引号包裹每个字段
      })
      .join(',')
  );

  const csvString = '\uFEFF' + csvRows.join('\n'); // 添加 BOM 支持中文
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * 将秒数格式化为 xx:xx:xx 格式
 * @param duration 秒数
 * @returns 格式化后的时间字符串
 */
export const formatDuration = (duration: number): string => {
  if (!duration) {
    return "-"
  }
	const hours = Math.floor(duration / 3600);
	const minutes = Math.floor((duration % 3600) / 60);
	const seconds = Math.floor(duration % 60);

	// 如果有小时，则显示 HH:MM:SS 格式
	if (hours > 0) {
		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	}
	// 否则只显示 MM:SS 格式
	return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * 将时间戳格式化为 Y-m-d H:i:s 格式
 * @param timestamp 时间戳（秒）
 * @returns 格式化后的日期时间字符串
 */
export const formatTimestamp = (timestamp: number | string | null | undefined): string => {
  if (!timestamp) {
    return "-";
  }
	const date = new Date(Number(timestamp) * 1000);
	
	const year = date.getFullYear();
	const month = (date.getMonth() + 1).toString().padStart(2, '0');
	const day = date.getDate().toString().padStart(2, '0');
	const hours = date.getHours().toString().padStart(2, '0');
	const minutes = date.getMinutes().toString().padStart(2, '0');
	const seconds = date.getSeconds().toString().padStart(2, '0');

	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 格式化数字为可读格式（如 1000 -> "1K", 1000000 -> "1M"）
 * @param value 要格式化的数值
 * @returns 格式化后的字符串
 */
export const formatNumber = (value: number | string | null | undefined): string => {
	if (!value && value !== 0) return '-';
	const num = typeof value === 'string' ? parseFloat(value) : value;
	if (isNaN(num)) return '-';
	
	if (num >= 1000000000) {
		return (num / 1000000000).toFixed(1).replace(/\.0$/, '') + 'B';
	}
	if (num >= 1000000) {
		return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
	}
	if (num >= 1000) {
		return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
	}
	return num.toString();
};

/**
 * 解析YouTube格式的数字（如 "1.9K", "2.3M", "1.2B"）为数值
 * @param value 要解析的值
 * @returns 解析后的数值
 */
export const parseYouTubeNumber = (value: string | number | null | undefined): number => {
	if (typeof value === 'number') return value;
	if (!value || typeof value !== 'string') return 0;
	
	const cleanValue = value.toString().trim().toUpperCase();
	if (!cleanValue) return 0;
	
	// 如果是纯数字，直接返回
	const numericValue = parseFloat(cleanValue.replace(/,/g, ''));
	if (!isNaN(numericValue) && !cleanValue.match(/[KMB]/)) {
		return Math.floor(numericValue);
	}
	
	// 处理K、M、B后缀
	const match = cleanValue.match(/^([0-9.]+)([KMB]?)$/);
	if (!match) return 0;
	
	const number = parseFloat(match[1]);
	const suffix = match[2];
	
	switch (suffix) {
		case 'K':
			return Math.floor(number * 1000);
		case 'M':
			return Math.floor(number * 1000000);
		case 'B':
			return Math.floor(number * 1000000000);
		default:
			return Math.floor(number);
	}
};