import { apiRequest } from './api';

// Stripe 商品同步
export interface SyncProductRequest {
  productId: number;
}

// 创建 Checkout 会话
export interface CreateCheckoutRequest {
  productId: number;
  quantity?: number;
  successUrl?: string;
  cancelUrl?: string;
}

export interface CheckoutSession {
  id: string;
  url: string;
  status: string;
  payment_status?: string;
  amount_total: number;
  currency: string;
  expires_at: number;
}

// 创建 Payment Intent
export interface CreatePaymentIntentRequest {
  productId: number;
  quantity?: number;
}

export interface PaymentIntent {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
}

// 获取会话状态
export interface SessionStatusRequest {
  sessionId: string;
}

export interface SessionStatus {
  status: string;
  payment_status: string;
  customer_details?: {
    email: string;
    name: string;
  };
}

// Stripe 商品信息
export interface StripeProduct {
  id: string;
  name: string;
  description?: string;
  active: boolean;
  default_price?: {
    id: string;
    unit_amount: number;
    currency: string;
  };
}

export const stripeApi = {
  // 同步商品到 Stripe（管理员功能）
  syncProduct: async (data: SyncProductRequest) => {
    return apiRequest<{ success: boolean; stripe_product_id: string; stripe_price_id: string }>('/stripe/sync-product', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 创建支付会话
  createCheckout: async (data: CreateCheckoutRequest) => {
    return apiRequest<CheckoutSession>('/stripe/create-checkout', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 获取会话状态
  getPaymentStatus: async (sessionId: string) => {
    return apiRequest<SessionStatus>('/stripe/payment-status', {
      method: 'POST',
      body: JSON.stringify({ sessionId }),
    });
  },

  // 创建Payment Intent（用于嵌入式支付）
  createPaymentIntent: async (data: CreatePaymentIntentRequest) => {
    return apiRequest<PaymentIntent>('/stripe/create-payment-intent', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },
};

export default stripeApi;