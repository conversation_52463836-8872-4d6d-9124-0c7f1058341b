import { apiRequest } from './api';

export interface CreditType {
  id: number;
  type_key: string;
  type_name: string;
  credit_amount: number;
  operation_type: 'add' | 'deduct';
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CreditTypeListResponse {
  data: CreditType[];
}

export interface CreateCreditTypeRequest {
  type_key: string;
  type_name: string;
  credit_amount: number;
  operation_type: 'add' | 'deduct';
  description?: string;
}

export interface UpdateCreditTypeRequest extends CreateCreditTypeRequest {}

export const creditTypeApi = {
  // 获取积分类型列表
  getCreditTypes: async () => {
    return apiRequest<CreditTypeListResponse>('/admin/credit-types', {
      method: 'GET',
    });
  },

  // 创建积分类型
  createCreditType: async (data: CreateCreditTypeRequest) => {
    return apiRequest<CreditType>('/admin/credit-types', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 更新积分类型
  updateCreditType: async (id: number, data: UpdateCreditTypeRequest) => {
    return apiRequest<CreditType>(`/admin/credit-types/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // 删除积分类型
  deleteCreditType: async (id: number) => {
    return apiRequest<{ success: boolean }>(`/admin/credit-types/${id}`, {
      method: 'DELETE',
    });
  },
};

export default creditTypeApi;