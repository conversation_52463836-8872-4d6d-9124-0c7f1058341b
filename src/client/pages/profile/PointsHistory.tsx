import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search } from 'lucide-react';
import { userApi, CreditRecord } from '../../lib/api';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../components/ui/use-toast';

// 积分变更类型选项将从API获取

const PointsHistory: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [records, setRecords] = useState<CreditRecord[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [changeType, setChangeType] = useState('all');
  const [username, setUsername] = useState('');
  const [changeTypeOptions, setChangeTypeOptions] = useState<Array<{value: string, label: string}>>([]);
  const pageSize = 20;
  
  const fetchRecords = async () => {
    setIsLoading(true);
    try {
      const response = await userApi.getCreditRecords(
        page, 
        pageSize, 
        changeType === 'all' ? undefined : changeType, 
        user?.role === 'admin' ? username : undefined,
      );
      
      if (response.success && response.data) {
        setRecords(response.data);
        if (page === 1 && response.data.length > 0) {
          toast({
            title: "加载成功",
            description: `已加载${response.data.length}条记录`,
          });
        } else if (response.data.length === 0) {
          toast({
            title: "暂无记录",
            description: "未找到符合条件的积分记录",
          });
        }
      } else {
        console.error('Failed to fetch credit records:', response.message);
        toast({
          variant: "destructive",
          title: "加载失败",
          description: response.message || "获取积分记录失败，请稍后重试",
        });
      }
    } catch (error) {
      console.error('Error fetching credit records:', error);
      toast({
        variant: "destructive",
        title: "加载失败",
        description: "获取积分记录失败，请稍后重试",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, [page]);

  // 获取积分变更类型
  useEffect(() => {
    const fetchCreditTypes = async () => {
      try {
        const response = await userApi.getCreditTypes();
        if (response.success && response.data) {
          // 添加"全部类型"选项
          setChangeTypeOptions([{ value: 'all', label: '全部类型' }, ...response.data]);
        }
      } catch (error) {
        console.error('Failed to fetch credit types:', error);
        // 设置默认选项，以防API调用失败
        setChangeTypeOptions([{ value: 'all', label: '全部类型' }]);
      }
    };
    
    fetchCreditTypes();
  }, []);

  const handleSearch = () => {
    setPage(1);
    fetchRecords();
  };

  const handleNextPage = () => {
    if (records.length < pageSize || isLoading) return;
    setPage((p) => p + 1);
  };

  const handlePrevPage = () => {
    if (page === 1 || isLoading) return;
    setPage((p) => Math.max(1, p - 1));
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const formatChangeType = (type: string) => {
    const option = changeTypeOptions.find(option => option.value === type);
    return option ? option.label : type;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-2xl font-bold">积分变更记录</h1>
      
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">变更类型</label>
            <Select value={changeType} onValueChange={setChangeType}>
              <SelectTrigger>
                <SelectValue placeholder="选择变更类型" />
              </SelectTrigger>
              <SelectContent>
                {changeTypeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {user?.role === 'admin' && (
            <div>
              <label className="block text-sm font-medium mb-1">用户名</label>
              <Input
                placeholder="输入用户名搜索"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
          )}
          
          <div className="flex items-end">
            <Button onClick={handleSearch} className="gap-2" disabled={isLoading}>
              <Search className="h-4 w-4" />
              {isLoading ? '加载中...' : '搜索'}
            </Button>
          </div>
        </div>
      </Card>
      
      <Card>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>序号</TableHead>
                <TableHead>变更类型</TableHead>
                <TableHead>变更前</TableHead>
                <TableHead>变更后</TableHead>
                <TableHead>变更数量</TableHead>
                <TableHead>变更原因</TableHead>
                <TableHead>变更时间</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-2"></div>
                      加载中...
                    </div>
                  </TableCell>
                </TableRow>
              ) : records.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4">
                    暂无记录
                  </TableCell>
                </TableRow>
              ) : (
                records.map((record, index) => (
                  <TableRow key={index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{formatChangeType(record.change_type)}</TableCell>
                    <TableCell>{record.change_before}</TableCell>
                    <TableCell>{record.change_after}</TableCell>
                    <TableCell className={record.change_credit > 0 ? 'text-green-600' : 'text-red-600'}>
                      {record.change_credit > 0 ? `+${record.change_credit}` : record.change_credit}
                    </TableCell>
                    <TableCell>{record.change_reason}</TableCell>
                    <TableCell>{formatDateTime(record.created_at)}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        
        <div className="flex items-center justify-between p-4">
          <Button
            variant="outline"
            onClick={handlePrevPage}
            disabled={page === 1 || isLoading}
          >
            上一页
          </Button>
          <div>第 {page} 页</div>
          <Button
            variant="outline"
            onClick={handleNextPage}
            disabled={records.length < pageSize || isLoading}
          >
            下一页
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default PointsHistory;