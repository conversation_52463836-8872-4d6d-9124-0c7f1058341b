import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { CreditCard, CheckCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';

interface PaymentRecord {
  id: string;
  amount: number;
  currency: string;
  status: 'succeeded' | 'pending' | 'failed';
  created: number;
  description: string;
  payment_method?: {
    type: string;
    card?: {
      brand: string;
      last4: string;
      exp_month?: number;
      exp_year?: number;
      funding?: string;
      country?: string;
      wallet?: string;
    };
    alipay?: {
      buyer_id: string;
    };
    wechat_pay?: {
      transaction_id: string;
    };
  };
}

const PaymentHistory: React.FC = () => {
  const { user } = useAuth();
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPaymentHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/stripe/payment-history', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取付款记录失败');
      }

      const data = await response.json();
      console.log('API响应数据:', data);
      
      if (data.success && data.data && data.data.payments) {
        setPayments(data.data.payments);
      } else {
        setPayments([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取付款记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentHistory();
  }, []);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'succeeded':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="w-3 h-3 mr-1" />
            支付成功
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary">
            <RefreshCw className="w-3 h-3 mr-1" />
            处理中
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            失败
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPaymentMethodDisplay = (paymentMethod?: PaymentRecord['payment_method']) => {
    if (!paymentMethod) return '未知';
    
    switch (paymentMethod.type) {
      case 'card':
        if (paymentMethod.card) {
          const { brand, last4, wallet } = paymentMethod.card;
          let display = `${(brand || 'CARD').toUpperCase()} ****${last4 || '****'}`;
          if (wallet) {
            display += ` (${wallet.toUpperCase()})`;
          }
          return display;
        }
        return '银行卡';
      
      case 'alipay':
        return '支付宝';
      
      case 'wechat_pay':
        return '微信支付';
      
      case 'apple_pay':
        return 'Apple Pay';
      
      case 'google_pay':
        return 'Google Pay';
      
      default:
        // 将类型转换为更友好的显示名称
        const typeMap: { [key: string]: string } = {
          'card': '银行卡',
          'alipay': '支付宝',
          'wechat_pay': '微信支付',
          'apple_pay': 'Apple Pay',
          'google_pay': 'Google Pay'
        };
        return typeMap[paymentMethod.type] || paymentMethod.type;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-lg">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">付款记录</h1>
        <Button
          variant="outline"
          onClick={fetchPaymentHistory}
          className="gap-2"
          disabled={loading}
        >
          <RefreshCw className="w-4 h-4" />
          {loading ? '加载中...' : '刷新记录'}
        </Button>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="flex items-center gap-2 text-red-600">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchPaymentHistory}
              className="ml-auto"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              重试
            </Button>
          </div>
        </Card>
      )}
      
      <Card>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>交易ID</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>支付方式</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>交易时间</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-2"></div>
                      加载中...
                    </div>
                  </TableCell>
                </TableRow>
              ) : payments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <CreditCard className="w-12 h-12 text-gray-400" />
                      <span className="text-gray-500">暂无付款记录</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell className="font-mono text-xs">
                      {payment.id}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatAmount(payment.amount, payment.currency)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(payment.status)}
                    </TableCell>
                    <TableCell>
                      {getPaymentMethodDisplay(payment.payment_method)}
                    </TableCell>
                    <TableCell>
                      {payment.description || '积分充值'}
                    </TableCell>
                    <TableCell>
                      {formatDate(payment.created)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </div>
  );
};

export default PaymentHistory;