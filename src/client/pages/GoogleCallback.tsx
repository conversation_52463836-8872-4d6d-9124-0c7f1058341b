import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { configApi } from '../lib/api';

export default function GoogleCallback() {
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get('code');
      const error = searchParams.get('error');
      
      if (error) {
        // 发送错误消息给父窗口
        window.opener?.postMessage({
          type: 'GOOGLE_AUTH_ERROR',
          error: error === 'access_denied' ? '用户取消了授权' : '授权失败'
        }, window.location.origin);
        window.close();
        return;
      }
      
      if (!code) {
        window.opener?.postMessage({
          type: 'GOOGLE_AUTH_ERROR',
          error: '未获取到授权码'
        }, window.location.origin);
        window.close();
        return;
      }
      
      try {
        // 从后端获取 Google OAuth 配置
        const config = await configApi.getPublicConfig();
        
        // 使用授权码交换access_token
        const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            code,
            client_id: config.data?.google?.clientId || '',
            client_secret: config.data?.google?.clientSecret || '', // 注意：在生产环境中，client_secret应该在后端处理
            redirect_uri: window.location.origin + '/auth/google/callback',
            grant_type: 'authorization_code',
          }),
        });
        
        if (!tokenResponse.ok) {
          throw new Error('获取访问令牌失败');
        }
        
        const tokenData = await tokenResponse.json();
        
        // 发送成功消息给父窗口
        window.opener?.postMessage({
          type: 'GOOGLE_AUTH_SUCCESS',
          access_token: tokenData.access_token
        }, window.location.origin);
        
        window.close();
      } catch (err: any) {
        window.opener?.postMessage({
          type: 'GOOGLE_AUTH_ERROR',
          error: err.message || '获取访问令牌失败'
        }, window.location.origin);
        window.close();
      }
    };
    
    handleCallback();
  }, [searchParams]);
  
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">正在处理Google登录...</p>
      </div>
    </div>
  );
}