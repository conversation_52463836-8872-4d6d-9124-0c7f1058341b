import React, { useState } from 'react';
import { But<PERSON> } from '../../components/ui/button';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../../components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { RadioGroup, RadioGroupItem } from '../../components/ui/radio-group';

interface Transcript {
  id: string;
  title: string;
  uploadedAt: string;
  duration: string;
  model: string;
  status: 'processing' | 'completed' | 'failed';
  progress?: number; // 新增进度百分比
  processingTime?: string; // 新增处理时间
}

const TranscriptList: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [language, setLanguage] = useState('en');
  const [model, setModel] = useState('whisper-tiny-en');
  const [transcripts, setTranscripts] = useState<Transcript[]>([
    {
      id: '1',
      title: 'Interview with <PERSON>.mp3',
      uploadedAt: '2023-10-15 14:30',
      duration: '23:45',
      model: 'whisper-large-v3-turbo',
      status: 'completed',
      processingTime: '5分30秒' // 新增处理时间
    },
    {
      id: '2',
      title: 'Product Presentation.mp4',
      uploadedAt: '2023-10-10 09:15',
      duration: '15:20',
      model: 'whisper-large-v2',
      status: 'completed',
      processingTime: '3分15秒'
    },
    {
      id: '3',
      title: 'Marketing Strategy Meeting.wav',
      uploadedAt: '2023-10-17 11:00',
      duration: '45:10',
      model: 'whisper-tiny-en',
      status: 'processing',
      progress: 65, // 新增进度
      processingTime: '处理中' // 处理中状态显示
    }
  ]);

  const handleStartTranscription = () => {
    // Here you would handle the actual transcription process
    setOpen(false);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">字幕提取</h1>
        <Button onClick={() => setOpen(true)}>转录文件</Button>
      </div>

      <div className="bg-card rounded-lg shadow">
        <table className="w-full">
          <thead className="border-b">
            <tr>
              <th className="text-left px-4 py-3">标题</th>
              <th className="text-left px-4 py-3">上传时间</th>
              <th className="text-left px-4 py-3">时长</th>
              <th className="text-left px-4 py-3">模型</th>
              <th className="text-left px-4 py-3">处理时间</th> {/* 新增处理时间列 */}
              <th className="text-left px-4 py-3">状态</th>
            </tr>
          </thead>
          <tbody>
            {transcripts.map(transcript => (
              <tr key={transcript.id} className="border-b hover:bg-muted/50">
                <td className="px-4 py-3">{transcript.title}</td>
                <td className="px-4 py-3">{transcript.uploadedAt}</td>
                <td className="px-4 py-3">{transcript.duration}</td>
                <td className="px-4 py-3">{transcript.model}</td>
                <td className="px-4 py-3">{transcript.processingTime}</td> {/* 显示处理时间 */}
                <td className="px-4 py-3">
                  <span className={`inline-block px-2 py-1 rounded text-xs ${
                    transcript.status === 'completed' ? 'bg-green-100 text-green-800' :
                    transcript.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {
                      transcript.status === 'completed' ? '已完成' :
                      transcript.status === 'processing' ? `${transcript.progress}%` : '失败'
                    }
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>转录文件</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="file">选择 Audio / Video File</Label>
              <Input id="file" type="file" accept="audio/*,video/*" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="language">选择 Audio Language</Label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger id="language">
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">英语</SelectItem>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="ja">日语</SelectItem>
                  <SelectItem value="ko">韩语</SelectItem>
                  <SelectItem value="fr">法语</SelectItem>
                  <SelectItem value="de">德语</SelectItem>
                  <SelectItem value="es">西班牙语</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label>选择 Transcription Mode</Label>
              <RadioGroup value={model} onValueChange={setModel}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="whisper-tiny-en" id="whisper-tiny-en" />
                  <Label htmlFor="whisper-tiny-en">whisper-tiny-en</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="whisper-large-v3-turbo" id="whisper-large-v3-turbo" />
                  <Label htmlFor="whisper-large-v3-turbo">whisper-large-v3-turbo</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="whisper-large-v2" id="whisper-large-v2" />
                  <Label htmlFor="whisper-large-v2">Whisper Large-v2</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleStartTranscription}>开始转录</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TranscriptList; 