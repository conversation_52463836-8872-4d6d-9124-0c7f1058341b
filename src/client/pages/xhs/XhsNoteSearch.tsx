import React, { useState } from 'react';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Badge } from '../../components/ui/badge';
import { Search, Download, ExternalLink, Heart, MessageCircle, Share2, Eye } from 'lucide-react';
import { xhsApi } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';

interface SearchParams {
  keyword: string;
  page: number;
  sort: string;
  noteType: string;
  noteTime?: string;
}

interface NoteItem {
  note: {
    note_id: string;
    title: string;
    desc: string;
    type: string;
    cover: {
      url: string;
    };
    user: {
      nickname: string;
      avatar: string;
      userid: string;
    };
    interact_info: {
      liked_count: string;
      collected_count: string;
      comment_count: string;
      share_count: string;
    };
    time: number;
    last_update_time: number;
  };
}

interface SearchResult {
  code: number;
  message: string;
  data: {
    items: NoteItem[];
  };
}

const XhsNoteSearch: React.FC = () => {
  const { toast } = useToast();
  const [searchParams, setSearchParams] = useState<SearchParams>({
    keyword: '',
    page: 1,
    sort: 'general',
    noteType: '_0',
    noteTime: undefined,
  });
  const [searchResults, setSearchResults] = useState<NoteItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isShowingExampleData, setIsShowingExampleData] = useState(false);

  // 搜索笔记
  const handleSearch = async () => {
    if (!searchParams.keyword.trim()) {
      toast({
        title: '提示',
        description: '请输入搜索关键词',
        variant: 'destructive'
      });
      return;
    }

    setIsSearching(true);
    setIsShowingExampleData(false);

    try {
      const response = await xhsApi.searchNotes(searchParams);

      if (response.code === 0 && response.data?.items) {
        setSearchResults(response.data.items);
        toast({
          title: '搜索成功',
          description: `找到 ${response.data.items.length} 条笔记`
        });
      } else {
        throw new Error(response.message || '搜索失败');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      toast({
        title: '搜索失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsSearching(false);
    }
  };

  // 显示示例数据
  const showExampleData = async () => {
    setIsShowingExampleData(true);
    try {
      const response = await fetch('/test/data/xhs/noteSearch.json');
      const data = await response.json();
      if (data.data?.data?.items) {
        setSearchResults(data.data.data.items);
        toast({
          title: '示例数据已加载',
          description: `显示 ${data.data.data.items.length} 条示例笔记`
        });
      }
    } catch (error) {
      console.error('加载示例数据失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载示例数据',
        variant: 'destructive'
      });
    }
  };

  // 清空结果
  const handleClear = () => {
    setSearchResults([]);
    setSearchParams({
      keyword: '',
      page: 1,
      sort: 'general',
      noteType: '_0',
      noteTime: undefined,
    });
    setIsShowingExampleData(false);
  };

  // 导出数据
  const handleExport = () => {
    if (searchResults.length === 0) {
      toast({
        title: '提示',
        description: '没有数据可导出',
        variant: 'destructive'
      });
      return;
    }

    const exportData = searchResults.map(item => ({
      笔记ID: item.note.note_id,
      标题: item.note.title,
      描述: item.note.desc,
      类型: item.note.type === 'video' ? '视频' : '图文',
      作者: item.note.user.nickname,
      作者ID: item.note.user.userid,
      点赞数: item.note.interact_info.liked_count,
      收藏数: item.note.interact_info.collected_count,
      评论数: item.note.interact_info.comment_count,
      分享数: item.note.interact_info.share_count,
      发布时间: new Date(item.note.time * 1000).toLocaleString(),
      更新时间: new Date(item.note.last_update_time * 1000).toLocaleString(),
      封面图: item.note.cover?.url || '',
      作者头像: item.note.user.avatar,
    }));

    const csv = [
      Object.keys(exportData[0]).join(','),
      ...exportData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `小红书笔记搜索_${searchParams.keyword}_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();

    toast({
      title: '导出成功',
      description: `已导出 ${searchResults.length} 条笔记数据`
    });
  };

  // 格式化数字
  const formatNumber = (num: string | number) => {
    const n = typeof num === 'string' ? parseInt(num) : num;
    if (n >= 10000) {
      return (n / 10000).toFixed(1) + 'w';
    }
    return n.toString();
  };

  // 获取笔记类型标签
  const getNoteTypeLabel = (type: string) => {
    switch (type) {
      case 'video': return '视频';
      case 'normal': return '图文';
      default: return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* 搜索表单 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">关键词搜索笔记</h2>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={showExampleData} disabled={isSearching}>
                <Eye className="h-4 w-4 mr-2" />
                查看示例
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="keyword">搜索关键词 *</Label>
              <Input
                id="keyword"
                placeholder="请输入搜索关键词"
                value={searchParams.keyword}
                onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
                disabled={isSearching}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="page">页码</Label>
              <Input
                id="page"
                type="number"
                min="1"
                value={searchParams.page}
                onChange={(e) => setSearchParams(prev => ({ ...prev, page: parseInt(e.target.value) || 1 }))}
                disabled={isSearching}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sort">排序方式</Label>
              <Select
                value={searchParams.sort}
                onValueChange={(value) => setSearchParams(prev => ({ ...prev, sort: value }))}
                disabled={isSearching}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">综合</SelectItem>
                  <SelectItem value="popularity_descending">最热</SelectItem>
                  <SelectItem value="time_descending">最新</SelectItem>
                  <SelectItem value="comment_descending">最多评论</SelectItem>
                  <SelectItem value="collect_descending">最多收藏</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="noteType">笔记类型</Label>
              <Select
                value={searchParams.noteType}
                onValueChange={(value) => setSearchParams(prev => ({ ...prev, noteType: value }))}
                disabled={isSearching}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="_0">综合</SelectItem>
                  <SelectItem value="_1">视频筛选</SelectItem>
                  <SelectItem value="_2">图文筛选</SelectItem>
                  <SelectItem value="_3">直播筛选</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="noteTime">时间范围</Label>
              <Select
                value={searchParams.noteTime || 'all'}
                onValueChange={(value) => setSearchParams(prev => ({ ...prev, noteTime: value === 'all' ? undefined : value }))}
                disabled={isSearching}
              >
                <SelectTrigger>
                  <SelectValue placeholder="不限时间" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">不限时间</SelectItem>
                  <SelectItem value="一天内">一天内</SelectItem>
                  <SelectItem value="一周内">一周内</SelectItem>
                  <SelectItem value="半年内">半年内</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={handleSearch} disabled={isSearching || !searchParams.keyword.trim()}>
              {isSearching ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              {isSearching ? '搜索中...' : '开始搜索'}
            </Button>
            <Button variant="outline" onClick={handleClear} disabled={isSearching}>
              清空
            </Button>
          </div>
        </div>
      </Card>

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                搜索结果 ({searchResults.length} 条)
                {isShowingExampleData && (
                  <Badge variant="secondary" className="ml-2">示例数据</Badge>
                )}
              </h3>
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                导出数据
              </Button>
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">封面</TableHead>
                    <TableHead className="w-[200px]">标题</TableHead>
                    <TableHead className="w-[150px]">作者</TableHead>
                    <TableHead className="w-[80px]">类型</TableHead>
                    <TableHead className="w-[80px]">点赞</TableHead>
                    <TableHead className="w-[80px]">收藏</TableHead>
                    <TableHead className="w-[80px]">评论</TableHead>
                    <TableHead className="w-[80px]">分享</TableHead>
                    <TableHead className="w-[120px]">发布时间</TableHead>
                    <TableHead className="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {searchResults.map((item, index) => (
                    <TableRow key={item.note.note_id || index}>
                      <TableCell>
                        <img
                          src={item.note.cover?.url}
                          alt={item.note.title}
                          className="w-16 h-16 object-cover rounded"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder-image.png';
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-sm line-clamp-2" title={item.note.title}>
                            {item.note.title}
                          </div>
                          <div className="text-xs text-gray-500 line-clamp-2" title={item.note.desc}>
                            {item.note.desc}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <img
                            src={item.note.user.avatar}
                            alt={item.note.user.nickname}
                            className="w-8 h-8 rounded-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-avatar.png';
                            }}
                          />
                          <div className="text-sm">{item.note.user.nickname}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getNoteTypeLabel(item.note.type)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3 text-red-500" />
                          <span className="text-sm">{formatNumber(item.note.interact_info.liked_count)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <span className="text-sm">{formatNumber(item.note.interact_info.collected_count)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="h-3 w-3 text-blue-500" />
                          <span className="text-sm">{formatNumber(item.note.interact_info.comment_count)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Share2 className="h-3 w-3 text-green-500" />
                          <span className="text-sm">{formatNumber(item.note.interact_info.share_count)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(item.note.time * 1000).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`https://www.xiaohongshu.com/explore/${item.note.note_id}`, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default XhsNoteSearch;
