import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Badge } from '../../components/ui/badge';
import { Checkbox } from '../../components/ui/checkbox';
import { Search, Download, ExternalLink, Heart, MessageCircle, Share2 } from 'lucide-react';
import { xhsApi, XhsBusinessData } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import { formatTimestamp } from '../../lib/utils';

interface SearchParams {
	keyword: string;
	page: number;
	sort: string;
	noteType: string;
	noteTime?: string;
}

interface NoteItem {
	note: {
		id: string;
		title: string;
		desc: string;
		type: string;
		images_list?: Array<{
			url?: string;
			height?: number;
			url_size_large?: string;
			width?: number;
			original?: string;
		}>;
		video_info_v2?: {
			media?: {
				stream?: {
					h264?: Array<{
						master_url?: string;
						backup_urls?: string[];
						format?: string;
						quality_type?: string;
						width?: number;
						height?: number;
						duration?: number;
					}>;
					h265?: Array<{
						master_url?: string;
						backup_urls?: string[];
						format?: string;
						quality_type?: string;
						width?: number;
						height?: number;
						duration?: number;
					}>;
					h266?: Array<{
						master_url?: string;
						backup_urls?: string[];
						format?: string;
						quality_type?: string;
						width?: number;
						height?: number;
						duration?: number;
					}>;
					av1?: Array<{
						master_url?: string;
						backup_urls?: string[];
						format?: string;
						quality_type?: string;
						width?: number;
						height?: number;
						duration?: number;
					}>;
				};
			};
			image?: {
				thumbnail?: string;
				first_frame?: string;
			};
		};
		user: {
			nickname: string;
			images: string;
			red_id?: string;
			userid: string;
		};
		liked_count: number;
		nice_count?: number;
		collected_count: number;
		comments_count: number;
		shared_count: number;
		timestamp: number;
		update_time?: number;
		last_update_time?: number;
	};
	businessData?: XhsBusinessData;
	businessDataLoading?: boolean;
}

const XhsNoteSearch: React.FC = () => {
	const { toast } = useToast();
	const [searchParams, setSearchParams] = useState<SearchParams>({
		keyword: '',
		page: 1,
		sort: 'general',
		noteType: '_0',
		noteTime: undefined,
	});
	const [searchResults, setSearchResults] = useState<NoteItem[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);
	const [hasMoreData, setHasMoreData] = useState(true);
	const [selectedNotes, setSelectedNotes] = useState<string[]>([]);

	// 默认加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	// 获取视频地址 - 遍历 stream 下的编码格式，优先选择有 master_url 的
	const getVideoUrl = (note: NoteItem['note']): string => {
		if (!note.video_info_v2?.media?.stream) return '';

		const stream = note.video_info_v2.media.stream;
		const formats = ['h265', 'h264', 'h266', 'av1'] as const;

		for (const format of formats) {
			const streams = stream[format];
			if (streams && streams.length > 0) {
				for (const streamItem of streams) {
					if (streamItem.master_url) {
						return streamItem.master_url;
					}
					if (streamItem.backup_urls && streamItem.backup_urls.length > 0) {
						return streamItem.backup_urls[0];
					}
				}
			}
		}
		return '';
	};

	// 获取图片地址列表
	const getImageUrls = (note: NoteItem['note']): string[] => {
		if (!note.images_list) return [];

		return note.images_list
			.map((img) => {
				return img.url_size_large || img.url || img.original || '';
			})
			.filter((url) => url !== '');
	};

	// 获取封面图片
	const getCoverImage = (note: NoteItem['note']): string => {
		// 如果是视频，优先使用视频缩略图
		if (note.type === 'video' && note.video_info_v2?.image?.thumbnail) {
			return note.video_info_v2.image.thumbnail;
		}

		// 否则使用第一张图片
		const imageUrls = getImageUrls(note);
		return imageUrls.length > 0 ? imageUrls[0] : '';
	};

	// 格式化搜索结果数据
	const formatSearchResults = (items: any[]): NoteItem[] => {
		return items.filter((item) => {
			// 保留 model_type 为 note 的数据
			if (item.model_type === 'note') {
				return true;
			}
			// 保留 model_type 为 ads 且 ads.model_type 为 note 的数据
			if (item.model_type === 'ads' && item.ads?.model_type === 'note') {
				// 将 ads 数据结构转换为 note 数据结构
				item.note = item.ads?.note;
				return true;
			}
			// 过滤掉其他类型（如 hot_query 等）
			return false;
		});
	};

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			const response = await fetch('/test/data/xhs/noteSearch.json');
			const data = await response.json();
			if (data.data?.data?.items) {
				const formattedItems = formatSearchResults(data.data.data.items);
				console.log('formattedItems:', formattedItems);
				setSearchResults(formattedItems);
				setIsShowingExampleData(true);
			}
		} catch (error) {
			console.error('加载示例数据失败:', error);
		}
	};

	// 搜索笔记
	const handleSearch = async () => {
		if (!searchParams.keyword.trim()) {
			toast({
				title: '提示',
				description: '请输入搜索关键词',
				variant: 'destructive',
			});
			return;
		}

		setIsSearching(true);
		setIsShowingExampleData(false);

		try {
			// 重置为第一页
			const searchParamsWithPage1 = { ...searchParams, page: 1 };
			const response = await xhsApi.searchNotes(searchParamsWithPage1);

			if (response.code === 0 && response.data?.items) {
				const formattedItems = formatSearchResults(response.data.items);
				setSearchResults(formattedItems);
				setSearchParams((prev) => ({ ...prev, page: 1 }));
				setHasMoreData(response.data.items.length >= 20); // 假设每页20条数据
				toast({
					title: '搜索成功',
					description: `找到 ${formattedItems.length} 条笔记`,
				});
			} else {
				throw new Error(response.message || '搜索失败');
			}
		} catch (error) {
			console.error('搜索失败:', error);
			toast({
				title: '搜索失败',
				description: error instanceof Error ? error.message : '未知错误',
				variant: 'destructive',
			});
		} finally {
			setIsSearching(false);
		}
	};

	// 加载更多数据
	const handleLoadMore = async () => {
		if (!searchParams.keyword.trim() || !hasMoreData) {
			return;
		}

		setIsSearching(true);

		try {
			const nextPage = searchParams.page + 1;
			const searchParamsWithNextPage = { ...searchParams, page: nextPage };
			const response = await xhsApi.searchNotes(searchParamsWithNextPage);

			if (response.code === 0 && response.data?.items) {
				const formattedItems = formatSearchResults(response.data.items);
				setSearchResults((prev) => [...prev, ...formattedItems]);
				setSearchParams((prev) => ({ ...prev, page: nextPage }));
				setHasMoreData(response.data.items.length >= 20); // 假设每页20条数据
				toast({
					title: '加载成功',
					description: `新增 ${formattedItems.length} 条笔记`,
				});
			} else {
				setHasMoreData(false);
				toast({
					title: '没有更多数据',
					description: '已加载全部搜索结果',
				});
			}
		} catch (error) {
			console.error('加载更多失败:', error);
			toast({
				title: '加载失败',
				description: error instanceof Error ? error.message : '未知错误',
				variant: 'destructive',
			});
		} finally {
			setIsSearching(false);
		}
	};

	// 选择笔记
	const handleNoteSelect = (noteId: string, checked: boolean) => {
		if (checked) {
			setSelectedNotes((prev) => [...prev, noteId]);
		} else {
			setSelectedNotes((prev) => prev.filter((id) => id !== noteId));
		}
	};

	// 全选/取消全选
	const handleSelectAllNotes = (checked: boolean) => {
		if (checked) {
			setSelectedNotes(searchResults.map((item) => item.note.id));
		} else {
			setSelectedNotes([]);
		}
	};

	// 查看选中笔记的隐藏数据
	const handleViewHiddenData = async () => {
		if (selectedNotes.length === 0) {
			toast({
				variant: 'destructive',
				title: '请选择笔记',
				description: '请先选择要查看隐藏数据的笔记',
			});
			return;
		}

		const selectedNotesData = searchResults.filter((item) => selectedNotes.includes(item.note.id));
		toast({
			title: '开始获取商业数据',
			description: `正在为${selectedNotes.length}条笔记获取商业数据...`,
		});

		fetchBusinessDataForNotesAsync(selectedNotesData).catch((error: any) => {
			console.error('Failed to fetch business data:', error);
			toast({
				variant: 'destructive',
				title: '获取商业数据失败',
				description: '获取商业数据时发生错误，请重试',
			});
		});
	};

	// 批量获取商业数据
	const fetchBusinessDataForNotesAsync = async (notesList: NoteItem[]) => {
		// 首先设置选中笔记的加载状态
		const noteIds = notesList.map((item) => item.note.id);
		setSearchResults((prevNotes) =>
			prevNotes.map((prevNote) => (noteIds.includes(prevNote.note.id) ? { ...prevNote, businessDataLoading: true } : prevNote))
		);

		const batchSize = 3; // 减少并发数量以提高响应速度

		for (let i = 0; i < notesList.length; i += batchSize) {
			const batch = notesList.slice(i, i + batchSize);
			const promises = batch.map(async (item) => {
				try {
					const response = await xhsApi.getNoteBusinessData(item.note.id);
					if (response.success && response.data) {
						// 实时更新状态
						setSearchResults((prevNotes) =>
							prevNotes.map((prevNote) =>
								prevNote.note.id === item.note.id
									? {
											...prevNote,
											businessData: response.data as unknown as XhsBusinessData,
											businessDataLoading: false,
									  }
									: prevNote
							)
						);
					} else {
						// 即使失败也要更新加载状态
						setSearchResults((prevNotes) =>
							prevNotes.map((prevNote) => (prevNote.note.id === item.note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
						);
					}
				} catch (error) {
					console.error(`Failed to fetch business data for note ${item.note.id}:`, error);
					// 更新加载状态
					setSearchResults((prevNotes) =>
						prevNotes.map((prevNote) => (prevNote.note.id === item.note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
					);
				}
			});
			await Promise.all(promises);
			// 添加小延迟避免请求过于频繁
			if (i + batchSize < notesList.length) {
				await new Promise((resolve) => setTimeout(resolve, 300));
			}
		}
	};

	// 清空结果
	const handleClear = () => {
		setSearchParams({
			keyword: '',
			page: 1,
			sort: 'general',
			noteType: '_0',
			noteTime: undefined,
		});
		setHasMoreData(true);
		setSelectedNotes([]);
		// 清空后重新加载示例数据
		loadExampleData();
	};

	// 导出数据
	const handleExport = () => {
		if (searchResults.length === 0) {
			toast({
				title: '提示',
				description: '没有数据可导出',
				variant: 'destructive',
			});
			return;
		}

		const exportData = searchResults.map((item) => ({
			笔记ID: item.note.id,
			标题: item.note.title,
			描述: item.note.desc,
			类型: item.note.type === 'video' ? '视频' : '图文',
			作者: item.note.user.nickname,
			作者ID: item.note.user.userid,
			点赞数: item.note.liked_count,
			收藏数: item.note.collected_count,
			评论数: item.note.comments_count,
			分享数: item.note.shared_count,
			发布时间: formatTimestamp(item.note.timestamp),
			更新时间: formatTimestamp(item.note.last_update_time),
			封面图: getCoverImage(item.note),
			作者头像: item.note.user.images,
			图片地址: getImageUrls(item.note).join('; '),
			视频地址: getVideoUrl(item.note),
			曝光量: item.businessData?.impNum || '-',
			阅读量: item.businessData?.readNum || '-',
			粉丝量: item.businessData?.fansNum || '-',
			笔记涨粉: item.businessData?.followCnt || '-',
			图文报价: item.businessData?.picturePrice || '-',
			视频报价: item.businessData?.videoPrice || '-',
		}));

		const csv = [
			Object.keys(exportData[0]).join(','),
			...exportData.map((row) =>
				Object.values(row)
					.map((val) => `"${val}"`)
					.join(',')
			),
		].join('\n');

		const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = `小红书笔记搜索_${searchParams.keyword}_${new Date().toISOString().split('T')[0]}.csv`;
		link.click();

		toast({
			title: '导出成功',
			description: `已导出 ${searchResults.length} 条笔记数据`,
		});
	};

	// 格式化数字
	const formatNumber = (num: string | number) => {
		const n = typeof num === 'string' ? parseInt(num) : num;
		if (n >= 10000) {
			return (n / 10000).toFixed(1) + 'w';
		}
		return n.toString();
	};

	// 获取笔记类型标签
	const getNoteTypeLabel = (type: string) => {
		switch (type) {
			case 'video':
				return '视频';
			case 'normal':
				return '图文';
			default:
				return type;
		}
	};

	return (
		<div className="space-y-6">
			{/* 搜索表单 */}
			<Card className="p-6">
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="text-lg font-semibold">关键词搜索笔记</h2>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						<div className="space-y-2">
							<Label htmlFor="keyword">搜索关键词 *</Label>
							<Input
								id="keyword"
								placeholder="请输入搜索关键词"
								value={searchParams.keyword}
								onChange={(e) => setSearchParams((prev) => ({ ...prev, keyword: e.target.value }))}
								disabled={isSearching}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="page">当前页码</Label>
							<Input
								id="page"
								type="number"
								min="1"
								value={searchParams.page}
								disabled={true}
								className="bg-gray-50"
								title="页码通过'加载更多'按钮自动递增"
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="sort">排序方式</Label>
							<Select
								value={searchParams.sort}
								onValueChange={(value) => setSearchParams((prev) => ({ ...prev, sort: value }))}
								disabled={isSearching}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="general">综合</SelectItem>
									<SelectItem value="popularity_descending">最热</SelectItem>
									<SelectItem value="time_descending">最新</SelectItem>
									<SelectItem value="comment_descending">最多评论</SelectItem>
									<SelectItem value="collect_descending">最多收藏</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div className="space-y-2">
							<Label htmlFor="noteType">笔记类型</Label>
							<Select
								value={searchParams.noteType}
								onValueChange={(value) => setSearchParams((prev) => ({ ...prev, noteType: value }))}
								disabled={isSearching}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="_0">综合</SelectItem>
									<SelectItem value="_1">视频筛选</SelectItem>
									<SelectItem value="_2">图文筛选</SelectItem>
									<SelectItem value="_3">直播筛选</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div className="space-y-2">
							<Label htmlFor="noteTime">时间范围</Label>
							<Select
								value={searchParams.noteTime || 'all'}
								onValueChange={(value) => setSearchParams((prev) => ({ ...prev, noteTime: value === 'all' ? undefined : value }))}
								disabled={isSearching}
							>
								<SelectTrigger>
									<SelectValue placeholder="不限时间" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">不限时间</SelectItem>
									<SelectItem value="一天内">一天内</SelectItem>
									<SelectItem value="一周内">一周内</SelectItem>
									<SelectItem value="半年内">半年内</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className="flex gap-2">
						<Button onClick={handleSearch} disabled={isSearching || !searchParams.keyword.trim()}>
							{isSearching ? (
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							) : (
								<Search className="h-4 w-4 mr-2" />
							)}
							{isSearching ? '搜索中...' : '开始搜索'}
						</Button>
						<Button variant="outline" onClick={handleClear} disabled={isSearching}>
							清空
						</Button>
					</div>
				</div>
			</Card>

			{/* 搜索结果 */}
			{searchResults.length > 0 && (
				<Card className="p-6">
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<h3 className="text-lg font-semibold">
								已加载 ({searchResults.length} 条)
								{isShowingExampleData && (
									<Badge variant="secondary" className="ml-2">
										示例数据
									</Badge>
								)}
							</h3>
							<div className="flex gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={handleViewHiddenData}
									disabled={isShowingExampleData || selectedNotes.length === 0}
								>
									查看选中笔记隐藏数据 ({selectedNotes.length})
								</Button>
								<Button variant="outline" size="sm" onClick={handleExport}>
									<Download className="h-4 w-4 mr-2" />
									导出数据
								</Button>
							</div>
						</div>

						<div className="overflow-x-auto">
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead className="w-[50px]">
											<Checkbox
												checked={selectedNotes.length === searchResults.length && searchResults.length > 0}
												onCheckedChange={handleSelectAllNotes}
												disabled={isShowingExampleData}
											/>
										</TableHead>
										<TableHead className="w-[100px]">封面</TableHead>
										<TableHead className="w-[200px]">标题</TableHead>
										<TableHead className="w-[150px]">作者</TableHead>
										<TableHead className="w-[80px]">类型</TableHead>
										<TableHead className="w-[80px]">点赞</TableHead>
										<TableHead className="w-[80px]">收藏</TableHead>
										<TableHead className="w-[80px]">评论</TableHead>
										<TableHead className="w-[80px]">分享</TableHead>
										<TableHead className="w-[130px]">发布时间</TableHead>
										<TableHead className="w-[80px]">曝光量</TableHead>
										<TableHead className="w-[80px]">阅读量</TableHead>
										<TableHead className="w-[80px]">粉丝量</TableHead>
										<TableHead className="w-[80px]">笔记涨粉</TableHead>
										<TableHead className="w-[100px]">报价</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{searchResults.map((item, index) => (
										<TableRow key={item.note.id || index}>
											<TableCell>
												<Checkbox
													checked={selectedNotes.includes(item.note.id)}
													onCheckedChange={(checked) => handleNoteSelect(item.note.id, checked as boolean)}
													disabled={isShowingExampleData}
												/>
											</TableCell>
											<TableCell>
												<img
													src={getCoverImage(item.note)}
													alt={item.note.title}
													className="w-16 h-16 object-cover rounded"
													onError={(e) => {
														(e.target as HTMLImageElement).src = '/placeholder-image.png';
													}}
												/>
											</TableCell>
											<TableCell>
												<div
													className="space-y-1 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
													onClick={() => window.open(`https://www.xiaohongshu.com/explore/${item.note.id}`, '_blank')}
													title="点击查看笔记详情"
												>
													<div className="font-medium text-sm line-clamp-2 text-blue-600 hover:text-blue-800" title={item.note.title}>
														{item.note.title}
													</div>
													<div className="text-xs text-gray-500 line-clamp-2" title={item.note.desc}>
														{item.note.desc}
													</div>
												</div>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-2">
													<img
														src={item.note.user.images}
														alt={item.note.user.nickname}
														className="w-8 h-8 rounded-full"
														onError={(e) => {
															(e.target as HTMLImageElement).src = '/placeholder-avatar.png';
														}}
													/>
													<div className="text-sm">{item.note.user.nickname}</div>
												</div>
											</TableCell>
											<TableCell>
												<Badge variant="outline">{getNoteTypeLabel(item.note.type)}</Badge>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-1">
													<Heart className="h-3 w-3 text-red-500" />
													<span className="text-sm">{formatNumber(item.note.liked_count)}</span>
												</div>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-1">
													<span className="text-sm">{formatNumber(item.note.collected_count)}</span>
												</div>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-1">
													<MessageCircle className="h-3 w-3 text-blue-500" />
													<span className="text-sm">{formatNumber(item.note.comments_count)}</span>
												</div>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-1">
													<Share2 className="h-3 w-3 text-green-500" />
													<span className="text-sm">{formatNumber(item.note.shared_count)}</span>
												</div>
											</TableCell>
											<TableCell>
												<div className="text-sm">{formatTimestamp(item.note.timestamp)}</div>
											</TableCell>
											<TableCell>
												{item.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
												) : (
													<div className="text-sm">{item.businessData?.impNum ? formatNumber(item.businessData.impNum) : '-'}</div>
												)}
											</TableCell>
											<TableCell>
												{item.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
												) : (
													<div className="text-sm">{item.businessData?.readNum ? formatNumber(item.businessData.readNum) : '-'}</div>
												)}
											</TableCell>
											<TableCell>
												{item.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
												) : (
													<div className="text-sm">{item.businessData?.fansNum ? formatNumber(item.businessData.fansNum) : '-'}</div>
												)}
											</TableCell>
											<TableCell>
												{item.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
												) : (
													<div className="text-sm">{item.businessData?.followCnt ? formatNumber(item.businessData.followCnt) : '-'}</div>
												)}
											</TableCell>
											<TableCell className="whitespace-pre-line">
												{item.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
												) : item.businessData ? (
													<div className="text-sm">
														<div>图文：{item.businessData.picturePrice ? `¥${item.businessData.picturePrice}` : '-'}</div>
														<div>视频：{item.businessData.videoPrice ? `¥${item.businessData.videoPrice}` : '-'}</div>
													</div>
												) : (
													<div className="text-sm">
														<div>图文：-</div>
														<div>视频：-</div>
													</div>
												)}
											</TableCell>
										</TableRow>
									))}
								</TableBody>
							</Table>
						</div>

						{/* 加载更多按钮 */}
						{!isShowingExampleData && searchParams.keyword && hasMoreData && (
							<div className="flex justify-center mt-4">
								<Button variant="outline" onClick={handleLoadMore} disabled={isSearching} className="min-w-[120px]">
									{isSearching ? (
										<>
											<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
											加载中...
										</>
									) : (
										'加载更多'
									)}
								</Button>
							</div>
						)}

						{/* 没有更多数据提示 */}
						{!isShowingExampleData && searchParams.keyword && !hasMoreData && searchResults.length > 0 && (
							<div className="text-center text-gray-500 mt-4">已加载全部搜索结果</div>
						)}
					</div>
				</Card>
			)}
		</div>
	);
};

export default XhsNoteSearch;
