import React, { useState, useRef, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Textarea } from '../../components/ui/textarea';
import { Label } from '../../components/ui/label';
import { Badge } from '../../components/ui/badge';
import { Upload, Download, Search, X, Heart, MessageCircle, Bookmark, AlertCircle, ExternalLink } from 'lucide-react';
import { useToast } from '../../components/ui/use-toast';
import { xhsApi, XhsBusinessData } from '../../lib/api';
import * as XLSX from 'xlsx';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Checkbox } from '../../components/ui/checkbox';

interface XhsNoteInfo {
  id: string;
  url: string;
  title?: string;
  desc?: string;
  type?: string;
  user?: {
    userId: string;
    nickname: string;
    avatar: string;
  };
  imageList?: string[];
  videoUrl?: string;
  likeCount?: number;
  collectCount?: number;
  commentCount?: number;
  shareCount?: number;
  time?: number;
  lastUpdateTime?: number;
  tagList?: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  formatted_create_time?: string;
  loading?: boolean;
  error?: string;
  businessData?: XhsBusinessData;
  businessDataLoading?: boolean;
}

const XhsBatchExtract: React.FC = () => {
  const [noteUrls, setNoteUrls] = useState('');
  const [noteInfos, setNoteInfos] = useState<XhsNoteInfo[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isShowingExampleData, setIsShowingExampleData] = useState(true);
  const [selectedNotes, setSelectedNotes] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // 加载示例数据
  const loadExampleData = async () => {
    try {
      const response = await fetch('/test/data/xhs/noteDetail.json');
      const data = await response.json();

      // 解析示例数据 - 根据最新的数据结构
      const noteData = data.data.data[0].note_list[0];



      const exampleNoteInfo: XhsNoteInfo = {
        id: noteData.id || 'example_note_id',
        url: noteData.share_info?.link || 'https://www.xiaohongshu.com/discovery/item/example',
        title: noteData.title || '示例小红书笔记',
        desc: noteData.desc || '',
        type: noteData.type || 'note',
        user: {
          userId: noteData.user?.userid || noteData.user?.id || '',
          nickname: noteData.user?.nickname || noteData.user?.name || '',
          avatar: noteData.user?.image || ''
        },
        imageList: noteData.images_list?.map((img: any) => img.url || img.original) || [],
        videoUrl: noteData.video?.url || noteData.video?.link || undefined,
        likeCount: noteData.liked_count || 0,
        collectCount: noteData.collected_count || 0,
        commentCount: noteData.comments_count || 0,
        shareCount: noteData.share_count || 0,
        time: noteData.time,
        lastUpdateTime: noteData.last_update_time,
        formatted_create_time: noteData.time ? new Date(noteData.time * 1000).toLocaleDateString('zh-CN') : '',
        loading: false,
        businessDataLoading: false,
        tagList: extractTagsFromDesc(noteData.desc || '')
      };

      setNoteInfos([exampleNoteInfo]);

      toast({
        title: '示例数据已加载',
        description: '已加载 1 条示例笔记数据',
      });
    } catch (error) {
      console.error('Failed to load example data:', error);
      toast({
        variant: 'destructive',
        title: '加载示例数据失败',
        description: '无法加载示例数据，请检查文件是否存在',
      });
    }
  };

  // 提取标签信息（从描述中解析hashtag）
  const extractTagsFromDesc = (desc: string): Array<{id: string; name: string; type: string}> => {
    const tagRegex = /#([^#\s]+)/g;
    const tags: Array<{id: string; name: string; type: string}> = [];
    let match: RegExpExecArray | null;
    let index = 0;
    while ((match = tagRegex.exec(desc)) !== null) {
      tags.push({
        id: `tag_${index++}`,
        name: match[1],
        type: 'topic'
      });
    }
    return tags;
  };

  // 组件挂载时加载示例数据
  useEffect(() => {
    loadExampleData();
  }, []);

  // 处理笔记选择
  const handleNoteSelect = (noteId: string, checked: boolean) => {
    if (checked) {
      setSelectedNotes((prev) => [...prev, noteId]);
    } else {
      setSelectedNotes((prev) => prev.filter((id) => id !== noteId));
    }
  };

  // 处理全选
  const handleSelectAllNotes = (checked: boolean) => {
    if (checked) {
      setSelectedNotes(noteInfos.map((note) => note.id));
    } else {
      setSelectedNotes([]);
    }
  };

  // 查看选中笔记的隐藏数据
  const handleViewHiddenData = async () => {
    if (selectedNotes.length === 0) {
      toast({
        variant: 'destructive',
        title: '请选择笔记',
        description: '请先选择要查看隐藏数据的笔记',
      });
      return;
    }

    const selectedNotesData = noteInfos.filter((note) => selectedNotes.includes(note.id));
    toast({
      title: '开始获取商业数据',
      description: `正在为${selectedNotes.length}条笔记获取商业数据...`,
    });

    fetchBusinessDataForNotesAsync(selectedNotesData).catch((error) => {
      console.error('Failed to fetch business data:', error);
      toast({
        variant: 'destructive',
        title: '获取商业数据失败',
        description: '获取商业数据时发生错误，请重试',
      });
    });
  };

  // 批量获取商业数据
  const fetchBusinessDataForNotesAsync = async (notesList: XhsNoteInfo[]) => {
    // 首先设置选中笔记的加载状态
    const noteIds = notesList.map((note) => note.id);
    setNoteInfos((prevNotes) =>
      prevNotes.map((prevNote) => (noteIds.includes(prevNote.id) ? { ...prevNote, businessDataLoading: true } : prevNote))
    );

    const batchSize = 3; // 减少并发数量以提高响应速度

    for (let i = 0; i < notesList.length; i += batchSize) {
      const batch = notesList.slice(i, i + batchSize);
      const promises = batch.map(async (note) => {
        try {
          const response = await xhsApi.getNoteBusinessData(note.id);
          if (response.success && response.data) {
            // 实时更新状态
            setNoteInfos((prevNotes) =>
              prevNotes.map((prevNote) =>
                prevNote.id === note.id
                  ? {
                      ...prevNote,
                      businessData: response.data as unknown as XhsBusinessData,
                      businessDataLoading: false,
                    }
                  : prevNote
              )
            );
          } else {
            // 即使失败也要更新加载状态
            setNoteInfos((prevNotes) =>
              prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
            );
          }
        } catch (error) {
          console.error(`Failed to fetch business data for note ${note.id}:`, error);
          // 更新加载状态
          setNoteInfos((prevNotes) =>
            prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
          );
        }
      });
      await Promise.all(promises);
      // 添加小延迟避免请求过于频繁
      if (i + batchSize < notesList.length) {
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    }
  };

  // 解析笔记URL
  const parseNoteUrls = (text: string): string[] => {
    const lines = text.split('\n').filter(line => line.trim());
    const urls: string[] = [];

    for (const line of lines) {
      // 匹配小红书URL (包括官方域名和短链接)
      const xhsUrlRegex = /https?:\/\/(?:(?:www\.)?xiaohongshu\.com\/[^\s]*|xhslink\.com\/[^\s]*)/g;
      const matches = line.match(xhsUrlRegex);
      if (matches) {
        urls.push(...matches);
      }
    }

    return [...new Set(urls)]; // 去重
  };

  // 获取单个笔记信息
  const fetchNoteInfo = async (url: string, index: number) => {
    try {
      setNoteInfos(prev => prev.map((info, i) =>
        i === index ? { ...info, loading: true, error: undefined } : info
      ));

      const response = await xhsApi.getNoteInfo(url);

      if (response.success && response.data?.data) {
        const detail = response.data;

        // 处理不同的数据结构
        let noteData = detail;

        // 如果是嵌套结构（类似示例数据），提取note_list中的第一个
        if (detail.data && Array.isArray(detail.data) && detail.data[0]?.note_list) {
          noteData = detail.data[0].note_list[0];
        }

        console.info(noteData);

        const noteInfo: XhsNoteInfo = {
          id: noteData.noteId || noteData.id || `xhs_${Date.now()}_${index}`,
          url,
          title: noteData.title || '无标题',
          desc: noteData.desc || '',
          type: noteData.type || 'note',
          user: {
            userId: noteData.user?.userid || noteData.user?.id || '',
            nickname: noteData.user?.nickname || noteData.user?.name || '',
            avatar: noteData.user?.image || noteData.user?.avatar || ''
          },
          imageList: noteData.imageList || noteData.images_list?.map((img: any) => img.url || img.original) || [],
          videoUrl: noteData.videoUrl || noteData.video?.url || noteData.video?.link,
          likeCount: noteData.likeCount || noteData.liked_count || 0,
          collectCount: noteData.collectCount || noteData.collected_count || 0,
          commentCount: noteData.commentCount || noteData.comments_count || 0,
          shareCount: noteData.shareCount || noteData.share_count || 0,
          time: noteData.time,
          lastUpdateTime: noteData.lastUpdateTime || noteData.last_update_time,
          tagList: noteData.tagList || extractTagsFromDesc(noteData.desc || ''),
          formatted_create_time: noteData.time ? new Date(noteData.time * 1000).toLocaleDateString('zh-CN') : '',
          loading: false,
          businessDataLoading: false
        };

        setNoteInfos(prev => prev.map((info, i) =>
          i === index ? noteInfo : info
        ));

      } else {
        throw new Error(response.message || '获取笔记信息失败');
      }
    } catch (error) {
      console.error('获取笔记信息失败:', error);
      setNoteInfos(prev => prev.map((info, i) =>
        i === index ? {
          ...info,
          loading: false,
          error: error instanceof Error ? error.message : '获取失败'
        } : info
      ));
    }
  };

  // 批量提取笔记信息
  const handleExtract = async () => {
    const urls = parseNoteUrls(noteUrls);

    if (urls.length === 0) {
      toast({
        title: '提示',
        description: '请输入有效的小红书笔记链接',
        variant: 'destructive'
      });
      return;
    }

    if (urls.length > 20) {
      toast({
        title: '提示',
        description: '一次最多处理20个笔记链接',
        variant: 'destructive'
      });
      return;
    }

    setIsExtracting(true);
    setIsShowingExampleData(false);
    setSelectedNotes([]);

    // 初始化笔记信息
    const initialNoteInfos: XhsNoteInfo[] = urls.map((url, index) => ({
      id: `temp_${index}`,
      url,
      loading: true,
      businessDataLoading: false
    }));
    setNoteInfos(initialNoteInfos);

    // 并发获取笔记信息
    const promises = urls.map((url, index) => fetchNoteInfo(url, index));

    try {
      await Promise.allSettled(promises);

      toast({
        title: '提取完成',
        description: `成功处理 ${urls.length} 个笔记链接`
      });
    } catch (error) {
      console.error('批量提取失败:', error);
      toast({
        title: '提取失败',
        description: '部分笔记信息获取失败，请检查链接是否有效',
        variant: 'destructive'
      });
    } finally {
      setIsExtracting(false);
    }
  };

  // 清空结果
  const handleClear = () => {
    setNoteInfos([]);
    setNoteUrls('');
    setSelectedNotes([]);
  };

  // 移除单个笔记
  const handleRemoveNote = (index: number) => {
    setNoteInfos(prev => prev.filter((_, i) => i !== index));
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w';
    }
    return num.toString();
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

        // 提取URL列（假设在第一列）
        const urls = jsonData
          .slice(1) // 跳过标题行
          .map(row => row[0])
          .filter(url => url && typeof url === 'string' && url.trim())
          .join('\n');

        setNoteUrls(prev => prev ? `${prev}\n${urls}` : urls);

        toast({
          title: '文件上传成功',
          description: `已导入 ${urls.split('\n').length} 个链接`
        });
      } catch (error) {
        console.error('文件解析失败:', error);
        toast({
          title: '文件解析失败',
          description: '请确保文件格式正确',
          variant: 'destructive'
        });
      }
    };
    reader.readAsArrayBuffer(file);
  };

  // 下载模板
  const downloadTemplate = () => {
    const templateData = [
      ['笔记链接'],
      ['https://www.xiaohongshu.com/explore/xxxxxx'],
      ['https://www.xiaohongshu.com/discovery/item/xxxxxx']
    ];

    const ws = XLSX.utils.aoa_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '小红书笔记链接模板');
    XLSX.writeFile(wb, '小红书笔记链接模板.xlsx');
  };

  // 导出结果
  const handleExport = () => {
    if (noteInfos.length === 0) {
      toast({
        title: '提示',
        description: '没有可导出的数据',
        variant: 'destructive'
      });
      return;
    }

    const exportData = noteInfos.map(info => ({
      '笔记ID': info.id,
      '笔记链接': info.url,
      '笔记标题': info.title || '',
      '笔记描述': info.desc || '',
      '笔记类型': info.type || '',
      '作者昵称': info.user?.nickname || '',
      '作者ID': info.user?.userId || '',
      '点赞数': info.likeCount || 0,
      '收藏数': info.collectCount || 0,
      '评论数': info.commentCount || 0,
      '分享数': info.shareCount || 0,
      '曝光量': info.businessData?.impNum || '-',
      '阅读量': info.businessData?.readNum || '-',
      '粉丝量': info.businessData?.fansNum || '-',
      '笔记涨粉数': info.businessData?.followCnt || '-',
      '图文报价': info.businessData?.picturePrice || '-',
      '视频报价': info.businessData?.videoPrice || '-',
      '图片数量': info.imageList?.length || 0,
      '图片链接': info.imageList?.join('; ') || '',
      '是否有视频': info.videoUrl ? '是' : '否',
      '视频链接': info.videoUrl || '',
      '标签数量': info.tagList?.length || 0,
      '标签内容': info.tagList?.map(tag => tag.name).join('; ') || '',
      '发布日期': info.formatted_create_time || '',
      '状态': info.error ? '失败' : '成功',
      '错误信息': info.error || ''
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    const sheetName = '小红书笔记信息';
    const fileName = `小红书笔记信息_${new Date().toISOString().slice(0, 10)}.xlsx`;

    XLSX.utils.book_append_sheet(wb, ws, sheetName);
    XLSX.writeFile(wb, fileName);

    toast({
      title: '导出成功',
      description: '笔记信息已导出到Excel文件'
    });
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* 输入区域 */}
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="note-urls" className="text-base font-medium">
                笔记链接输入
              </Label>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={downloadTemplate} disabled={isExtracting}>
                  <Download className="h-4 w-4 mr-2" />
                  下载模板
                </Button>
                <Button variant="outline" size="sm" onClick={() => fileInputRef.current?.click()} disabled={isExtracting}>
                  <Upload className="h-4 w-4 mr-2" />
                  导入Excel
                </Button>
                <input ref={fileInputRef} type="file" accept=".xlsx,.xls" onChange={handleFileUpload} className="hidden" />
              </div>
            </div>

            <Textarea
              id="note-urls"
              placeholder={`请输入小红书笔记链接，每行一个...

示例：
https://www.xiaohongshu.com/explore/xxxxxx
https://www.xiaohongshu.com/discovery/item/xxxxxx
http://xhslink.com/m/xxxxxx`}
              value={noteUrls}
              onChange={(e) => setNoteUrls(e.target.value)}
              className="min-h-[120px]"
              disabled={isExtracting}
            />
            <div className="flex justify-end mt-4 gap-2">
              <Button onClick={handleExtract} disabled={isExtracting || !noteUrls.trim()}>
                <Search className="w-4 h-4 mr-2" />
                {isExtracting ? '提取中...' : '提取'}
              </Button>
              <Button variant="outline" onClick={handleClear} disabled={isExtracting || (!noteUrls.trim() && noteInfos.length === 0)}>
                清空
              </Button>
            </div>
          </div>
        </Card>

        {/* 结果区域 */}
        {noteInfos.length > 0 && (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">提取结果 ({noteInfos.length})</h3>
                {isShowingExampleData && (
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                    示例数据
                  </span>
                )}
              </div>
              {noteInfos.length > 0 && (
                <div className="flex gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={isShowingExampleData ? undefined : handleViewHiddenData}
                        disabled={isShowingExampleData || selectedNotes.length === 0}
                        variant="outline"
                      >
                        查看选中笔记隐藏数据 ({selectedNotes.length})
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isShowingExampleData ? '示例数据不支持查看隐藏数据' : '查看选中笔记的隐藏数据'}</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        onClick={isShowingExampleData ? undefined : handleExport}
                        disabled={isShowingExampleData}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        导出结果
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isShowingExampleData ? '示例数据不支持导出' : '导出笔记数据'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              )}
            </div>

            {/* 笔记表格 */}
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={selectedNotes.length === noteInfos.length && noteInfos.length > 0}
                        onCheckedChange={handleSelectAllNotes}
                      />
                    </TableHead>
                    <TableHead className="w-[100px]">状态</TableHead>
                    <TableHead className="w-[120px]">作者</TableHead>
                    <TableHead className="w-[180px]">标题/描述</TableHead>
                    <TableHead className="w-[80px]">类型</TableHead>
                    <TableHead className="w-[100px]">媒体</TableHead>
                    <TableHead className="w-[120px]">标签</TableHead>
                    <TableHead className="w-[80px]">点赞</TableHead>
                    <TableHead className="w-[80px]">收藏</TableHead>
                    <TableHead className="w-[80px]">评论</TableHead>
                    <TableHead className="w-[80px]">曝光</TableHead>
                    <TableHead className="w-[80px]">阅读</TableHead>
                    <TableHead className="w-[80px]">粉丝</TableHead>
                    <TableHead className="w-[80px]">笔记涨粉</TableHead>
                    <TableHead className="w-[100px]">报价</TableHead>
                    <TableHead className="w-[120px]">发布日期</TableHead>
                    <TableHead className="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {noteInfos.map((note, index) => (
                    <TableRow key={note.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedNotes.includes(note.id)}
                          onCheckedChange={(checked) => handleNoteSelect(note.id, checked as boolean)}
                          aria-label={`Select note ${note.id}`}
                          disabled={note.loading || note.businessDataLoading}
                        />
                      </TableCell>
                      <TableCell>
                        {note.loading ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            <span className="text-xs text-gray-500">加载中</span>
                          </div>
                        ) : note.error ? (
                          <div className="flex items-center gap-1">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <span className="text-xs text-red-600">失败</span>
                          </div>
                        ) : (
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            成功
                          </Badge>
                        )}
                      </TableCell>

                      <TableCell>
                        {note.user && (
                          <div className="flex items-center gap-2">
                            <img
                              src={note.user.avatar}
                              alt={note.user.nickname}
                              className="w-8 h-8 rounded-full"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/32x32?text=U';
                              }}
                            />
                            <div className="min-w-0">
                              <div className="text-sm font-medium truncate">{note.user.nickname}</div>
                              <div className="text-xs text-gray-500 truncate">{note.user.userId}</div>
                            </div>
                          </div>
                        )}
                      </TableCell>

                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-sm line-clamp-1" title={note.title || '无标题'}>
                            {(note.title || '无标题').length > 20 ? (note.title || '无标题').substring(0, 20) + '...' : (note.title || '无标题')}
                          </div>
                          {note.desc && (
                            <div className="text-xs text-gray-600 line-clamp-1" title={note.desc}>
                              {note.desc.length > 30 ? note.desc.substring(0, 30) + '...' : note.desc}
                            </div>
                          )}
                          {note.error && (
                            <div className="text-xs text-red-600">
                              {note.error}
                            </div>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        {note.type && (
                          <Badge variant="outline" className="text-xs">
                            {note.type}
                          </Badge>
                        )}
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          {note.imageList && note.imageList.length > 0 && (
                            <div className="flex items-center gap-1">
                              <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                                <span className="text-xs text-blue-600">{note.imageList.length}</span>
                              </div>
                              <span className="text-xs text-gray-500">图</span>
                            </div>
                          )}
                          {note.videoUrl && (
                            <div className="flex items-center gap-1">
                              <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                                <ExternalLink className="w-3 h-3 text-green-600" />
                              </div>
                              <span className="text-xs text-gray-500">视频</span>
                            </div>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        {note.tagList && note.tagList.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {note.tagList.slice(0, 2).map((tag) => (
                              <Badge key={tag.id} variant="secondary" className="text-xs">
                                #{tag.name}
                              </Badge>
                            ))}
                            {note.tagList.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{note.tagList.length - 2}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <span className="text-xs text-gray-500">-</span>
                        )}
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3 text-red-500" />
                          <span className="text-sm">{formatNumber(note.likeCount || 0)}</span>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Bookmark className="h-3 w-3 text-yellow-500" />
                          <span className="text-sm">{formatNumber(note.collectCount || 0)}</span>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="h-3 w-3 text-blue-500" />
                          <span className="text-sm">{formatNumber(note.commentCount || 0)}</span>
                        </div>
                      </TableCell>

                      <TableCell>
                        {note.businessDataLoading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
                        ) : note.businessData?.impNum ? (
                          formatNumber(note.businessData.impNum)
                        ) : (
                          '-'
                        )}
                      </TableCell>

                      <TableCell>
                        {note.businessDataLoading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
                        ) : note.businessData?.readNum ? (
                          formatNumber(note.businessData.readNum)
                        ) : (
                          '-'
                        )}
                      </TableCell>

                      <TableCell>
                        {note.businessDataLoading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
                        ) : note.businessData?.fansNum ? (
                          formatNumber(note.businessData.fansNum)
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell>
                        {note.businessDataLoading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
                        ) : note.businessData?.followCnt ? (
                          formatNumber(note.businessData.followCnt)
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell className="whitespace-pre-line">
                        {note.businessDataLoading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        ) : note.businessData ? (
                          <div className="text-sm">
                            <div>图文：{note.businessData.picturePrice ? `¥${note.businessData.picturePrice}` : '-'}</div>
                            <div>视频：{note.businessData.videoPrice ? `¥${note.businessData.videoPrice}` : '-'}</div>
                          </div>
                        ) : (
                          <div className="text-sm">
                            <div>图文：-</div>
                            <div>视频：-</div>
                          </div>
                        )}
                      </TableCell>

                      <TableCell>
                        <div className="text-xs text-gray-600">
                          {note.formatted_create_time || '-'}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(note.url, '_blank')}
                            className="h-8 w-8 p-0"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveNote(index)}
                            disabled={isShowingExampleData}
                            className="h-8 w-8 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        )}

        {/* 显示处理中的笔记占位符 */}
        {isExtracting && noteInfos.some(note => note.loading) && (
          <Card className="p-4">
            <div className="text-sm text-gray-600">
              <div className="flex items-center gap-2 mb-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                正在实时获取笔记信息...
              </div>
              <div className="text-xs">
                已处理: {noteInfos.filter(n => !n.loading && !n.error).length} / {noteInfos.length}
              </div>
            </div>
          </Card>
        )}
      </div>
    </TooltipProvider>
  );
};

export default XhsBatchExtract;
