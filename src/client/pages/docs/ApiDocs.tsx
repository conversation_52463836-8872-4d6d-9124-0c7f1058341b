import React, { useState } from 'react';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import {
  BookOpen,
  Copy,
  Check,
  Key,
  ArrowRight,
  ExternalLink,
  Menu,
  X,
  Download,
  Loader2
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { ApiCategory, ApiEndpoint, apiEndpoints } from '.';

const ApiDocs: React.FC = () => {
  const { user } = useAuth();
  const [copiedEndpoint, setCopiedEndpoint] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string>('auth');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loadedExamples, setLoadedExamples] = useState<Record<string, any>>({});
  const [loadingExamples, setLoadingExamples] = useState<Record<string, boolean>>({});

  // 获取当前域名
  const getCurrentDomain = () => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return 'https://data.snappdown.com'; // 默认域名
  };

  const copyToClipboard = async (text: string, endpoint: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedEndpoint(endpoint);
      setTimeout(() => setCopiedEndpoint(null), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 切换到指定部分
  const switchToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    setSidebarOpen(false); // 移动端点击后关闭侧边栏
  };



  // 加载示例数据
  const loadExampleData = async (endpoint: ApiEndpoint) => {
    const testDataPath = endpoint.testDataPath;
    if (!testDataPath) {
      console.warn(`No test data found for endpoint: ${endpoint.path}`);
      return;
    }

    const cacheKey = endpoint.path;

    // 如果已经加载过，直接返回
    if (loadedExamples[cacheKey]) {
      return;
    }

    // 设置加载状态
    setLoadingExamples(prev => ({ ...prev, [cacheKey]: true }));

    try {
      const response = await fetch(testDataPath);
      if (!response.ok) {
        throw new Error(`Failed to load test data: ${response.statusText}`);
      }

      const data = await response.json();

      // 保存加载的数据
      setLoadedExamples(prev => ({ ...prev, [cacheKey]: data }));
    } catch (error) {
      console.error(`Error loading test data for ${endpoint.path}:`, error);
    } finally {
      // 清除加载状态
      setLoadingExamples(prev => ({ ...prev, [cacheKey]: false }));
    }
  };

  // 侧边栏菜单项
  const sidebarItems = [
    { id: 'auth', title: 'API 认证', icon: <Key className="h-4 w-4" /> },
    { id: 'xiaohongshu', title: '红薯取数', icon: apiEndpoints[0].icon },
    { id: 'tiktok', title: 'TikTok取数', icon: apiEndpoints[1].icon },
    { id: 'douyin', title: '抖音取数', icon: apiEndpoints[2].icon },
    { id: 'youtube', title: 'YouTube取数', icon: apiEndpoints[3].icon },
    { id: 'kuaishou', title: '快手取数', icon: apiEndpoints[4].icon },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
        <div className="w-full px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
              <Link to="/" className="flex items-center gap-2 text-lg font-semibold hover:text-blue-600 transition-colors">
                <img src="/favicon.png" alt="DataMiner Logo" className="w-6 h-6" />
                DataMiner
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center gap-2 text-gray-600">
                <BookOpen className="h-5 w-5" />
                <span className="font-medium">API 文档</span>
              </div>
            </div>
            <Link to="/dashboard">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowRight className="h-4 w-4" />
                返回工作台
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="flex w-full">
        {/* 左侧菜单 */}
        <div className={`fixed inset-y-0 left-0 z-40 w-72 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:sticky lg:top-[73px] lg:h-[calc(100vh-73px)] ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}>
          <div className="flex flex-col h-full pt-20 lg:pt-0">
            <div className="px-6 py-6 overflow-y-auto flex-1">
              <div className="mb-6">
                <h3 className="text-lg font-bold text-gray-900 mb-2">API 文档导航</h3>
                <p className="text-sm text-gray-600">选择要查看的接口分类</p>
              </div>
              <nav className="space-y-2">
                {sidebarItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => switchToSection(item.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-left group ${
                      activeSection === item.id
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-sm border border-blue-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
                    }`}
                  >
                    <div className={`p-2 rounded-lg transition-colors ${
                      activeSection === item.id 
                        ? 'bg-blue-100' 
                        : 'bg-gray-100 group-hover:bg-gray-200'
                    }`}>
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{item.title}</div>
                      {item.id === 'auth' && (
                        <div className="text-xs text-gray-500 mt-0.5">认证说明</div>
                      )}
                    </div>
                    {activeSection === item.id && (
                      <div className="w-1 h-8 bg-blue-500 rounded-full"></div>
                    )}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>

        {/* 遮罩层 (移动端) */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* 主内容区域 */}
        <div className="flex-1 lg:ml-0 min-h-screen">
          <div className="max-w-5xl mx-auto px-8 py-8">
            {/* 根据选中的菜单项显示对应内容 */}
            {activeSection === 'auth' && (
              <div className="space-y-8">
                {/* 页面标题 */}
                <div className="text-center mb-12">
                  <div className="inline-flex items-center gap-3 mb-4">
                    <div className="p-3 bg-blue-100 rounded-xl">
                      <Key className="h-8 w-8 text-blue-600" />
                    </div>
                    <h1 className="text-4xl font-bold text-gray-900">API 认证指南</h1>
                  </div>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    了解如何使用API Key进行身份认证，开始您的数据采集之旅
                  </p>
                </div>

                {/* API Key Section */}
                <Card className="p-8 border-blue-200 bg-gradient-to-br from-blue-50 via-white to-indigo-50 shadow-lg">
                  <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">您的API密钥</h2>
                    <p className="text-gray-600">
                      请妥善保管您的API Key，不要在公开场所分享
                    </p>
                  </div>
                  <div className="bg-white rounded-xl p-6 border border-blue-200 shadow-sm">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-lg font-semibold text-gray-900">请求头设置</span>
                      {user?.api_key ? (
                        <Link to="/dashboard" className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                          <ExternalLink className="h-4 w-4" />
                          复制API Key
                        </Link>
                      ) : (
                        <Link to="/dashboard" className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                          获取API Key
                        </Link>
                      )}
                    </div>
                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                      <code className="text-green-400 font-mono text-sm">
                        x-api-key: {user?.api_key || 'YOUR_API_KEY_HERE'}
                      </code>
                    </div>
                  </div>
                </Card>

                {/* 使用说明和示例代码 */}
                <div className="grid md:grid-cols-2 gap-8">
                  <Card className="p-8 shadow-lg">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <BookOpen className="h-8 w-8 text-green-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">快速开始</h3>
                    </div>
                    <div className="space-y-6">
                      <div className="flex gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-bold text-sm">1</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">获取API Key</h4>
                          <p className="text-gray-600 text-sm">登录后在工作台页面查看和复制您的API Key</p>
                        </div>
                      </div>
                      <div className="flex gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-bold text-sm">2</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">设置请求头</h4>
                          <p className="text-gray-600 text-sm">在请求中添加认证头部信息</p>
                        </div>
                      </div>
                      <div className="flex gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-bold text-sm">3</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">发送请求</h4>
                          <p className="text-gray-600 text-sm">使用POST方法发送JSON数据</p>
                        </div>
                      </div>
                      <div className="flex gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-bold text-sm">4</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">处理响应</h4>
                          <p className="text-gray-600 text-sm">解析JSON响应获取数据结果</p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* 示例代码 */}
                  <Card className="p-8 shadow-lg">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Copy className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">请求示例</h3>
                    </div>
                    <div className="bg-gray-900 rounded-xl p-6 overflow-x-auto">
                      <pre className="text-green-400 text-sm leading-relaxed">
{`curl -X POST "${getCurrentDomain()}/api/xhs/topic/search" \\
  -H "Content-Type: application/json" \\
  -H "x-api-key: YOUR_API_KEY" \\
  -d '{
    "keyword": "美食推荐"
  }'`}
                      </pre>
                    </div>
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">响应示例</h4>
                      <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                        <pre className="text-blue-400 text-xs">
{`{
  "code": 200,
  "message": "success",
  "data": {
    "total": 1000,
    "items": [...]
  }
}`}
                        </pre>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            )}

            {/* 显示选中平台的接口文档 */}
            {activeSection !== 'auth' && (
              <div className="space-y-8">
                {apiEndpoints
                  .filter((category: ApiCategory) => {
                    const sectionMap: { [key: string]: string } = {
                      'xiaohongshu': '红薯取数',
                      'tiktok': 'TikTok取数',
                      'douyin': '抖音取数',
                      'youtube': 'YouTube取数',
                      'kuaishou': '快手取数'
                    };
                    return category.category === sectionMap[activeSection];
                  })
                  .map((category: ApiCategory, categoryIndex: number) => (
                    <div key={categoryIndex}>
                      {/* 平台标题 */}
                      <div className="text-center mb-12">
                        <div className="inline-flex items-center gap-4 mb-6">
                          <div className="p-4 bg-white rounded-2xl shadow-lg border border-gray-200">
                            {category.icon}
                          </div>
                          <div className="text-left">
                            <h1 className="text-4xl font-bold text-gray-900 mb-2">{category.category}</h1>
                            <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                          </div>
                        </div>
                        <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                          {activeSection === 'xiaohongshu' && '话题搜索笔记、账号笔记提取，获取完整的小红书内容数据和用户信息'}
                          {activeSection === 'tiktok' && '账号视频提取、账号粉丝提取、视频批量提取等海外平台数据'}
                          {activeSection === 'douyin' && '账号作品提取、视频批量提取、评论列表提取，全方位抖音数据获取'}
                          {activeSection === 'youtube' && '账号作品提取、播放列表视频、视频批量提取等全方位数据获取'}
                          {activeSection === 'kuaishou' && '视频批量提取、评论列表提取，快手平台数据采集工具'}
                        </p>
                      </div>

                      {/* 接口列表 */}
                      <div className="grid gap-8">
                        {category.endpoints.map((endpoint: ApiEndpoint, endpointIndex: number) => (
                          <Card key={endpointIndex} className="p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-white to-gray-50">
                            <div className="mb-6">
                              <div className="flex items-center gap-4 mb-4">
                                <span className={`px-3 py-1.5 text-sm font-bold rounded-full ${
                                  endpoint.method === 'POST' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-blue-100 text-blue-700 border border-blue-200'
                                }`}>
                                  {endpoint.method}
                                </span>
                                <h3 className="text-2xl font-bold text-gray-900">{endpoint.name}</h3>
                              </div>
                              <p className="text-gray-600 text-lg leading-relaxed">{endpoint.description}</p>
                            </div>

                            <div className="grid lg:grid-cols-2 gap-8">
                              {/* 左侧：接口信息 */}
                              <div className="space-y-6">
                                <div>
                                  <div className="flex items-center justify-between mb-3">
                                    <h4 className="text-lg font-semibold text-gray-900">接口地址</h4>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => copyToClipboard(endpoint.path, endpoint.path)}
                                      className="h-8 px-3 hover:bg-blue-50"
                                    >
                                      {copiedEndpoint === endpoint.path ? (
                                        <Check className="h-4 w-4 text-green-600" />
                                      ) : (
                                        <Copy className="h-4 w-4" />
                                      )}
                                      <span className="ml-2 text-sm">
                                        {copiedEndpoint === endpoint.path ? '已复制' : '复制'}
                                      </span>
                                    </Button>
                                  </div>
                                  <div className="bg-gray-900 rounded-xl p-4 overflow-x-auto">
                                    <code className="text-green-400 font-mono text-sm">
                                      {endpoint.path}
                                    </code>
                                  </div>
                                </div>

                                <div>
                                  <h4 className="text-lg font-semibold text-gray-900 mb-3">请求参数</h4>
                                  <div className="bg-white rounded-xl p-4 border border-gray-200">
                                    <div className="space-y-3">
                                      {Object.entries(endpoint.params).map(([key, value]) => (
                                        <div key={key} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                                          <code className="font-mono text-blue-600 font-semibold text-sm bg-blue-50 px-3 py-2 rounded min-w-fit">
                                            {key}
                                          </code>
                                          <span className="text-gray-700 text-sm flex-1 leading-relaxed">{String(value)}</span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* 右侧：请求示例 */}
                              <div>
                                <h4 className="text-lg font-semibold text-gray-900 mb-3">请求示例</h4>
                                <div className="bg-gray-900 rounded-xl p-6 overflow-x-auto">
                                  <pre className="text-green-400 text-sm leading-relaxed">
{`curl -X ${endpoint.method} "${endpoint.path}" \\
  -H "Content-Type: application/json" \\
  -H "x-api-key: YOUR_API_KEY" \\
  -d '${JSON.stringify(
    Object.keys(endpoint.params).reduce((acc: any, key) => {
      if (key.includes('array') || key.includes('urls')) {
        acc[key] = ['example1', 'example2'];
      } else if (key.includes('number') || key === 'limit' || key === 'page') {
        acc[key] = 20;
      } else {
        acc[key] = 'example_value';
      }
      return acc;
    }, {}), null, 2
  )}'`}
                                  </pre>
                                </div>

                                {/* 响应示例部分 */}
                                <div className="mt-6">
                                  <div className="flex items-center justify-between mb-3">
                                    <h4 className="text-lg font-semibold text-gray-900">响应示例</h4>
                                    {!loadedExamples[endpoint.path] && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => loadExampleData(endpoint)}
                                        disabled={loadingExamples[endpoint.path]}
                                        className="flex items-center gap-2"
                                      >
                                        {loadingExamples[endpoint.path] ? (
                                          <>
                                            <Loader2 className="h-4 w-4 animate-spin" />
                                            加载中...
                                          </>
                                        ) : (
                                          <>
                                            <Download className="h-4 w-4" />
                                            加载示例数据
                                          </>
                                        )}
                                      </Button>
                                    )}
                                  </div>

                                  {loadedExamples[endpoint.path] ? (
                                    <div className="bg-gray-900 rounded-xl p-6 overflow-x-auto">
                                      <pre className="text-blue-400 text-sm">
                                        {JSON.stringify(loadedExamples[endpoint.path], null, 2)}
                                      </pre>
                                    </div>
                                  ) : (
                                    <div className="bg-gray-100 rounded-xl p-6 text-center">
                                      <p className="text-gray-500">点击"加载示例数据"按钮查看真实的响应示例</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ))
                }
              </div>
            )}

            {/* Footer */}
            <div className="mt-16">
              <Card className="p-12 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200 shadow-xl">
                <div className="text-center">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <BookOpen className="h-10 w-10 text-blue-600" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">需要帮助？</h3>
                  <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                    如果您在使用API过程中遇到问题，我们的技术支持团队随时为您提供帮助
                  </p>
                  <div className="flex flex-col sm:flex-row justify-center gap-4">
                    <Link to="/dashboard">
                      <Button size="lg" className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg">
                        返回工作台
                      </Button>
                    </Link>
                    <Button
                      variant="outline"
                      size="lg"
                      className="px-8 py-3 border-2 border-blue-300 hover:bg-blue-50"
                      onClick={() => {
                        // 打开 Tawk.to 在线客服
                        if (typeof window !== 'undefined' && (window as any).Tawk_API) {
                          (window as any).Tawk_API.toggle();
                        }
                      }}
                    >
                      联系技术支持
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiDocs;
