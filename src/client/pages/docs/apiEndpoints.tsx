import React from 'react';
import { Search, Camera, Music, Youtube, Zap } from 'lucide-react';

export interface ApiEndpoint {
	name: string;
	method: string;
	path: string;
	description: string;
	params: Record<string, string>;
	testDataPath?: string;
}

export interface ApiCategory {
	category: string;
	icon: React.ReactNode;
	color: string;
	endpoints: ApiEndpoint[];
}

export const apiEndpoints: ApiCategory[] = [
	{
		category: '红薯取数',
		icon: <Search className="h-5 w-5 text-red-500" />,
		color: 'border-red-200 bg-red-50',
		endpoints: [
			// {
			// 	name: '关键词搜索话题',
			// 	method: 'POST',
			// 	path: 'https://data.snappdown.com/api/xhs/topic/search',
			// 	description: '根据关键词搜索小红书相关话题',
			// 	params: {
			// 		keyword: 'string - 搜索关键词',
			// 	},
			// 	testDataPath: '/test/data/xhs/topic.json',
			// },
			// {
			// 	name: '话题搜索笔记',
			// 	method: 'POST',
			// 	path: 'https://data.snappdown.com/api/xhs/topic/notes',
			// 	description: '根据话题搜索小红书笔记',
			// 	params: {
			// 		pageId: 'string - 话题ID',
			// 		cursor: 'string - 分页游标',
			// 		sort: 'string - 排序方式（可选，hot, time, trend）',
			// 		lastNoteId: 'string - 上次加载的最后一条笔记ID',
			// 		lastNoteCt: 'string - 上次加载的最后一条笔记创建时间',
			// 	},
			// 	testDataPath: '/test/data/xhs/topic.json',
			// },
			{
				name: '笔记详情数据',
				method: 'POST', 
				path: 'https://data.snappdown.com/api/xhs/note/info',
				description: '提取笔记详情数据',
				params: {
					url: 'string - 笔记链接',
				},
				testDataPath: '/test/data/xhs/noteDetail.json',
			},
			{
				name: '账号笔记提取',
				method: 'POST', 
				path: 'https://data.snappdown.com/api/xhs/user/notes',
				description: '提取指定用户的笔记列表',
				params: {
					userId: 'string - 用户ID',
					lastCursor: 'string - 上次加载的最后一条笔记游标'
				},
				testDataPath: '/test/data/xhs/userNotes.json',
			},
			{
				name: '笔记扩展数据',
				method: 'POST', 
				path: 'https://data.snappdown.com/api/xhs/note/business',
				description: '提取指定用户的笔记列表（曝光、阅读、涨粉数）',
				params: {
					url: 'string - 笔记链接 与 noteId 二选一',
					noteId: 'string - 笔记ID 与 url 二选一'
				},
				testDataPath: '/test/data/xhs/noteBusiness.json',
			}
		]
	},
	{
		category: 'TikTok取数',
		icon: <Camera className="h-5 w-5 text-black" />,
		color: 'border-gray-200 bg-gray-50',
		endpoints: [
			{
				name: '账号视频提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/tiktok//user/posts',
				description: '提取TikTok用户的视频列表',
				params: {
					secUserId: 'string - tiktok 用户secUserId',
					cursor: 'string - 分页游标'
				},
				testDataPath: '/test/data/tiktok/userPost.json',
			},
			{
				name: '账号粉丝提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/tiktok/user/fans',
				description: '提取TikTok用户的粉丝信息',
				params: {
					secUid: 'string - tiktok 用户secUid',
					minCursor: 'string - 分页游标'
				},
				testDataPath: '/test/data/tiktok/userFans.json',
			},
			{
				name: '账号信息',
				method: 'POST',
				path: 'https://data.snappdown.com/api/tiktok/user/info',
				description: '提取tiktok账户的详情',
				params: {
					url: 'string - 用户URL链接地址'
				},
				testDataPath: '/test/data/tiktok/user.json',
			},
			{
				name: '视频详情',
				method: 'POST',
				path: 'https://data.snappdown.com/api/tiktok/video/detail',
				description: '提取tiktok账户的详情',
				params: {
					url: 'string - tiktok视频链接'
				},
				testDataPath: '/test/data/tiktok/videoDetail.json',
			}
		]
	},
	{
		category: '抖音取数',
		icon: <Music className="h-5 w-5 text-purple-500" />,
		color: 'border-purple-200 bg-purple-50',
		endpoints: [
			{
				name: '账号作品提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/dy/user/videos',
				description: '提取抖音用户的作品列表',
				params: {
					url: 'string - 用户主页链接',
					maxCursor: 'number - 分页游标'
				},
				testDataPath: '/test/data/douyin/userVideos.json',
			},
			{
				name: '视频详情提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/dy/detail',
				description: '批量提取抖音视频详细信息',
				params: {
					url: 'string - 视频URL'
				},
				testDataPath: '/test/data/douyin/dyDetail.json',
			},
			{
				name: '评论列表提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/dy/video/comments',
				description: '提取抖音视频的评论列表',
				params: {
					url: 'string - 视频URL 与video_id 二选一',
					video_id: 'string - 视频ID 与URL 二选一',
					cursor: 'string - 分页游标'
				},
				testDataPath: '/test/data/douyin/commentList.json',
			},
			{
				name: '评论回复提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/dy/video/comments',
				description: '提取抖音视频的评论列表',
				params: {
					comment_id: 'string - 评论ID',
					aweme_id: 'string - 视频ID',
					cursor: 'string - 分页游标'
				},
				testDataPath: '/test/data/douyin/replyList.json',
			},
			{
				name: '视频详情扩展数据',
				method: 'POST',
				path: 'https://data.snappdown.com/api/dy/detail',
				description: '批量提取抖音视频详细的扩展信息（基础数据+播放量、完播率等）',
				params: {
					url: 'string - 视频URL'
				},
				testDataPath: '/test/data/douyin/xtDetail.json',
			}
		]
	},
	{
		category: 'YouTube取数',
		icon: <Youtube className="h-5 w-5 text-red-600" />,
		color: 'border-red-200 bg-red-50',
		endpoints: [
			{
				name: '视频元数据获取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/youtube/video',
				description: '获取YouTube视频的详细元数据信息',
				params: {
					id: 'string - 视频ID或URL'
				},
				testDataPath: '/test/data/YouTube/sup/video.json',
			},
			{
				name: '频道信息获取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/youtube/channel',
				description: '获取YouTube频道的基本信息',
				params: {
					channel_id: 'string - 频道ID或URL'
				},
				testDataPath: '/test/data/YouTube/sup/channel.json',
			},
			{
				name: '频道视频列表',
				method: 'POST',
				path: 'https://data.snappdown.com/api/youtube/user/videos',
				description: '获取YouTube频道的视频列表',
				params: {
					url: 'string - 频道URL',
					start: 'number - 开始条数',
					end: 'number - 结束条数',
					type: 'string - 视频类型（可选）：videos, shorts'
				},
				testDataPath: '/test/data/YouTube/userVideos.json',
			},
			{
				name: '播放列表视频',
				method: 'POST',
				path: 'https://data.snappdown.com/api/youtube/user/videos',
				description: '获取YouTube播放列表的视频列表',
				params: {
					url: 'string - 频道URL',
					start: 'number - 开始条数',
					end: 'number - 结束条数',
					type: 'string - 视频类型（可选）：playlists'
				},
				testDataPath: '/test/data/YouTube/playlistsVideo.json',
			}
		]
	},
	{
		category: '快手取数',
		icon: <Zap className="h-5 w-5 text-orange-500" />,
		color: 'border-orange-200 bg-orange-50',
		endpoints: [
			{
				name: '视频详情提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/kuaishou/video/detail',
				description: '批量提取快手视频详细信息',
				params: {
					url: 'string - 视频URL'
				},
				testDataPath: '/test/data/kuaishou/ksDetail.json',
			},
			{
				name: '评论列表提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/kuaishou/comments',
				description: '提取快手视频的评论列表',
				params: {
					photoId: 'string - 作品ID 例如https://www.kuaishou.com/short-video/xxx  xxx的值',
					authorId: 'string - 作者ID',
					pcursor: 'string - 分页游标'
				},
				testDataPath: '/test/data/kuaishou/commentList.json',
			},
			{
				name: '评论回复列表提取',
				method: 'POST',
				path: 'https://data.snappdown.com/api/kuaishou/replies',
				description: '提取快手视频的评论列表',
				params: {
					photoId: 'string - 作品ID 例如https://www.kuaishou.com/short-video/xxx  xxx的值',
					authorId: 'string - 作者ID',
					rootCommentId: '评论ID',
					pcursor: 'string - 分页游标'
				},
				testDataPath: '/test/data/kuaishou/replyList.json',
			}
		]
	}
];
