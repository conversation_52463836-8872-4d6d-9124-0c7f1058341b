import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Plus, Minus, UserPlus } from 'lucide-react';
import { adminApi, AdminUser, ApiError } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../../components/ui/dialog';
import { Switch } from '../../components/ui/switch';
import { Label } from '../../components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';

const UserList: React.FC = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<number>(1); // Default to enabled (1)
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [creditsToAdd, setCreditsToAdd] = useState<number>(0);
  const [creditsChangeReason, setCreditsChangeReason] = useState<string>('');
  const [isCreditsDialogOpen, setIsCreditsDialogOpen] = useState(false);
  const [isRemarkDialogOpen, setIsRemarkDialogOpen] = useState(false);
  const [newRemark, setNewRemark] = useState<string>('');
  const [isUpdatingRemark, setIsUpdatingRemark] = useState(false);
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const limit = 10;
  const { toast } = useToast();

  // New user form state
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    role: 'user',
    credit: 0
  });
  const [isCreatingUser, setIsCreatingUser] = useState(false);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const response = await adminApi.getUsers(searchQuery, page, limit, statusFilter);
      if (response.success) {
        setUsers(response.data?.users || []);
        setTotalUsers(response.data?.total || 0);
      } else {
        toast({
          variant: "destructive",
          title: "获取用户列表失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "获取用户列表失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, statusFilter]);

  const handleSearch = () => {
    setPage(1);
    fetchUsers();
  };

  const handleStatusToggle = async (user: AdminUser, checked: boolean) => {
    try {
      const newStatus = checked ? 1 : 0;
      const response = await adminApi.updateUserStatus(user.id, newStatus);
      if (response.success) {
        setUsers(users.map(u => 
          u.id === user.id ? { ...u, status: newStatus } : u
        ));
        toast({
          variant: "success",
          title: "更新成功",
          description: `用户 ${user.username} 状态已${checked ? '启用' : '禁用'}`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "更新用户状态失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "更新用户状态失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    }
  };

  const openCreditsDialog = (user: AdminUser) => {
    setSelectedUser(user);
    setCreditsToAdd(0);
    setIsCreditsDialogOpen(true);
  };

  const openRemarkDialog = (user: AdminUser) => {
    setSelectedUser(user);
    setNewRemark(user.remark || '');
    setIsRemarkDialogOpen(true);
  };

  const handleUpdateRemark = async () => {
    if (!selectedUser) return;

    setIsUpdatingRemark(true);
    try {
      const response = await adminApi.updateUserRemark(selectedUser.id, newRemark);
      if (response.success) {
        setUsers(users.map(u => 
          u.id === selectedUser.id ? { ...u, remark: newRemark } : u
        ));
        setIsRemarkDialogOpen(false);
        toast({
          variant: "success",
          title: "备注更新成功",
          description: `用户 ${selectedUser.username} 的备注已更新`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "更新备注失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "更新备注失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    } finally {
      setIsUpdatingRemark(false);
    }
  };

  const handleAddCredits = async () => {
    if (!selectedUser) return;
    
    try {
      const response = await adminApi.updateUserCredit(selectedUser.id, 'system_add_credit', creditsToAdd, creditsChangeReason);
      if (response.success) {
        setUsers(users.map(u => 
          u.id === selectedUser.id ? { ...u, credit: u.credit + creditsToAdd } : u
        ));
        setIsCreditsDialogOpen(false);
        toast({
          variant: "success",
          title: "积分更新成功",
          description: `用户 ${selectedUser.username} 的积分已${creditsToAdd >= 0 ? '增加' : '减少'} ${Math.abs(creditsToAdd)}`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "更新积分失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "更新积分失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    }
  };

  const handleCreateUser = async () => {
    if (!newUser.username || !newUser.password) {
      toast({
        variant: "destructive",
        title: "创建用户失败",
        description: "用户名和密码不能为空",
      });
      return;
    }

    setIsCreatingUser(true);
    try {
      const response = await adminApi.createUser(
        newUser.username,
        newUser.password,
        newUser.role,
        newUser.credit
      );

      if (response.success && response.data) {
        // If we're on the first page, add the new user to the list
        if (page === 1) {
          setUsers([response.data, ...users].slice(0, limit) as AdminUser[]);
        }
        
        // Reset form and close dialog
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          credit: 0
        });
        setIsCreateUserDialogOpen(false);
        
        toast({
          variant: "success",
          title: "创建用户成功",
          description: `用户 ${newUser.username} 已成功创建`,
        });
        
        // Refresh the user list to ensure accurate counts
        fetchUsers();
      } else {
        toast({
          variant: "destructive",
          title: "创建用户失败",
          description: response.message || "未知错误",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "创建用户失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    } finally {
      setIsCreatingUser(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewUser(prev => ({
      ...prev,
      [name]: name === 'credits' ? parseInt(value) || 0 : value
    }));
  };

  const totalPages = Math.ceil(totalUsers / limit);

  return (
    <div className="p-4 space-y-4">
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">用户管理</h2>
          <Button onClick={() => setIsCreateUserDialogOpen(true)}>
            <UserPlus className="w-4 h-4 mr-2" />
            添加用户
          </Button>
        </div>
        
        <div className="flex gap-2 mb-6">
          <Input
            placeholder="搜索用户名"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            className="max-w-sm"
          />
          <Select
            value={statusFilter.toString()}
            onValueChange={(value) => setStatusFilter(parseInt(value))}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">启用</SelectItem>
              <SelectItem value="0">禁用</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleSearch} disabled={isLoading}>
            <Search className="w-4 h-4 mr-2" />
            搜索
          </Button>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>用户名</TableHead>
                <TableHead>角色</TableHead>
                <TableHead>积分</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>API Key</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>最后登录</TableHead>
                <TableHead>备注</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>{user.credit}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={user.status === 1}
                        onCheckedChange={(checked) => handleStatusToggle(user, checked)}
                      />
                      <Label>{user.status === 1 ? '启用' : '禁用'}</Label>
                    </div>
                  </TableCell>
                  <TableCell>{user.api_key}</TableCell>
                  <TableCell>{user.created_at}</TableCell>
                  <TableCell>{user.last_login_at ? new Date(user.last_login_at).toLocaleString() : '未登录'}</TableCell>
                  <TableCell>{user.remark}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openCreditsDialog(user)}
                      >
                        积分管理
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openRemarkDialog(user)}
                      >
                        更新备注
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {users.length === 0 && !isLoading && (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-4">
                    暂无用户数据
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(prev => Math.max(prev - 1, 1))}
              disabled={page === 1}
            >
              上一页
            </Button>
            <span className="text-sm">
              第 {page} 页，共 {totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(prev => Math.min(prev + 1, totalPages))}
              disabled={page === totalPages}
            >
              下一页
            </Button>
          </div>
        )}

        {isLoading && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
      </Card>

      {/* Credits Dialog */}
      <Dialog open={isCreditsDialogOpen} onOpenChange={setIsCreditsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>积分管理 - {selectedUser?.username}</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">  {/* 添加space-y-4保持垂直间距 */}
            <p className="mb-2">当前积分: {selectedUser?.credit || 0}</p>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCreditsToAdd(prev => prev - 10)}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Input
                type="number"
                value={creditsToAdd}
                onChange={(e) => setCreditsToAdd(parseInt(e.target.value) || 0)}
                className="w-20 text-center"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCreditsToAdd(prev => prev + 10)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {/* 新增变更原因输入区域 */}
            <div className="space-y-2">
              <Label>变更原因</Label>
              <textarea
                value={creditsChangeReason}
                onChange={(e) => setCreditsChangeReason(e.target.value)}
                className="w-full p-2 border rounded-md resize-y min-h-[80px]"  // 基础样式
                placeholder="请输入积分变更原因（必填）"
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleAddCredits}>
              {creditsToAdd >= 0 ? '增加积分' : '扣减积分'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remark Dialog */}
      <Dialog open={isRemarkDialogOpen} onOpenChange={setIsRemarkDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>更新备注 - {selectedUser?.username}</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label>备注内容</Label>
              <textarea
                value={newRemark}
                onChange={(e) => setNewRemark(e.target.value)}
                className="w-full p-2 border rounded-md resize-y min-h-[120px]"
                placeholder="请输入用户备注"
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleUpdateRemark} disabled={isUpdatingRemark}>
              {isUpdatingRemark ? '保存中...' : '保存备注'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create User Dialog */}
      <Dialog open={isCreateUserDialogOpen} onOpenChange={setIsCreateUserDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加新用户</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                name="username"
                placeholder="请输入用户名"
                value={newUser.username}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="请输入密码"
                value={newUser.password}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">角色</Label>
              <Select
                value={newUser.role}
                onValueChange={(value) => setNewUser(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">普通用户</SelectItem>
                  <SelectItem value="admin">管理员</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleCreateUser} disabled={isCreatingUser}>
              {isCreatingUser ? '创建中...' : '创建用户'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserList;