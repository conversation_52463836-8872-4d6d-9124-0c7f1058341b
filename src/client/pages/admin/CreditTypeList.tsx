import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Plus, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react';
import { useToast } from '../../components/ui/use-toast';
import { creditTypeApi, type CreditType } from '../../lib/creditTypeApi';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../../components/ui/dialog';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Textarea } from '../../components/ui/textarea';
import { Switch } from '../../components/ui/switch';

const CreditTypeList: React.FC = () => {
	const [creditTypes, setCreditTypes] = useState<CreditType[]>([]);
	const [loading, setLoading] = useState(false);
	const [saving, setSaving] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [editingCreditType, setEditingCreditType] = useState<CreditType | null>(null);
	const [formData, setFormData] = useState({
		type_key: '',
		type_name: '',
		credit_amount: 0,
		operation_type: 'add' as 'add' | 'deduct',
		description: '',
	});
	const { toast } = useToast();

	// 获取积分类型列表
	const fetchCreditTypes = async () => {
		setLoading(true);
		try {
			const result = await creditTypeApi.getCreditTypes();
			setCreditTypes(result.data as unknown as CreditType[]);
		} catch (error) {
			toast({
				variant: 'destructive',
				title: '错误',
				description: error instanceof Error ? error.message : '获取积分类型列表失败',
			});
		} finally {
			setLoading(false);
		}
	};

	// 创建或更新积分类型
	const handleSave = async () => {
		setSaving(true);
		try {
			if (editingCreditType) {
				await creditTypeApi.updateCreditType(editingCreditType.id, formData);
			} else {
				await creditTypeApi.createCreditType(formData);
			}

			toast({
				title: '成功',
				description: editingCreditType ? '积分类型更新成功' : '积分类型创建成功',
			});

			setIsDialogOpen(false);
			setEditingCreditType(null);
			resetForm();
			fetchCreditTypes();
		} catch (error) {
			toast({
				variant: 'destructive',
				title: '错误',
				description: error instanceof Error ? error.message : '保存失败',
			});
		} finally {
			setSaving(false);
		}
	};
	// 重置表单
	const resetForm = () => {
		setFormData({
			type_key: '',
			type_name: '',
			credit_amount: 0,
			operation_type: 'add',
			description: '',
		});
	};

	// 打开编辑对话框
	const handleEdit = (creditType: CreditType) => {
		setEditingCreditType(creditType);
		setFormData({
			type_key: creditType.type_key,
			type_name: creditType.type_name,
			credit_amount: creditType.credit_amount,
			operation_type: creditType.operation_type,
			description: creditType.description || '',
		});
		setIsDialogOpen(true);
	};

	// 打开新建对话框
	const handleAdd = () => {
		setEditingCreditType(null);
		resetForm();
		setIsDialogOpen(true);
	};

	// 过滤积分类型
	const filteredCreditTypes = creditTypes.filter(
		(creditType) =>
			creditType.type_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			creditType.type_key.toLowerCase().includes(searchTerm.toLowerCase())
	);

	useEffect(() => {
		fetchCreditTypes();
	}, []);

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h1 className="text-2xl font-bold">积分类型管理</h1>
				<div className="flex gap-2">
					<Button onClick={handleAdd}>
						<Plus className="w-4 h-4 mr-2" />
						新增积分类型
					</Button>
				</div>
			</div>

			<Card className="p-6">
				<div className="flex gap-4 mb-6">
					<div className="flex-1">
						<Input
							placeholder="搜索积分类型名称或键值..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="max-w-sm"
						/>
					</div>
				</div>

				<div className="rounded-md border">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>ID</TableHead>
								<TableHead>类型键值</TableHead>
								<TableHead>类型名称</TableHead>
								<TableHead>积分数量</TableHead>
								<TableHead>操作类型</TableHead>
								<TableHead>描述</TableHead>
								<TableHead>创建时间</TableHead>
								<TableHead>操作</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{loading ? (
								<TableRow>
									<TableCell colSpan={9} className="text-center py-8">
										加载中...
									</TableCell>
								</TableRow>
							) : filteredCreditTypes.length === 0 ? (
								<TableRow>
									<TableCell colSpan={9} className="text-center py-8">
										暂无数据
									</TableCell>
								</TableRow>
							) : (
								filteredCreditTypes.map((creditType) => (
									<TableRow key={creditType.id}>
										<TableCell>{creditType.id}</TableCell>
										<TableCell className="font-mono text-sm">{creditType.type_key}</TableCell>
										<TableCell>{creditType.type_name}</TableCell>
										<TableCell>
											{creditType.type_key === 'stripe_payment_credit' || creditType.type_key === 'system_add_credit'
												? '-'
												: creditType.credit_amount}
										</TableCell>
										<TableCell>
											<span
												className={`px-2 py-1 rounded-full text-xs ${
													creditType.operation_type === 'add' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
												}`}
											>
												{creditType.operation_type === 'add' ? '增加' : '扣除'}
											</span>
										</TableCell>
										<TableCell className="max-w-xs truncate">{creditType.description}</TableCell>
										<TableCell>{new Date(creditType.created_at).toLocaleDateString()}</TableCell>
										<TableCell>
											<div className="flex gap-2">
												<Button variant="ghost" size="sm" onClick={() => handleEdit(creditType)}>
													<Edit className="w-4 h-4" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								))
							)}
						</TableBody>
					</Table>
				</div>
			</Card>

			{/* 新增/编辑对话框 */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>{editingCreditType ? '编辑积分类型' : '新增积分类型'}</DialogTitle>
						<DialogDescription>{editingCreditType ? '修改积分类型信息' : '创建新的积分类型'}</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="type_key" className="text-right">
								类型键值
							</Label>
							<Input
								id="type_key"
								value={formData.type_key}
								onChange={(e) => setFormData({ ...formData, type_key: e.target.value })}
								className="col-span-3"
								placeholder="例如: login_bonus"
							/>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="type_name" className="text-right">
								类型名称
							</Label>
							<Input
								id="type_name"
								value={formData.type_name}
								onChange={(e) => setFormData({ ...formData, type_name: e.target.value })}
								className="col-span-3"
								placeholder="例如: 登录奖励"
							/>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="credit_amount" className="text-right">
								积分数量
							</Label>
							<Input
								id="credit_amount"
								type="number"
								value={formData.credit_amount}
								onChange={(e) => setFormData({ ...formData, credit_amount: parseInt(e.target.value) || 0 })}
								className="col-span-3"
								placeholder="积分数量"
							/>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="operation_type" className="text-right">
								操作类型
							</Label>
							<Select
								value={formData.operation_type}
								onValueChange={(value: 'add' | 'deduct') => setFormData({ ...formData, operation_type: value })}
							>
								<SelectTrigger className="col-span-3">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="add">增加</SelectItem>
									<SelectItem value="deduct">扣除</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="description" className="text-right">
								描述
							</Label>
							<Textarea
								id="description"
								value={formData.description}
								onChange={(e) => setFormData({ ...formData, description: e.target.value })}
								className="col-span-3"
								placeholder="积分类型描述"
								rows={3}
							/>
						</div>
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setIsDialogOpen(false)} disabled={saving}>
							取消
						</Button>
						<Button onClick={handleSave} disabled={saving}>
							{saving ? '保存中...' : (editingCreditType ? '更新' : '创建')}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
};

export default CreditTypeList;
