import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Plus, Pencil } from 'lucide-react';
import { productApi, Product } from '../../lib/productApi';
import { ApiError } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../../components/ui/dialog';
import { Switch } from '../../components/ui/switch';
import { Label } from '../../components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';

const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isActiveFilter, setIsActiveFilter] = useState<number | undefined>(1); // 默认显示启用状态的商品
  const [currencyFilter, setCurrencyFilter] = useState<string>('');
  const [creditsFilter, setCreditsFilter] = useState<number | undefined>();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);

  const [page, setPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const limit = 10;
  const { toast } = useToast();

  // 商品表单状态
  const [productForm, setProductForm] = useState<Omit<Product, 'id' | 'created_at' | 'updated_at' | 'stripe_product_id' | 'stripe_price_id'>>({
    name: '',
    description: '',
    price: 0,
    credits: 0,
    currency: 'CNY',
    is_active: 1
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const fetchProducts = async () => {
    setIsLoading(true);
    try {
      const response = await productApi.getProducts(searchQuery, page, limit, isActiveFilter, currencyFilter, creditsFilter);
      if (response.success) {
        setProducts(response.data?.products || []);
        setTotalProducts(response.data?.total || 0);
      } else {
        toast({
          variant: "destructive",
          title: "获取商品列表失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "获取商品列表失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [page, isActiveFilter, currencyFilter, creditsFilter]);

  const handleSearch = () => {
    setPage(1);
    fetchProducts();
  };

  const handleStatusToggle = async (product: Product, checked: boolean) => {
    try {
      const newIsActive = checked ? 1 : 0;
      const response = await productApi.updateProduct(product.id, { is_active: newIsActive });
      if (response.success) {
        setProducts(products.map(p => 
          p.id === product.id ? { ...p, is_active: newIsActive } : p
        ));
        toast({
          variant: "success",
          title: "更新成功",
          description: `商品 ${product.name} 状态已${checked ? '启用' : '禁用'}`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "更新商品状态失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "更新商品状态失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    }
  };

  const openProductDialog = (product?: Product) => {
    if (product) {
      setIsEditing(true);
      setSelectedProduct(product);
      setProductForm({
        name: product.name,
        description: product.description,
        price: product.price,
        credits: product.credits,
        currency: product.currency,
        is_active: product.is_active
      });
    } else {
      setIsEditing(false);
      setSelectedProduct(null);
      setProductForm({
        name: '',
        description: '',
        price: 0,
        credits: 0,
        currency: 'CNY',
        is_active: 1
      });
    }
    setIsProductDialogOpen(true);
  };



  const handleSubmitProduct = async () => {
    if (!productForm.name) {
      toast({
        variant: "destructive",
        title: "提交失败",
        description: "商品名称不能为空",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      let response;
      if (isEditing && selectedProduct) {
        response = await productApi.updateProduct(selectedProduct.id, productForm);
        if (response.success) {
          setProducts(products.map(p => 
            p.id === selectedProduct.id ? { ...p, ...productForm } : p
          ));
          toast({
            variant: "success",
            title: "更新商品成功",
            description: `商品 ${productForm.name} 已成功更新`,
          });
        }
      } else {
        response = await productApi.createProduct(productForm);
        if (response.success && response.data) {
          // 如果在第一页，将新商品添加到列表
          if (page === 1) {
            setProducts([response.data, ...products].slice(0, limit) as Product[]);
          }
          toast({
            variant: "success",
            title: "创建商品成功",
            description: `商品 ${productForm.name} 已成功创建`,
          });
        }
      }

      if (!response.success) {
        toast({
          variant: "destructive",
          title: isEditing ? "更新商品失败" : "创建商品失败",
          description: response.message || "未知错误",
        });
      } else {
        setIsProductDialogOpen(false);
        // 刷新商品列表
        fetchProducts();
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: isEditing ? "更新商品失败" : "创建商品失败",
        description: (error as ApiError).message || "网络错误，请稍后重试",
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProductForm(prev => ({
      ...prev,
      [name]: name === 'price' || name === 'credits' ? parseFloat(value) || 0 : value
    }));
  };

  const totalPages = Math.ceil(totalProducts / limit);

  return (
    <div className="p-4 space-y-4">
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">商品管理</h2>
          <Button onClick={() => openProductDialog()}>
            <Plus className="w-4 h-4 mr-2" />
            添加商品
          </Button>
        </div>
        
        <div className="flex gap-2 mb-6">
          <Input
            placeholder="搜索商品名称"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            className="max-w-sm"
          />
          <Select
            value={isActiveFilter?.toString() || 'all'}
            onValueChange={(value) => setIsActiveFilter(value === 'all' ? undefined : parseInt(value))}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="1">启用</SelectItem>
              <SelectItem value="0">禁用</SelectItem>
            </SelectContent>
          </Select>
          <Input
            placeholder="货币类型"
            value={currencyFilter}
            onChange={(e) => setCurrencyFilter(e.target.value)}
            className="max-w-[120px]"
          />
          <Input
            placeholder="积分"
            type="number"
            value={creditsFilter || ''}
            onChange={(e) => setCreditsFilter(e.target.value ? parseInt(e.target.value) : undefined)}
            className="max-w-[120px]"
          />
          <Button onClick={handleSearch} disabled={isLoading}>
            <Search className="w-4 h-4 mr-2" />
            搜索
          </Button>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>商品名称</TableHead>
                <TableHead>价格</TableHead>
                <TableHead>积分</TableHead>
                <TableHead>货币</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.id}>
                  <TableCell>{product.id}</TableCell>
                  <TableCell>{product.name}</TableCell>
                  <TableCell>{product.price.toFixed(2)}</TableCell>
                  <TableCell>{product.credits}</TableCell>
                  <TableCell>{product.currency}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={product.is_active === 1}
                        onCheckedChange={(checked) => handleStatusToggle(product, checked)}
                      />
                      <Label>{product.is_active === 1 ? '启用' : '禁用'}</Label>
                    </div>
                  </TableCell>
                  <TableCell>{new Date(product.created_at).toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openProductDialog(product)}
                      >
                        <Pencil className="w-4 h-4 mr-1" />
                        编辑
                      </Button>

                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {products.length === 0 && !isLoading && (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-4">
                    暂无商品数据
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(prev => Math.max(prev - 1, 1))}
              disabled={page === 1}
            >
              上一页
            </Button>
            <span className="text-sm">
              第 {page} 页，共 {totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(prev => Math.min(prev + 1, totalPages))}
              disabled={page === totalPages}
            >
              下一页
            </Button>
          </div>
        )}

        {isLoading && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
      </Card>

      {/* 商品表单对话框 */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{isEditing ? '编辑商品' : '添加新商品'}</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">商品名称</Label>
              <Input
                id="name"
                name="name"
                placeholder="请输入商品名称"
                value={productForm.name}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">商品描述</Label>
              <textarea
                id="description"
                name="description"
                placeholder="请输入商品描述"
                value={productForm.description}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md resize-y min-h-[80px]"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="price">价格</Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                placeholder="请输入价格"
                value={productForm.price}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="credits">积分</Label>
              <Input
                id="credits"
                name="credits"
                type="number"
                placeholder="请输入积分"
                value={productForm.credits}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="currency">货币</Label>
              <Select
                value={productForm.currency}
                onValueChange={(value) => setProductForm(prev => ({ ...prev, currency: value }))}
              >
                <SelectTrigger id="currency">
                  <SelectValue placeholder="选择货币" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                  <SelectItem value="USD">美元 (USD)</SelectItem>
                  <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                  <SelectItem value="JPY">日元 (JPY)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="is_active">状态</Label>
              <Select
                value={productForm.is_active.toString()}
                onValueChange={(value) => setProductForm(prev => ({ ...prev, is_active: parseInt(value) }))}
              >
                <SelectTrigger id="is_active">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">启用</SelectItem>
                  <SelectItem value="0">禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSubmitProduct} disabled={isSubmitting}>
              {isSubmitting ? '提交中...' : isEditing ? '更新商品' : '创建商品'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  );
};

export default ProductList;