import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Copy, RefreshCw, Check, CreditCard, CheckCircle, Zap } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../components/ui/use-toast';
import { authApi } from '../lib/api';
import { productApi, Product } from '../lib/productApi';
import PopularFeatures from '../components/PopularFeatures';
import PaymentModal from '../components/PaymentModal';
import { useNavigate } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const { user, setUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isCopied, setIsCopied] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pricingPlans, setPricingPlans] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);


  const handleCopyApiKey = async () => {
    // 复制API Key逻辑
    if (user?.api_key) {
      try {
        await navigator.clipboard.writeText(user.api_key);
        setIsCopied(true);
        // 显示成功提示
        toast({
          variant: "success",
          title: "复制成功",
          description: "API Key 已复制到剪贴板",
          duration: 2000,
        });
        // 2秒后重置状态
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      } catch (err) {
        console.error('复制失败:', err);
        toast({
          title: "复制失败",
          description: "无法复制到剪贴板，请手动复制",
          variant: "destructive",
        });
      }
    }
  };

  const handleRefreshApiKey = async () => {
    if (!user) return;
    
    setIsRefreshing(true);
    try {
      const response = await authApi.refreshApiKey();
      
      // 更新用户状态中的API Key
      setUser({
        ...user,
        api_key: response.data?.api_key || ''
      });
      
      // 显示成功提示
      toast({
        variant: "success",
        title: "更新成功",
        description: response.data?.message || "API Key 已成功更新",
        duration: 3000,
      });
    } catch (error) {
      console.error('刷新API Key失败:', error);
      toast({
        title: "更新失败",
        description: "无法更新API Key，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // 获取商品数据
  const fetchPricingPlans = async () => {
    setIsLoadingProducts(true);
    try {
      const response = await productApi.getProducts('', 1, 100, 1); // 获取所有活跃商品
      if (response.success && response.data) {
        const activeProducts = response.data.products
          .filter(product => product.is_active === 1)
          .sort((a, b) => a.price - b.price); // 按价格排序
        setPricingPlans(activeProducts);
      } else {
        toast({
          variant: "destructive",
          title: "获取商品失败",
          description: response.message || "请稍后重试",
        });
      }
    } catch (error) {
      console.error('获取商品失败:', error);
      toast({
        variant: "destructive",
        title: "获取商品失败",
        description: "网络错误，请稍后重试",
      });
    } finally {
      setIsLoadingProducts(false);
    }
  };

  // 获取用户最新信息
  const fetchUserInfo = async () => {
    try {
      const response = await authApi.getCurrentUser();
      if (response.success && response.data) {
        setUser(response.data.user);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };



  const handlePurchase = (product: Product) => {
    setSelectedProduct(product);
    setIsPaymentModalOpen(true);
  };

  const handlePaymentSuccess = () => {
    setIsPaymentModalOpen(false);
    setSelectedProduct(null);
    
    // 延迟刷新用户信息，确保后端处理完成
    setTimeout(() => {
      fetchUserInfo();
    }, 1000);
    
    toast({
      title: '支付成功！',
      description: '积分已添加到您的账户，感谢您的购买！',
      variant: 'success',
    });
  };

  const handlePaymentModalClose = () => {
    setIsPaymentModalOpen(false);
    setSelectedProduct(null);
  };

  useEffect(() => {
    fetchUserInfo(); // 获取最新用户信息
    fetchPricingPlans();

  }, []);



  return (
    <div className="p-6 space-y-6">
      {/* API Key 和当前余额 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Key 区域 */}
        <Card className="p-6 shadow-md border-0 bg-gradient-to-br from-white to-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <div className="w-2.5 h-2.5 bg-blue-500 rounded-full"></div>
              API Key
            </h2>
            {/* <div className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-700 cursor-pointer transition-colors">
              <Zap className="h-4 w-4" />
              <span className="font-medium">配置教程</span>
            </div> */}
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <code className="bg-gray-100 px-3 py-2 rounded text-sm font-mono flex-1 border border-gray-200">
                {user?.api_key || '未获取到API Key'}
              </code>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleCopyApiKey}
                className={`${isCopied ? 'text-green-600 border-green-600 bg-green-50' : 'hover:bg-gray-50'}`}
              >
                {isCopied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefreshApiKey}
                disabled={isRefreshing}
                className={`${isRefreshing ? 'opacity-50' : 'hover:bg-gray-50'}`}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              请妥善保管您的API Key，不要在公开场所分享
            </p>
          </div>
        </Card>

        {/* 当前余额 */}
        <Card className="p-6 shadow-md border-0 bg-gradient-to-br from-blue-50 to-indigo-50">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <div className="w-2.5 h-2.5 bg-blue-500 rounded-full"></div>
              当前余额
            </h2>
            <div className="space-y-1">
              <div 
                className="text-3xl font-bold text-blue-600 cursor-pointer hover:text-blue-700 transition-colors"
                onClick={() => navigate('/profile/points-history')}
                title="点击查看积分变更记录"
              >
                {user?.credits?.toLocaleString() || 0}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                Credits 可用积分
              </div>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <CheckCircle className="h-3.5 w-3.5 text-green-500" />
              <span>积分永不过期</span>
            </div>
          </div>
        </Card>
      </div>

      {/* 充值套餐 */}
      <div className="space-y-6">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
            <CreditCard className="h-6 w-6" />
            充值套餐
          </h2>
          <p className="text-muted-foreground">
            选择合适的积分套餐，通过 Stripe 安全支付
          </p>
        </div>

        {isLoadingProducts ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                <p className="text-muted-foreground">加载商品中...</p>
              </div>
            </CardContent>
          </Card>
        ) : pricingPlans.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <CreditCard className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">暂无可用商品</h3>
                <p className="text-muted-foreground">目前没有可购买的积分套餐</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {pricingPlans.map((plan, index) => {
              const isRecommended = index === Math.floor(pricingPlans.length / 2);
              return (
                <Card key={plan.id} className={`relative ${isRecommended ? 'ring-2 ring-blue-500 shadow-lg scale-105' : ''}`}>
                  {isRecommended && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-500 text-white px-3 py-1">
                        推荐
                      </Badge>
                    </div>
                  )}
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xl">{plan.name}</CardTitle>
                      <Badge variant="secondary">
                        {plan.credits.toLocaleString()} 积分
                      </Badge>
                    </div>
                    {plan.description && (
                      <CardDescription>{plan.description}</CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold">
                          {plan.currency === 'CNY' ? '¥' : '$'}
                          {plan.price.toFixed(2)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          获得 {plan.credits.toLocaleString()} 积分
                        </div>
                      </div>
                      
                      <Button 
                        className={`w-full ${isRecommended ? 'bg-blue-500 hover:bg-blue-600 text-white' : ''}`}
                        onClick={() => handlePurchase(plan)}
                      >
                        <CreditCard className="mr-2 h-4 w-4" />
                        立即购买
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        <div className="mt-12 text-center">
          <div className="inline-flex items-center space-x-2 text-sm text-muted-foreground">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>安全支付由 Stripe 提供保障</span>
          </div>
        </div>
      </div>

      {/* 推荐功能 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Zap className="h-5 w-5" />
          热门功能
        </h2>
        <PopularFeatures 
          showHeader={false}
        />
      </Card>

      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handlePaymentModalClose}
        product={selectedProduct}
        onSuccess={handlePaymentSuccess}
      />
    </div>
  );
};

export default Dashboard;