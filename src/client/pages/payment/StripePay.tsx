import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Loader2, CreditCard, CheckCircle } from 'lucide-react';
import { useToast } from '../../components/ui/use-toast';
import { useAuth } from '../../contexts/AuthContext';
import { productApi } from '../../lib/productApi';
import PopularFeatures from '../../components/PopularFeatures';
import PaymentModal from '../../components/PaymentModal';

interface Product {
	id: number;
	name: string;
	description?: string;
	price: number;
	currency: string;
	credits: number;
	is_active: number;
}

const StripePay: React.FC = () => {
	const [products, setProducts] = useState<Product[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const { toast } = useToast();
	const { user } = useAuth();
	const navigate = useNavigate();
	const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
	const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

	const fetchProducts = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await productApi.getProducts('', 1, 100, 1);
			if (response.success && response.data) {
				const activeProducts = response.data.products.filter((product) => product.is_active === 1);
				// 按价格从小到大排序
				const sortedProducts = activeProducts.sort((a, b) => a.price - b.price);
				setProducts(sortedProducts);
			}
		} catch (error) {
			console.error('获取商品列表失败:', error);
			toast({
				title: '获取商品失败',
				description: '无法加载商品列表，请刷新页面重试。',
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	}, [toast]);



	const handlePurchase = (product: Product) => {
		// 检查用户是否已登录
		if (!user) {
			toast({
				title: '请先登录',
				description: '购买积分需要先登录账户，点击确定跳转到登录页面。',
				variant: 'destructive',
			});
			// 跳转到登录页面，并保存当前页面路径用于登录后返回
			navigate('/login', { state: { from: { pathname: '/payment' } } });
			return;
		}

		setSelectedProduct(product);
		setIsPaymentModalOpen(true);
	};

	const handlePaymentSuccess = () => {
		setIsPaymentModalOpen(false);
		setSelectedProduct(null);
		toast({
			title: '支付成功！',
			description: '积分已添加到您的账户，感谢您的购买！',
			variant: 'success',
		});
	};

	const handlePaymentModalClose = () => {
		setIsPaymentModalOpen(false);
		setSelectedProduct(null);
	};

	useEffect(() => {
		fetchProducts();
	}, [fetchProducts]);

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<Loader2 className="h-8 w-8 animate-spin" />
				<span className="ml-2">加载中...</span>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			<div className="mb-8">
				<h1 className="text-3xl font-bold mb-2">购买积分</h1>
				<p className="text-muted-foreground">选择合适的积分套餐，通过 Stripe 安全支付</p>
			</div>

			{products.length === 0 ? (
				<Card>
					<CardContent className="flex items-center justify-center py-12">
						<div className="text-center">
							<CreditCard className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
							<h3 className="text-lg font-semibold mb-2">暂无可用商品</h3>
							<p className="text-muted-foreground">目前没有可购买的积分套餐</p>
						</div>
					</CardContent>
				</Card>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{products.map((product, index) => {
						const isRecommended = index === Math.floor(products.length / 2);
						return (
							<Card key={product.id} className={`relative ${isRecommended ? 'ring-2 ring-blue-500 shadow-lg scale-105' : ''}`}>
								{isRecommended && (
									<div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
										<Badge className="bg-blue-500 text-white px-3 py-1">推荐</Badge>
									</div>
								)}
								<CardHeader>
									<div className="flex items-center justify-between">
										<CardTitle className="text-xl">{product.name}</CardTitle>
										<Badge variant="secondary">{product.credits} 积分</Badge>
									</div>
									{product.description && <CardDescription>{product.description}</CardDescription>}
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										<div className="text-center">
											<div className="text-3xl font-bold">
												{product.currency === 'CNY' ? '¥' : '$'}
												{product.price.toFixed(2)}
											</div>
											<div className="text-sm text-muted-foreground">获得 {product.credits} 积分</div>
										</div>

										<Button
											className={`w-full ${isRecommended ? 'bg-blue-500 hover:bg-blue-600 text-white' : ''}`}
											onClick={() => handlePurchase(product)}
										>
											<CreditCard className="mr-2 h-4 w-4" />
											立即购买
										</Button>
									</div>
								</CardContent>
							</Card>
						);
					})}
				</div>
			)}
			<div className="mt-12 text-center">
				<div className="inline-flex items-center space-x-2 text-sm text-muted-foreground">
					<CheckCircle className="h-4 w-4 text-green-500" />
					<span>安全支付由 Stripe 提供保障</span>
				</div>
			</div>
			{/* 热门功能列表 */}
			<div className="mt-8">
				<PopularFeatures 
					title="热门功能"
					description="使用积分解锁强大的数据挖掘功能"
					showHeader={true}
				/>
			</div>

			<PaymentModal
				isOpen={isPaymentModalOpen}
				onClose={handlePaymentModalClose}
				product={selectedProduct}
				onSuccess={handlePaymentSuccess}
			/>
		</div>
	);
};

export default StripePay;
