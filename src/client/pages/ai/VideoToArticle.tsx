import React, { useState } from 'react';
import { Upload, FileText, Video, Image } from 'lucide-react';

interface VideoToArticleProps {}

const VideoToArticle: React.FC<VideoToArticleProps> = () => {
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [generatedContent, setGeneratedContent] = useState<{text: string, images: string[]}>({text: '', images: []});
  const [error, setError] = useState<string>('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setVideoFile(e.target.files[0]);
      setVideoUrl('');
      setError('');
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setVideoUrl(e.target.value);
    setVideoFile(null);
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setGeneratedContent({text: '', images: []});

    if (!videoFile && !videoUrl) {
      setError('请上传视频文件或输入视频链接');
      return;
    }

    setIsProcessing(true);

    try {
      // 这里将来会实现与后端API的交互
      // 模拟API调用
      setTimeout(() => {
        setGeneratedContent({
          text: '# 视频内容图文解析\n\n## 视频概述\n\n这是一段关于人工智能应用的视频，主要介绍了AI在日常生活中的应用场景和未来发展趋势。\n\n## 主要内容\n\n1. 人工智能的基本概念和发展历程\n2. 当前AI技术在各行业的应用案例\n3. 未来AI技术的发展方向和可能带来的社会变革\n\n## 详细内容\n\n人工智能技术正在快速发展，从简单的算法到复杂的深度学习网络，AI的能力不断提升。在医疗领域，AI辅助诊断系统可以帮助医生更准确地识别疾病；在金融领域，AI风控系统能够更有效地预测风险；在教育领域，个性化学习平台能够根据学生的学习情况提供定制化的教学内容。\n\n随着技术的进步，AI将会在更多领域发挥作用，包括但不限于自动驾驶、智能家居、个人助理等。这些应用将极大地改变人们的生活方式和工作方式。',
          images: [
            'https://example.com/ai-image1.jpg',
            'https://example.com/ai-image2.jpg',
            'https://example.com/ai-image3.jpg'
          ]
        });
        setIsProcessing(false);
      }, 2000);
    } catch (err) {
      setError('处理视频时出错，请稍后再试');
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">AI 视频转图文</h1>
      
      <div className="bg-card rounded-lg shadow-sm p-6 mb-6">
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">上传视频</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <label className="block text-sm font-medium">本地视频文件</label>
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                      <p className="mb-2 text-sm text-muted-foreground">
                        <span className="font-semibold">点击上传</span> 或拖放文件
                      </p>
                      <p className="text-xs text-muted-foreground">支持MP4, AVI, MOV等格式</p>
                    </div>
                    <input 
                      type="file" 
                      className="hidden" 
                      accept="video/*" 
                      onChange={handleFileChange} 
                    />
                  </label>
                </div>
                {videoFile && (
                  <p className="text-sm text-muted-foreground">
                    已选择: {videoFile.name}
                  </p>
                )}
              </div>
              
              <div className="space-y-4">
                <label className="block text-sm font-medium">在线视频链接</label>
                <input
                  type="url"
                  placeholder="输入视频URL (支持YouTube, Bilibili等)"
                  className="w-full px-4 py-2 border rounded-md bg-background"
                  value={videoUrl}
                  onChange={handleUrlChange}
                />
                <p className="text-xs text-muted-foreground">
                  支持常见视频平台的链接
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isProcessing}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
            >
              {isProcessing ? '处理中...' : '生成图文'}
            </button>
          </div>
        </form>
      </div>
      
      {error && (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
          {error}
        </div>
      )}
      
      {generatedContent.text && (
        <div className="bg-card rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            生成的图文内容
          </h2>
          
          <div className="mb-6">
            <div className="prose max-w-none dark:prose-invert">
              {generatedContent.text.split('\n').map((paragraph, index) => {
                if (paragraph.startsWith('# ')) {
                  return <h1 key={index} className="text-2xl font-bold mt-4 mb-2">{paragraph.substring(2)}</h1>;
                } else if (paragraph.startsWith('## ')) {
                  return <h2 key={index} className="text-xl font-semibold mt-3 mb-2">{paragraph.substring(3)}</h2>;
                } else if (paragraph.startsWith('### ')) {
                  return <h3 key={index} className="text-lg font-medium mt-2 mb-1">{paragraph.substring(4)}</h3>;
                } else if (paragraph.startsWith('1. ') || paragraph.startsWith('2. ') || paragraph.startsWith('3. ')) {
                  return <li key={index} className="ml-5">{paragraph.substring(3)}</li>;
                } else if (paragraph === '') {
                  return <br key={index} />;
                } else {
                  return <p key={index} className="my-2">{paragraph}</p>;
                }
              })}
            </div>
          </div>
          
          {generatedContent.images.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3 flex items-center">
                <Image className="mr-2 h-5 w-5" />
                提取的图片
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {generatedContent.images.map((image, index) => (
                  <div key={index} className="border rounded-md overflow-hidden">
                    <img src={image} alt={`提取图片 ${index + 1}`} className="w-full h-48 object-cover" />
                    <div className="p-2 bg-muted/50">
                      <p className="text-sm text-center">图片 {index + 1}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="mt-6 flex justify-end space-x-2">
            <button className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
              复制文本
            </button>
            <button className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
              下载图文
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoToArticle;