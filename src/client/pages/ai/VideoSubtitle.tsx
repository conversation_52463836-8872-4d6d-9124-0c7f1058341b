import React, { useState } from 'react';
import { Upload, Video } from 'lucide-react';

interface VideoSubtitleProps {}

const VideoSubtitle: React.FC<VideoSubtitleProps> = () => {
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [subtitles, setSubtitles] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setVideoFile(e.target.files[0]);
      setVideoUrl('');
      setError('');
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setVideoUrl(e.target.value);
    setVideoFile(null);
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSubtitles('');

    if (!videoFile && !videoUrl) {
      setError('请上传视频文件或输入视频链接');
      return;
    }

    setIsProcessing(true);

    try {
      // 这里将来会实现与后端API的交互
      // 模拟API调用
      setTimeout(() => {
        setSubtitles('这是AI生成的视频字幕示例。\n00:00:01,000 --> 00:00:05,000\n欢迎使用AI视频字幕生成工具。\n00:00:05,500 --> 00:00:10,000\n这个工具可以帮助您自动生成视频的字幕。');
        setIsProcessing(false);
      }, 2000);
    } catch (err) {
      setError('处理视频时出错，请稍后再试');
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">AI生成视频字幕</h1>
      
      <div className="bg-card rounded-lg shadow-sm p-6 mb-6">
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">上传视频</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <label className="block text-sm font-medium">本地视频文件</label>
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                      <p className="mb-2 text-sm text-muted-foreground">
                        <span className="font-semibold">点击上传</span> 或拖放文件
                      </p>
                      <p className="text-xs text-muted-foreground">支持MP4, AVI, MOV等格式</p>
                    </div>
                    <input 
                      type="file" 
                      className="hidden" 
                      accept="video/*" 
                      onChange={handleFileChange} 
                    />
                  </label>
                </div>
                {videoFile && (
                  <p className="text-sm text-muted-foreground">
                    已选择: {videoFile.name}
                  </p>
                )}
              </div>
              
              <div className="space-y-4">
                <label className="block text-sm font-medium">在线视频链接</label>
                <input
                  type="url"
                  placeholder="输入视频URL (支持YouTube, Bilibili等)"
                  className="w-full px-4 py-2 border rounded-md bg-background"
                  value={videoUrl}
                  onChange={handleUrlChange}
                />
                <p className="text-xs text-muted-foreground">
                  支持常见视频平台的链接
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isProcessing}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
            >
              {isProcessing ? '处理中...' : '生成字幕'}
            </button>
          </div>
        </form>
      </div>
      
      {error && (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
          {error}
        </div>
      )}
      
      {subtitles && (
        <div className="bg-card rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Video className="mr-2 h-5 w-5" />
            生成的字幕
          </h2>
          <div className="bg-muted p-4 rounded-md">
            <pre className="whitespace-pre-wrap text-sm">{subtitles}</pre>
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <button className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
              复制
            </button>
            <button className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
              下载SRT
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoSubtitle;