import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import { useAuth } from '../contexts/AuthContext';
import { configApi, emailApi } from '../lib/api';

export default function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [api_key, setApiKey] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loginMethod, setLoginMethod] = useState<'password' | 'api_key' | 'google'>('api_key');
  
  // 邮箱登录相关状态
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [emailStep, setEmailStep] = useState<'input' | 'verify'>('input');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [countdown, setCountdown] = useState(0);

  // 如果是从Home页面（根路径）过来的，或者没有指定来源页面，都跳转到dashboard
  const from = location.state?.from?.pathname === '/' ? '/dashboard' : (location.state?.from?.pathname || '/dashboard');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // 由于界面只显示API KEY输入框，直接使用API KEY登录
      await login({
        loginType: 'api_key',
        api_key: api_key
      });
      navigate(from, { replace: true });
    } catch (err: any) {
      setError(err.message || '登录失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 邮箱登录相关函数
  const handleEmailLogin = () => {
    setIsEmailDialogOpen(true);
    setEmailStep('input');
    setEmail('');
    setVerificationCode('');
    setEmailError('');
  };

  const handleSendVerificationCode = async () => {
    if (!email) {
      setEmailError('请输入邮箱地址');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError('请输入有效的邮箱地址');
      return;
    }

    setIsEmailLoading(true);
    setEmailError('');

    try {
      const response = await emailApi.sendVerificationCode(email);
      if (response.success) {
        setEmailStep('verify');
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setEmailError(response.message || '发送验证码失败');
      }
    } catch (error: any) {
      setEmailError(error.message || '发送验证码失败，请稍后重试');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode) {
      setEmailError('请输入验证码');
      return;
    }

    if (verificationCode.length !== 6) {
      setEmailError('验证码必须是6位数字');
      return;
    }

    setIsEmailLoading(true);
    setEmailError('');

    try {
      // 使用统一的login接口进行邮箱验证码登录
      await login({
        loginType: 'email',
        email: email,
        code: verificationCode
      });
      
      // 关闭弹窗
      setIsEmailDialogOpen(false);
      
      // 跳转到目标页面
      navigate(from, { replace: true });
    } catch (error: any) {
      setEmailError(error.message || '验证失败，请稍后重试');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleBackToEmailInput = () => {
    setEmailStep('input');
    setVerificationCode('');
    setEmailError('');
  };

  const handleCloseEmailDialog = () => {
    setIsEmailDialogOpen(false);
    setEmailStep('input');
    setEmail('');
    setVerificationCode('');
    setEmailError('');
    setCountdown(0);
  };

  const handleGoogleLogin = async () => {
    setError('');
    setIsLoading(true);

    try {
      // 从后端获取 Google OAuth 配置
      const config = await configApi.getPublicConfig();
      const googleClientId = config.data?.google?.clientId;
      
      if (!googleClientId) {
        throw new Error('Google Client ID 未配置');
      }
      
      const redirectUri = `${window.location.origin}/auth/google/callback`;
      const scope = 'openid email profile';
      const responseType = 'code';
      const state = Math.random().toString(36).substring(2, 15);
      
      // 构建 Google OAuth URL
      const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${googleClientId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=${encodeURIComponent(scope)}&` +
        `response_type=${responseType}&` +
        `state=${state}`;
      
      // 打开弹窗进行 Google 登录
      const popup = window.open(
        googleAuthUrl,
        'google-login',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );
      
      if (!popup) {
        throw new Error('无法打开登录弹窗，请检查浏览器弹窗设置');
      }
      
      // 监听弹窗消息
      const handleMessage = async (event: MessageEvent) => {
        if (event.origin !== window.location.origin) {
          return;
        }
        
        if (event.data.type === 'GOOGLE_AUTH_SUCCESS') {
          const { access_token } = event.data;
          
          try {
            // 使用 access_token 进行登录
            await login({
              loginType: 'google',
              google_token: access_token
            });
            navigate(from, { replace: true });
          } catch (error) {
            console.error('Google 登录失败:', error);
            setError('Google 登录失败，请重试');
          }
          
          popup.close();
          window.removeEventListener('message', handleMessage);
        } else if (event.data.type === 'GOOGLE_AUTH_ERROR') {
          setError(event.data.error || 'Google 登录失败');
          popup.close();
          window.removeEventListener('message', handleMessage);
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      // 检查弹窗是否被关闭
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          setIsLoading(false);
        }
      }, 1000);
      
    } catch (error) {
      console.error('Google 登录错误:', error);
      setError('Google 登录失败，请重试');
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle className="text-center">登录</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
                {error}
              </div>
            )}
            
            {/* Google 登录 - 主要登录方式 */}
            <div className="space-y-4">
              <Button 
                type="button" 
                className="w-full bg-blue-600 hover:bg-blue-700 text-white" 
                disabled={isLoading}
                onClick={handleGoogleLogin}
              >
                <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                {isLoading ? '登录中...' : '使用 Google 登录/注册'}
              </Button>
              
              <Button
                type="button"
                onClick={handleEmailLogin}
                className="w-full bg-green-600 hover:bg-green-700 text-white mt-2"
                disabled={isLoading}
              >
                使用邮箱登录/注册
              </Button>
            </div>

            {/* 分隔线 */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">或</span>
              </div>
            </div>

            {/* 其他登录方式选择 */}
            <div className="space-y-3">
              {/* 暂时注释用户名密码登录方式 */}
               {/* <div className="flex justify-center space-x-2 mb-4">
                 <Button
                   type="button"
                   variant={loginMethod === 'password' ? 'default' : 'outline'}
                   onClick={() => setLoginMethod('password')}
                   className="text-xs px-4 py-2"
                 >
                   用户名密码
                 </Button>
                 <Button
                   type="button"
                   variant={loginMethod === 'api_key' ? 'default' : 'outline'}
                   onClick={() => setLoginMethod('api_key')}
                   className="text-xs px-4 py-2"
                 >
                   API Key
                 </Button>
               </div> */}

               {/* 只保留API Key登录方式 */}
               <div className="space-y-2">
                 <label htmlFor="api_key" className="text-sm font-medium">
                   API KEY
                 </label>
                 <Input
                   id="api_key"
                   type="text"
                   value={api_key}
                   onChange={(e) => setApiKey(e.target.value)}
                   placeholder="请输入 API KEY"
                   required
                   disabled={isLoading}
                 />
               </div>
               <Button type="submit" className="w-full" disabled={isLoading}>
                 {isLoading ? '登录中...' : '登录'}
               </Button>

               {/* 暂时注释用户名密码登录表单 */}
               {/* {loginMethod === 'password' && (
                 <>
                   <div className="space-y-2">
                     <label htmlFor="username" className="text-sm font-medium">
                       用户名
                     </label>
                     <Input
                       id="username"
                       type="text"
                       value={username}
                       onChange={(e) => setUsername(e.target.value)}
                       placeholder="请输入用户名"
                       required
                       disabled={isLoading}
                     />
                   </div>
                   <div className="space-y-2">
                     <label htmlFor="password" className="text-sm font-medium">
                       密码
                     </label>
                     <Input
                       id="password"
                       type="password"
                       value={password}
                       onChange={(e) => setPassword(e.target.value)}
                       placeholder="请输入密码"
                       required
                       disabled={isLoading}
                     />
                   </div>
                   <Button type="submit" className="w-full" disabled={isLoading}>
                     {isLoading ? '登录中...' : '登录'}
                   </Button>
                 </>
               )} */}
            </div>
          </form>
        </CardContent>
      </Card>
      
      <Dialog open={isEmailDialogOpen} onOpenChange={handleCloseEmailDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {emailStep === 'input' ? '邮箱登录/注册' : '验证邮箱'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {emailStep === 'input' ? (
              // 输入邮箱步骤
              <>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    邮箱地址
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入您的邮箱地址"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isEmailLoading}
                  />
                </div>
                
                {emailError && (
                  <div className="text-red-500 text-sm">{emailError}</div>
                )}
                
                <div className="flex space-x-2">
                  <Button
                    onClick={handleCloseEmailDialog}
                    variant="outline"
                    className="flex-1"
                    disabled={isEmailLoading}
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleSendVerificationCode}
                    className="flex-1"
                    disabled={isEmailLoading || !email}
                  >
                    {isEmailLoading ? '发送中...' : '发送验证码'}
                  </Button>
                </div>
              </>
            ) : (
              // 验证码验证步骤
              <>
                <div className="text-sm text-gray-600 mb-4">
                  验证码已发送至 <span className="font-medium">{email}</span>
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="code" className="text-sm font-medium">
                    验证码
                  </label>
                  <Input
                    id="code"
                    type="text"
                    placeholder="请输入6位验证码"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    disabled={isEmailLoading}
                    maxLength={6}
                  />
                </div>
                
                {emailError && (
                  <div className="text-red-500 text-sm">{emailError}</div>
                )}
                
                <div className="flex space-x-2">
                  <Button
                    onClick={handleBackToEmailInput}
                    variant="outline"
                    className="flex-1"
                    disabled={isEmailLoading}
                  >
                    返回
                  </Button>
                  <Button
                    onClick={countdown > 0 ? undefined : handleSendVerificationCode}
                    variant="outline"
                    disabled={isEmailLoading || countdown > 0}
                  >
                    {countdown > 0 ? `重发(${countdown}s)` : '重新发送'}
                  </Button>
                  <Button
                    onClick={handleVerifyCode}
                    className="flex-1"
                    disabled={isEmailLoading || verificationCode.length !== 6}
                  >
                    {isEmailLoading ? '验证中...' : '登录'}
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}