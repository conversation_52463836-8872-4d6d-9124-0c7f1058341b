import React, { useState, useEffect, useRef } from 'react';
import { Card } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { CheckCircle, Star, ArrowRight, Shield, Database, ChevronDown, Mail, BookOpen, Package, DollarSign, LayoutDashboard, Send } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { UserAvatar } from '../components/layout/UserAvatar';
import { useAuth } from '../contexts/AuthContext';
import { useDataExtractionMenuItems, useHomeFeatures } from '../components/common/MenuItems';

const Home: React.FC = () => {
	const { user } = useAuth();
	const navigate = useNavigate();
	const isAuthenticated = !!user;
	const username = user?.username || '';
	const [showProductMenu, setShowProductMenu] = useState(false);
	const [menuAnimation, setMenuAnimation] = useState(false);
	const menuRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
				setShowProductMenu(false);
			}
		};

		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === 'Escape' && showProductMenu) {
				setShowProductMenu(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		document.addEventListener('keydown', handleKeyDown);

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [showProductMenu]);

	useEffect(() => {
		if (showProductMenu) {
			// 菜单显示后立即触发动画
			setTimeout(() => setMenuAnimation(true), 10);
		} else {
			setMenuAnimation(false);
		}
	}, [showProductMenu]);

	// 关闭菜单的函数
	const closeMenu = () => {
		setShowProductMenu(false);
	};

	const productMenuItems = useDataExtractionMenuItems();

	const features = useHomeFeatures();

	const useCases = [
		{
			title: '小红书话题研究',
			description: '通过话题搜索笔记功能，深入分析热门话题趋势，挖掘用户关注点和内容偏好',
		},
		{
			title: '抖音评论分析',
			description: '提取视频评论列表，分析用户反馈和互动情况，优化内容策略和用户体验',
		},
		{
			title: 'TikTok粉丝洞察',
			description: '获取账号粉丝数据，了解粉丝画像和行为特征，制定精准的海外营销策略',
		},
		{
			title: 'YouTube内容优化',
			description: '分析播放列表视频表现，优化视频内容和发布策略，提升频道影响力',
		},
		{
			title: '快手数据监控',
			description: '批量提取快手视频数据和评论信息，监控内容表现和用户反馈',
		},
		{
			title: '多平台数据整合',
			description: '整合各平台数据，构建完整的用户画像和内容分析体系',
		},
	];

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
			{/* Navigation */}
			<nav className="bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50 shadow-md w-full">
				<div className="w-full px-6 py-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<img src="/favicon.png" alt="DataMiner Logo" className="w-10 h-10" />
							<span className="text-2xl font-bold text-gray-900 tracking-tight bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
								DataMiner
							</span>
						</div>
						<div className="flex items-center gap-6">
							<div className="relative" ref={menuRef}>
								<button
									className="flex items-center gap-1.5 text-gray-700 hover:text-blue-600 transition-colors px-4 py-2 rounded-md hover:bg-blue-50 font-medium"
									onClick={() => setShowProductMenu(!showProductMenu)}
									aria-expanded={showProductMenu}
									aria-haspopup="true"
									aria-controls="product-menu"
									aria-label="产品菜单"
								>
									<Package className="h-4 w-4" />
									产品
									<ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showProductMenu ? 'transform rotate-180' : ''}`} />
								</button>
								{showProductMenu && (
									<div
										id="product-menu"
										role="menu"
										className={`absolute top-full right-0 mt-2 w-[650px] bg-white rounded-xl shadow-2xl border border-gray-100 z-50 overflow-hidden transition-all duration-200 ${
											menuAnimation ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'
										}`}
									>
										<div className="p-6 max-h-[80vh] overflow-y-auto">
											<div className="grid grid-cols-3 gap-6">
												{productMenuItems.map((item) => (
													<div key={item.title} className="group bg-gray-50/50 rounded-lg p-3 hover:bg-blue-50/50 transition-colors">
														<div
															className="flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-900 border-b border-gray-100 pb-2 mb-2 group-hover:text-blue-600 transition-colors"
															role="presentation"
														>
															<div className="p-1.5 bg-white rounded-md shadow-sm group-hover:bg-blue-100/50 transition-colors">
																{item.icon}
															</div>
															<span className="font-semibold">{item.title}</span>
														</div>
														<div className="space-y-1.5">
															{item.children?.map(
																(child) =>
																	child.path && (
																		<Link
																			key={child.path}
																			to={child.path}
																			className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:bg-white hover:text-blue-600 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 hover:shadow-sm"
																			onClick={closeMenu}
																			role="menuitem"
																		>
																			<div className="p-1 bg-gray-100 rounded-md group-hover:bg-blue-100/50">{child.icon}</div>
																			<span>{child.title}</span>
																		</Link>
																	)
															)}
														</div>
													</div>
												))}
											</div>
											<div className="mt-5 pt-4 border-t border-gray-100 flex justify-between items-center">
												<span className="text-xs text-gray-500">探索更多数据采集工具</span>
												<Link
													to="/dashboard"
													className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1 font-medium hover:underline"
													onClick={closeMenu}
												>
													查看全部 <ArrowRight className="h-3 w-3" />
												</Link>
											</div>
										</div>
									</div>
								)}
							</div>
							<Link
								to="/docs"
								className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors px-4 py-2 rounded-md hover:bg-blue-50 font-medium"
							>
								<BookOpen className="h-4 w-4" />
								文档
							</Link>
							<Link
								to="/payment/stripe"
								className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors px-4 py-2 rounded-md hover:bg-blue-50 font-medium"
							>
								<DollarSign className="h-4 w-4" />
								价格
							</Link>
							<Link
								to="/dashboard"
								className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors px-4 py-2 rounded-md hover:bg-blue-50 font-medium"
							>
								<LayoutDashboard className="h-4 w-4" />
								工作台
							</Link>
							<UserAvatar isAuthenticated={isAuthenticated} username={username} />
						</div>
					</div>
				</div>
			</nav>

			{/* Hero Section */}
			<section className="relative px-6 py-28 bg-gradient-to-br from-blue-50 via-indigo-50 to-white overflow-hidden">
				<div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-blue-300/10 to-indigo-300/10 rounded-full blur-3xl transform translate-x-1/4 -translate-y-1/4"></div>
				<div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-blue-300/10 to-purple-300/10 rounded-full blur-3xl transform -translate-x-1/4 translate-y-1/4"></div>

				<div className="w-full relative z-10">
					<div className="max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center">
						<div className="text-left">
							<div className="inline-block px-4 py-1.5 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-8 shadow-sm">
								<span className="flex items-center gap-2">
									<span className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
									全平台数据采集工具
								</span>
							</div>
							<h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-8 leading-tight">
								多平台社交媒体数据
								<span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent block">一站式采集</span>
							</h1>
							<p className="text-xl text-gray-600 mb-10 leading-relaxed">
								支持小红书、抖音、TikTok、YouTube、快手等主流平台，提供话题搜索、账号数据、视频批量提取、评论分析、粉丝数据等全方位数据服务。
							</p>
							<div className="flex items-center gap-4 mb-10 p-3 bg-gray-50 rounded-lg border border-gray-100 w-fit">
								<div className="flex">
									{[...Array(5)].map((_, i) => (
										<Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
									))}
								</div>
								<span className="text-gray-700 font-medium">已被 1000+ 用户使用</span>
							</div>
							<div className="flex flex-col sm:flex-row gap-5">
								<Link to="/dashboard">
									<Button
										size="lg"
										className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-xl shadow-lg shadow-blue-200/50 font-medium text-base"
									>
										开始使用
										<ArrowRight className="ml-2 w-5 h-5" />
									</Button>
								</Link>
								<Button
									variant="outline"
									size="lg"
									className="px-8 py-4 border-2 rounded-xl border-gray-300 hover:border-blue-400 hover:bg-blue-50 text-base font-medium"
									onClick={() => navigate('/docs')}
								>
									文档
								</Button>
							</div>
						</div>
						<div className="relative hidden md:block">
							<div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl opacity-10 blur-3xl"></div>
							<div className="relative bg-white p-8 rounded-2xl shadow-2xl border border-gray-100">
								<div className="flex items-center justify-between mb-6">
									<div className="flex items-center gap-2">
										<div className="w-3 h-3 rounded-full bg-red-500"></div>
										<div className="w-3 h-3 rounded-full bg-yellow-500"></div>
										<div className="w-3 h-3 rounded-full bg-green-500"></div>
									</div>
									<div className="text-xs text-gray-500">数据采集控制台</div>
								</div>
								<div className="grid grid-cols-2 gap-5">
									{features.slice(0, 4).map((feature, index) => (
										<div
											key={index}
											className="flex items-start gap-4 p-4 rounded-xl hover:bg-blue-50/50 transition-all duration-300 border border-transparent hover:border-blue-100 group"
										>
											<div className="p-2.5 bg-gray-50 rounded-lg group-hover:bg-white group-hover:shadow-md transition-all duration-300">
												{feature.icon}
											</div>
											<div>
												<h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{feature.title}</h3>
												<p className="text-xs text-gray-500 mt-1 line-clamp-2 group-hover:text-gray-700 transition-colors">
													{feature.description}
												</p>
											</div>
										</div>
									))}
								</div>
								<div className="mt-6 pt-4 border-t border-gray-100 flex justify-between items-center">
									<div className="text-xs text-gray-500">数据实时更新中...</div>
									<div className="flex items-center gap-2">
										<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
										<span className="text-xs text-green-600 font-medium">在线</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Features Section */}
			<section className="px-6 py-32 bg-white">
				<div className="w-full">
					<div className="max-w-6xl mx-auto">
						<div className="text-center mb-20">
							<div className="inline-flex items-center gap-2 px-4 py-1.5 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6 shadow-sm">
								<span className="w-2 h-2 bg-blue-600 rounded-full"></span>
								<span>功能特性</span>
							</div>
							<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-900 via-blue-800 to-gray-900 bg-clip-text text-transparent">
								强大的数据采集能力
							</h2>
							<p className="text-gray-600 text-lg max-w-3xl mx-auto">覆盖主流社交媒体平台的专业数据提取工具，满足您的全方位数据需求</p>
						</div>
						<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
							{features.map(
								(feature, index) =>
									feature.link && (
										<Link key={index} to={feature.link} className="block group">
											<Card className="p-8 hover:shadow-2xl transition-all duration-300 hover:translate-y-[-8px] cursor-pointer border-gray-100 group-hover:border-blue-100 rounded-xl overflow-hidden relative">
												<div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
												<div className="relative z-10">
													<div className="mb-6 p-4 bg-gray-50 rounded-xl inline-block group-hover:bg-white group-hover:shadow-md transition-all duration-300 border border-transparent group-hover:border-blue-100">
														{feature.icon}
													</div>
													<h3 className="text-xl font-semibold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
														{feature.title}
													</h3>
													<p className="text-gray-600 mb-5 min-h-[48px]">{feature.description}</p>
													<div className="flex items-center text-blue-600 text-sm font-medium group-hover:translate-x-2 transition-all duration-300">
														立即使用 <ArrowRight className="ml-2 w-4 h-4 group-hover:ml-3 transition-all duration-300" />
													</div>
												</div>
											</Card>
										</Link>
									)
							)}
						</div>
					</div>
				</div>
			</section>

			{/* Use Cases Section */}
			<section className="px-6 py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
				<div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white to-transparent"></div>
				<div className="absolute -top-24 -right-24 w-96 h-96 bg-blue-100/30 rounded-full blur-3xl"></div>
				<div className="absolute -bottom-24 -left-24 w-96 h-96 bg-indigo-100/30 rounded-full blur-3xl"></div>

				<div className="w-full relative z-10">
					<div className="max-w-6xl mx-auto">
						<div className="flex flex-col md:flex-row gap-16 items-center mb-20">
							<div className="md:w-1/2">
								<div className="inline-flex items-center gap-2 px-4 py-1.5 bg-green-100 text-green-800 rounded-full text-sm font-medium mb-6 shadow-sm">
									<span className="w-2 h-2 bg-green-600 rounded-full"></span>
									<span>应用场景</span>
								</div>
								<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-900 via-green-800 to-gray-900 bg-clip-text text-transparent">
									多种业务场景应用
								</h2>
								<p className="text-gray-600 text-lg leading-relaxed">
									适用于内容营销、竞品分析、达人合作等多种业务场景，助力您的业务决策和数据驱动增长
								</p>
							</div>
							<div className="md:w-1/2 bg-white p-8 rounded-2xl shadow-xl border border-gray-100 transform md:rotate-1 hover:rotate-0 transition-transform duration-500">
								<div className="flex items-center justify-between mb-6">
									<div className="flex items-center gap-2">
										<div className="w-3 h-3 rounded-full bg-red-500"></div>
										<div className="w-3 h-3 rounded-full bg-yellow-500"></div>
										<div className="w-3 h-3 rounded-full bg-green-500"></div>
									</div>
									<div className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-md">数据采集面板</div>
								</div>
								<div className="h-56 bg-gradient-to-br from-gray-50 to-white rounded-xl flex items-center justify-center border border-gray-100 relative overflow-hidden">
									<div className="absolute inset-0 bg-grid-gray-200/50"></div>
									<div className="relative z-10 text-center p-6">
										<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
											<Database className="w-8 h-8 text-green-600" />
										</div>
										<h4 className="text-lg font-semibold text-gray-900 mb-2">数据采集中心</h4>
										<p className="text-gray-500 text-sm max-w-xs mx-auto">实时监控数据采集进度，多平台数据一站式管理</p>
										<div className="mt-4 flex items-center justify-center gap-2">
											<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
											<span className="text-xs text-green-600 font-medium">数据同步中</span>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
							{useCases.map((useCase, index) => (
								<div
									key={index}
									className="bg-white p-8 rounded-xl shadow-md border border-gray-100 hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group"
								>
									<div className="flex items-start gap-5">
										<div className="p-3 bg-green-50 rounded-xl group-hover:bg-green-100 transition-colors">
											<CheckCircle className="w-7 h-7 text-green-500 flex-shrink-0 group-hover:text-green-600 transition-colors" />
										</div>
										<div>
											<h3 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-green-700 transition-colors">
												{useCase.title}
											</h3>
											<p className="text-gray-600 group-hover:text-gray-700 transition-colors">{useCase.description}</p>
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="px-6 py-32 bg-gradient-to-r from-blue-600 via-indigo-600 to-indigo-700 text-white relative overflow-hidden">
				<div className="absolute inset-0 bg-pattern-grid opacity-10"></div>
				<div className="absolute top-0 right-0 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl"></div>
				<div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl"></div>

				<div className="w-full relative z-10">
					<div className="max-w-6xl mx-auto">
						<div className="flex flex-col md:flex-row items-center justify-between gap-16">
							<div className="md:w-2/3">
								<div className="inline-flex items-center gap-2 px-4 py-1.5 bg-white/20 text-white rounded-full text-sm font-medium mb-6 backdrop-blur-sm">
									<span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
									<span>立即行动</span>
								</div>
								<h2 className="text-3xl md:text-5xl font-bold mb-6 leading-tight">准备开始了吗？</h2>
								<p className="text-xl mb-6 text-blue-100 max-w-2xl">30 秒内获取您的 API 密钥。无需信用卡，立即开始构建您的数据采集方案。</p>
								<div className="flex flex-wrap gap-5">
									<Link to="/dashboard">
										<Button
											size="lg"
											variant="secondary"
											className="px-8 py-4 shadow-xl hover:shadow-blue-200/30 transition-all duration-300"
										>
											立即开始
											<ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
										</Button>
									</Link>
									<Link to="/payment/stripe">
										<Button
											size="lg"
											variant="secondary"
											className="px-8 py-4 shadow-xl hover:shadow-blue-200/30 transition-all duration-300"
										>
											查看价格
										</Button>
									</Link>
								</div>
							</div>
							<div className="md:w-1/3 flex justify-center">
								<div className="w-32 h-32 rounded-full bg-white/20 flex items-center justify-center backdrop-blur-sm shadow-xl hover:scale-105 transition-transform duration-300">
									<Database className="w-16 h-16 text-white" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className="px-6 py-32 bg-white relative overflow-hidden">
				<div className="absolute -top-24 left-1/2 transform -translate-x-1/2 w-[800px] h-[800px] bg-blue-50/50 rounded-full blur-3xl opacity-70"></div>

				<div className="w-full relative z-10">
					<div className="max-w-6xl mx-auto">
						<div className="text-center mb-20">
							<div className="inline-flex items-center gap-2 px-4 py-1.5 bg-purple-100 text-purple-800 rounded-full text-sm font-medium mb-6 shadow-sm">
								<span className="w-2 h-2 bg-purple-600 rounded-full"></span>
								<span>常见问题</span>
							</div>
							<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-900 via-purple-800 to-gray-900 bg-clip-text text-transparent">
								您可能想知道的
							</h2>
							<p className="text-gray-600 text-lg max-w-3xl mx-auto">我们整理了一些用户常见的问题，希望能帮助您更好地了解我们的服务</p>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							<div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl border border-gray-100 transition-all duration-300 hover:-translate-y-1">
								<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-3">
									<div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-sm shadow-sm">
										Q
									</div>
									支持哪些平台和功能？
								</h3>
								<p className="text-gray-600 ml-11">
									支持小红书（话题搜索笔记、账号笔记提取）、抖音（账号作品、视频批量、评论列表）、TikTok（账号视频、粉丝数据、视频批量）、YouTube（账号作品、播放列表、视频批量）、快手（视频批量、评论列表）等功能。
								</p>
							</div>
							<div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl border border-gray-100 transition-all duration-300 hover:-translate-y-1">
								<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-3">
									<div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-sm shadow-sm">
										Q
									</div>
									数据更新频率如何？
								</h3>
								<p className="text-gray-600 ml-11">数据实时获取，确保您始终获得最新的平台信息和用户内容。</p>
							</div>
							<div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl border border-gray-100 transition-all duration-300 hover:-translate-y-1">
								<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-3">
									<div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-sm shadow-sm">
										Q
									</div>
									可以获取哪些数据？
								</h3>
								<p className="text-gray-600 ml-11">
									包括话题搜索结果、用户作品列表、视频详细信息、评论数据、粉丝信息、播放列表等多维度数据信息，支持CSV格式导出。
								</p>
							</div>
							<div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl border border-gray-100 transition-all duration-300 hover:-translate-y-1">
								<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-3">
									<div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-sm shadow-sm">
										Q
									</div>
									如何使用积分系统？
								</h3>
								<p className="text-gray-600 ml-11">
									平台采用积分制，不同功能消耗不同积分。您可以在个人中心查看积分变更记录和付款记录，支持在线购买积分。
								</p>
							</div>
						</div>
						<div className="mt-16 text-center">
							<Link
								to="/dashboard"
								className="inline-flex items-center gap-2 px-6 py-3 bg-purple-50 text-purple-700 rounded-full font-medium hover:bg-purple-100 transition-colors shadow-sm hover:shadow-md"
							>
								查看更多常见问题 <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
							</Link>
						</div>
					</div>
				</div>
			</section>

			{/* Footer */}
			<footer className="px-6 pt-20 pb-5 bg-gradient-to-b from-gray-900 to-gray-950 text-white relative overflow-hidden">
				<div className="absolute inset-0 bg-grid-white/5 pointer-events-none"></div>
				<div className="absolute -top-24 -right-24 w-96 h-96 bg-blue-900/20 rounded-full blur-3xl"></div>
				<div className="absolute -bottom-24 -left-24 w-96 h-96 bg-indigo-900/20 rounded-full blur-3xl"></div>

				<div className="w-full relative z-10">
					<div className="max-w-6xl mx-auto">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-12 lg:gap-16">
							<div className="col-span-1 md:col-span-1">
								<div className="flex items-center gap-3 mb-6">
									<div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg shadow-blue-900/20">
										<Database className="w-6 h-6 text-white" />
									</div>
									<span className="text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
										DataMiner
									</span>
								</div>
								<p className="text-gray-300 mb-8 text-lg max-w-md leading-relaxed">
									专业的社交媒体数据采集工具，助力您的业务增长和数据驱动决策
								</p>

								<div className="space-y-3">
									<div className="flex items-center gap-3 bg-gray-800/50 p-4 rounded-xl backdrop-blur-sm">
										<Mail className="w-5 h-5 text-blue-400 flex-shrink-0" />
										<a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors text-sm">
											<EMAIL>
										</a>
									</div>
									<div className="flex items-center gap-3 bg-gray-800/50 p-4 rounded-xl backdrop-blur-sm">
										<Send className="w-5 h-5 text-blue-400 flex-shrink-0" />
										<a href="https://t.me/datatoolvip" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white transition-colors text-sm">
											@datatoolvip
										</a>
									</div>
								</div>
							</div>
							<div>
								<h3 className="font-semibold mb-6 text-xl text-white flex items-center gap-2">
									<span className="w-1 h-6 bg-blue-500 rounded-full"></span>产品
								</h3>
								<ul className="space-y-4">
									<li>
										<Link
											to="/tools/topic-search"
											className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1"
										>
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>红薯取数</span>
										</Link>
									</li>
									<li>
										<Link
											to="/tools/douyin/user-videos"
											className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1"
										>
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>抖音取数</span>
										</Link>
									</li>
									<li>
										<Link
											to="/tools/tiktok/user-videos"
											className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1"
										>
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>TikTok取数</span>
										</Link>
									</li>
									<li>
										<Link
											to="/tools/youtube/user-videos"
											className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1"
										>
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>YouTube取数</span>
										</Link>
									</li>
								</ul>
							</div>
							<div>
								<h3 className="font-semibold mb-6 text-xl text-white flex items-center gap-2">
									<span className="w-1 h-6 bg-blue-500 rounded-full"></span>资源
								</h3>
								<ul className="space-y-4">
									<li>
										<Link to="/dashboard" className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1">
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>工作台</span>
										</Link>
									</li>
									<li>
										<Link to="/docs" className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1">
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>API文档</span>
										</Link>
									</li>
									<li>
										<Link
											to="/payment/stripe"
											className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1"
										>
											<ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
											<span>价格</span>
										</Link>
									</li>
									{/* <li>
                  <Link to="/api-docs" className="text-gray-300 hover:text-white transition-colors flex items-center gap-2 group py-1">
                    <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1" />
                    <span>API文档</span>
                  </Link>
                </li> */}
								</ul>
							</div>
						</div>

						<div className="border-t border-gray-800 mt-16 pt-12 pb-8 flex flex-col md:flex-row justify-between items-center gap-8">
							<div className="flex flex-col md:flex-row items-center md:items-center gap-6 w-full md:w-auto">
								<div className="flex items-center gap-2 text-gray-400 text-sm">
									<span className="text-gray-500">© {new Date().getFullYear()} DataMiner.</span>
									<span className="text-gray-600">保留所有权利</span>
								</div>
								<div className="h-4 w-px bg-gray-800 hidden md:block"></div>
								<div className="flex items-center gap-2 text-sm text-gray-400 hover:text-blue-400 transition-colors">
									<Shield className="w-3.5 h-3.5 text-blue-500" />
									<span>企业级安全保障</span>
								</div>
							</div>
							<div className="flex flex-wrap justify-center gap-x-8 gap-y-4 text-sm w-full md:w-auto">
								<Link
									to="#"
									className="text-gray-400 hover:text-blue-400 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 hover:after:w-full after:bg-blue-500 after:transition-all after:duration-300 px-1 py-0.5"
								>
									隐私政策
								</Link>
								<Link
									to="#"
									className="text-gray-400 hover:text-blue-400 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 hover:after:w-full after:bg-blue-500 after:transition-all after:duration-300 px-1 py-0.5"
								>
									服务条款
								</Link>
								<Link
									to="#"
									className="text-gray-400 hover:text-blue-400 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 hover:after:w-full after:bg-blue-500 after:transition-all after:duration-300 px-1 py-0.5"
								>
									Cookie 政策
								</Link>
								<Link
									to="#"
									className="text-gray-400 hover:text-blue-400 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 hover:after:w-full after:bg-blue-500 after:transition-all after:duration-300 px-1 py-0.5"
								>
									使用指南
								</Link>
							</div>
						</div>
					</div>
				</div>
			</footer>
		</div>
	);
};

export default Home;
