import React, { useState, useRef, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Download } from 'lucide-react';
import { topicApi, xhsApi, Topic, Note, ApiError, XhsBusinessData } from '../../lib/api';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';

// 扩展Note类型以包含商业数据
interface NoteWithBusinessData extends Note {
	businessData?: XhsBusinessData;
	businessDataLoading?: boolean;
}
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';

import { Checkbox } from '../../components/ui/checkbox';
// import { Progress } from '../../components/ui/progress';
import { exportArrayToCSV, formatNumber, sleep } from '../../lib/utils';
import { useToast } from '../../components/ui/use-toast';

type SortType = 'hot' | 'time' | 'trend';

const TopicSearch: React.FC = () => {
	const [keyword, setKeyword] = useState('');
	const [topics, setTopics] = useState<Topic[]>([]);
	const [notes, setNotes] = useState<NoteWithBusinessData[]>([]);
	const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [hasMore, setHasMore] = useState(false);
	const [lastNote, setLastNote] = useState<NoteWithBusinessData | null>(null);
	const [sortType, setSortType] = useState<SortType>('hot');
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);

	const [exportProgress, setExportProgress] = useState<number>(0);
	const [isExporting, setIsExporting] = useState<boolean>(false);
	const [selectedNotes, setSelectedNotes] = useState<string[]>([]);
	const cancelExportRef = useRef<boolean>(false);
	const { toast } = useToast();

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			// 并行加载话题数据和笔记数据
			const [topicsResponse, notesResponse] = await Promise.all([fetch('/test/data/xhs/topic.json'), fetch('/test/data/xhs/notes.json')]);

			const topicsData = await topicsResponse.json();
			const notesData = await notesResponse.json();

			// 直接断言数据类型，避免大量赋值代码
			const convertedTopics: Topic[] = topicsData.data.data.topic_info_dtos as Topic[];
			const convertedNotes: NoteWithBusinessData[] = notesData.data.data.items.map((note: any) => ({
				...note,
				businessDataLoading: false,
			})) as NoteWithBusinessData[];

			setTopics(convertedTopics);
			setNotes(convertedNotes);
			setSelectedTopic(convertedTopics[0]); // 选择第一个话题
			setHasMore(false);

			toast({
				title: '示例数据已加载',
				description: `已加载 ${convertedTopics.length} 个话题和 ${convertedNotes.length} 条笔记数据`,
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	const formatViewCount = (count: number): string => {
		if (count >= 100000000) {
			return `${(count / 100000000).toFixed(1)}亿`;
		}
		if (count >= 10000) {
			return `${(count / 10000).toFixed(1)}万`;
		}
		return count.toString();
	};

	const handleSortChange = async (value: string) => {
		if (isShowingExampleData) {
			toast({
				title: '示例数据模式',
				description: '示例数据不支持修改排序',
			});
			return;
		}

		setSortType(value as SortType);
		if (selectedTopic) {
			setNotes([]);
			setIsLoading(true);
			setLastNote(null);
			try {
				const pageId = extractPageId(selectedTopic.link);
				const response = await topicApi.getNotes(pageId, value);
				const items = response.data?.data?.items || [];

				// 先立即渲染基础笔记数据
				const notesWithBusinessData = convertToNotesWithBusinessData(items);
				setNotes(notesWithBusinessData);
				setHasMore(response.data?.data?.has_more || false);
				if (items.length > 0) {
					setLastNote(notesWithBusinessData[notesWithBusinessData.length - 1]);
					toast({
						variant: 'success',
						title: '排序成功',
						description: `已按${value === 'hot' ? '综合' : value === 'time' ? '最新' : '最热'}排序加载${items.length}条笔记`,
					});

					// 清空之前的选择状态
					setSelectedNotes([]);
				} else {
					toast({
						title: '未找到数据',
						description: '该话题暂无笔记或数据不可见',
					});
				}
			} catch (error) {
				console.error('Failed to fetch notes:', error);
				toast({
					variant: 'destructive',
					title: '排序失败',
					description: (error as ApiError) ? error.message : '无法获取笔记数据',
				});
			} finally {
				setIsLoading(false);
			}
		}
	};

	const handleNoteSelect = (noteId: string, checked: boolean) => {
		if (checked) {
			setSelectedNotes((prev) => [...prev, noteId]);
		} else {
			setSelectedNotes((prev) => prev.filter((id) => id !== noteId));
		}
	};

	const handleSelectAllNotes = (checked: boolean) => {
		if (checked) {
			setSelectedNotes(notes.map((note) => note.id));
		} else {
			setSelectedNotes([]);
		}
	};

	const handleViewBusinessData = () => {
		if (selectedNotes.length === 0) {
			toast({
				variant: 'destructive',
				title: '请选择笔记',
				description: '请先选择要查看隐藏数据的笔记',
			});
			return;
		}

		const selectedNotesData = notes.filter((note) => selectedNotes.includes(note.id));
		toast({
			variant: 'success',
			title: '开始获取商业数据',
			description: `正在为${selectedNotes.length}条笔记获取商业数据...`,
		});

		fetchBusinessDataForNotesAsync(selectedNotesData).catch((error) => {
			console.error('Failed to fetch business data:', error);
			toast({
				variant: 'destructive',
				title: '获取商业数据失败',
				description: '获取商业数据时发生错误，请重试',
			});
		});
	};

	const handleViewSingleNoteBusinessData = async (note: NoteWithBusinessData) => {
		if (note.businessDataLoading) {
			toast({
				title: '正在获取',
				description: '该笔记的商业数据正在获取中，请稍候...',
			});
			return;
		}

		if (note.businessData) {
			toast({
				title: '商业数据已存在',
				description: '该笔记的商业数据已获取：' + JSON.stringify(note.businessData),
			});
			return;
		}

		// 设置该笔记的加载状态
		setNotes((prevNotes) => prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: true } : prevNote)));

		try {
			const response = await xhsApi.getNoteBusinessData(note.id);
			if (response.success && response.data) {
				setNotes((prevNotes) =>
					prevNotes.map((prevNote) =>
						prevNote.id === note.id
							? { ...prevNote, businessData: response.data as unknown as XhsBusinessData, businessDataLoading: false }
							: prevNote
					)
				);
				toast({
					variant: 'success',
					title: '获取成功',
					description: '已获取笔记 ' + note.title + ' 的商业数据',
				});
			} else {
				setNotes((prevNotes) =>
					prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
				);
				toast({
					variant: 'destructive',
					title: '获取失败',
					description: response.message || '无法获取商业数据',
				});
			}
		} catch (error) {
			console.error(`Failed to fetch business data for note ${note.id}:`, error);
			setNotes((prevNotes) =>
				prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
			);
			toast({
				variant: 'destructive',
				title: '获取失败',
				description: error instanceof Error ? error.message : '获取商业数据时发生错误',
			});
		}
	};

	const handleExport = async () => {
		if (notes.length === 0) {
			toast({
				variant: 'destructive',
				title: '无数据可导出',
				description: '请先选择话题并加载笔记数据',
			});
			return;
		}

		setIsExporting(true);
		setExportProgress(0);
		cancelExportRef.current = false;

		try {
			const rows: (string | number)[][] = [
				[
					'ID',
					'话题',
					'标题',
					'作者',
					'描述',
					'点赞',
					'收藏',
					'评论',
					'分享',
					'曝光量',
					'阅读量',
					'粉丝数',
					'品牌',
					'视频报价',
					'图文报价',
					'发布时间',
					'笔记链接',
					'博主链接',
				],
			];

			// 导出当前表格中的所有笔记数据
			for (let i = 0; i < notes.length; i++) {
				if (cancelExportRef.current) {
					break;
				}

				const note = notes[i];
				rows.push([
					note.id,
					selectedTopic?.name || '',
					note.title || note.desc?.split('\n')[0] || '',
					note.user.nickname,
					note.desc || '',
					note.interaction_info.like_count,
					note.interaction_info.collect_count,
					note.interaction_info.comment_count,
					note.interaction_info.share_count || 0,
					note.businessData?.impNum || '-',
					note.businessData?.readNum || '-',
					note.businessData?.fansNum || '-',
					note.businessData?.reportBrandName || '-',
					note.businessData?.videoPrice || '-',
					note.businessData?.picturePrice || '-',
					new Date(note.create_time)
						.toLocaleString('zh-CN', {
							year: 'numeric',
							month: '2-digit',
							day: '2-digit',
							hour: '2-digit',
							minute: '2-digit',
							second: '2-digit',
							hour12: false,
						})
						.replace(/\//g, '-'),
					note.share_info.link,
					`https://www.xiaohongshu.com/user/profile/${note.user.user_id}`,
				]);

				// 更新进度
				const progress = Math.floor(((i + 1) / notes.length) * 100);
				setExportProgress(progress);

				// 添加小延迟以显示进度
				if (i % 10 === 0) {
					await new Promise((resolve) => setTimeout(resolve, 10));
				}
			}

			if (!cancelExportRef.current) {
				const fileName = selectedTopic
					? `${selectedTopic.name}_笔记列表_${new Date().toLocaleString('zh-CN').replace(/[/:]/g, '-')}.csv`
					: `笔记列表_${new Date().toLocaleString('zh-CN').replace(/[/:]/g, '-')}.csv`;

				exportArrayToCSV(rows, fileName);
				toast({
					variant: 'success',
					title: '导出成功',
					description: `已导出${rows.length - 1}条笔记数据`,
				});
			} else {
				toast({
					title: '导出已取消',
					description: '您已取消导出操作',
				});
			}
		} catch (error) {
			console.error('Export failed:', error);
			toast({
				variant: 'destructive',
				title: '导出失败',
				description: error instanceof Error ? error.message : '导出过程中发生错误',
			});
		} finally {
			setIsExporting(false);
		}
	};

	const handleCancelExport = () => {
		cancelExportRef.current = true;
	};

	// 将笔记转换为带商业数据的格式（初始状态）
	const convertToNotesWithBusinessData = (notesList: Note[]): NoteWithBusinessData[] => {
		return notesList.map((note) => ({
			...note,
			businessDataLoading: false,
		}));
	};

	// 逐个获取商业数据并实时更新状态
	const fetchBusinessDataForNotesAsync = async (notesList: NoteWithBusinessData[]) => {
		// 首先设置选中笔记的加载状态
		const noteIds = notesList.map((note) => note.id);
		setNotes((prevNotes) =>
			prevNotes.map((prevNote) => (noteIds.includes(prevNote.id) ? { ...prevNote, businessDataLoading: true } : prevNote))
		);

		const batchSize = 3; // 减少并发数量以提高响应速度

		for (let i = 0; i < notesList.length; i += batchSize) {
			const batch = notesList.slice(i, i + batchSize);
			const promises = batch.map(async (note) => {
				try {
					const response = await xhsApi.getNoteBusinessData(note.id);
					if (response.success && response.data) {
						// 实时更新状态
						setNotes((prevNotes) =>
							prevNotes.map((prevNote) =>
								prevNote.id === note.id
									? {
											...prevNote,
											businessData: response.data as unknown as XhsBusinessData,
											businessDataLoading: false,
									  }
									: prevNote
							)
						);
					} else {
						// 即使失败也要更新加载状态
						setNotes((prevNotes) =>
							prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
						);
					}
				} catch (error) {
					console.error(`Failed to fetch business data for note ${note.id}:`, error);
					// 更新加载状态
					setNotes((prevNotes) =>
						prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
					);
				}
			});
			await Promise.all(promises);
			// 添加小延迟避免请求过于频繁
			if (i + batchSize < notesList.length) {
				await new Promise((resolve) => setTimeout(resolve, 300));
			}
		}
	};

	// 批量获取商业数据（保留原有函数用于导出等场景）
	const fetchBusinessDataForNotes = async (notesList: Note[]): Promise<NoteWithBusinessData[]> => {
		const notesWithBusinessData: NoteWithBusinessData[] = notesList.map((note) => ({
			...note,
			businessDataLoading: true,
		}));

		// 并发获取商业数据，但限制并发数量避免过载
		const batchSize = 5;
		for (let i = 0; i < notesWithBusinessData.length; i += batchSize) {
			const batch = notesWithBusinessData.slice(i, i + batchSize);
			const promises = batch.map(async (note) => {
				try {
					const response = await xhsApi.getNoteBusinessData(note.id);
					if (response.success && response.data) {
						note.businessData = response.data as unknown as XhsBusinessData;
					}
				} catch (error) {
					console.error(`Failed to fetch business data for note ${note.id}:`, error);
				} finally {
					note.businessDataLoading = false;
				}
			});
			await Promise.all(promises);
			// 添加小延迟避免请求过于频繁
			if (i + batchSize < notesWithBusinessData.length) {
				await new Promise((resolve) => setTimeout(resolve, 500));
			}
		}

		return notesWithBusinessData;
	};

	const extractPageId = (link: string): string => {
		const match = link.match(/\/topics\/([^?]+)/);
		return match ? match[1] : '';
	};

	const handleSearch = async () => {
		if (!keyword.trim()) {
			toast({
				variant: 'destructive',
				title: '搜索关键词为空',
				description: '请输入关键词后再进行搜索',
			});
			return;
		}

		setIsLoading(true);
		setIsShowingExampleData(false);

		// 清空示例数据
		setTopics([]);
		setNotes([]);
		setSelectedTopic(null);
		setHasMore(false);
		setLastNote(null);
		setSelectedNotes([]);

		try {
			const response = await topicApi.search(keyword);
			const topicResults = response.data?.data.topic_info_dtos || [];
			setTopics(topicResults);

			if (topicResults.length > 0) {
				toast({
					variant: 'success',
					title: '搜索成功',
					description: `找到${topicResults.length}个相关话题`,
				});
			} else {
				toast({
					title: '未找到相关话题',
					description: '请尝试其他关键词',
				});
			}
		} catch (error) {
			console.error('Failed to search topics:', error);
			toast({
				variant: 'destructive',
				title: '搜索失败',
				description: (error as ApiError) ? error.message : '无法获取话题数据',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleTopicSelect = async (topic: Topic) => {
		if (isShowingExampleData) {
			toast({
				title: '示例数据模式',
				description: '示例数据不支持切换话题',
			});
			return;
		}

		setSelectedTopic(topic);
		setIsLoading(true);
		setLastNote(null);
		try {
			const pageId = extractPageId(topic.link);
			const response = await topicApi.getNotes(pageId, sortType);
			const items = response.data?.data?.items || [];

			// Track unique notes by ID
			const uniqueItems = items.filter((item, index, self) => self.findIndex((t) => t.id === item.id) === index);

			// 先立即渲染基础笔记数据
			const notesWithBusinessData = convertToNotesWithBusinessData(uniqueItems);
			setNotes(notesWithBusinessData);
			setHasMore(response.data?.data?.has_more || false);
			if (items.length > 0) {
				setLastNote(notesWithBusinessData[notesWithBusinessData.length - 1]);
				toast({
					variant: 'success',
					title: '加载成功',
					description: `已加载话题"${topic.name}"的${uniqueItems.length}条笔记`,
				});

				// 清空之前的选择状态
				setSelectedNotes([]);
			} else {
				toast({
					title: '未找到笔记',
					description: `话题"${topic.name}"暂无笔记或数据不可见`,
				});
			}
		} catch (error) {
			console.error('Failed to fetch notes:', error);
			toast({
				variant: 'destructive',
				title: '加载失败',
				description: (error as ApiError) ? error.message : '无法获取笔记数据',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleLoadMore = async () => {
		if (!selectedTopic || !hasMore || isLoadingMore) return;

		setIsLoadingMore(true);
		try {
			const pageId = extractPageId(selectedTopic.link);
			const response = await topicApi.getNotes(pageId, sortType, lastNote?.cursor_score, lastNote?.id, lastNote?.create_time);
			const items = response.data?.data?.items || [];

			// Check if all items in this page already exist in our notes collection
			const existingNoteIds = notes.map((note) => note.id);
			const newItems = items.filter((item) => !existingNoteIds.includes(item.id));

			// If no new notes were found, we've reached the end
			if (newItems.length === 0) {
				setHasMore(false);
				setIsLoadingMore(false);
				toast({
					title: '没有更多数据',
					description: '已加载全部笔记',
				});
				return;
			}

			// 先立即渲染新笔记的基础数据
			const newNotesWithBusinessData = convertToNotesWithBusinessData(newItems);
			setNotes((prev) => [...prev, ...newNotesWithBusinessData]);
			setHasMore(response.data?.data?.has_more || false);
			if (items.length > 0) {
				setLastNote(newNotesWithBusinessData[newNotesWithBusinessData.length - 1]);
				toast({
					variant: 'success',
					title: '加载成功',
					description: `已加载额外${newItems.length}条笔记`,
				});

				// 加载更多时不自动选择新笔记
			}
		} catch (error) {
			console.error('Failed to load more notes:', error);
			toast({
				variant: 'destructive',
				title: '加载失败',
				description: (error as ApiError) ? error.message : '获取更多笔记失败',
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	return (
		<TooltipProvider>
			<div className="p-4 space-y-4">
				<Card className="p-6">
					<div className="flex gap-2 mb-4">
						<Input
							placeholder="输入关键词搜索"
							value={keyword}
							onChange={(e) => setKeyword(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
						/>
						<Button onClick={handleSearch} disabled={isLoading}>
							<Search className="w-4 h-4 mr-2" />
							搜索
						</Button>
					</div>

					{topics.length > 0 && (
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<h2 className="text-lg font-semibold">相关话题</h2>
								{isShowingExampleData && <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">示例数据</span>}
							</div>
							<div className="flex flex-wrap gap-2">
								{topics.map((topic) => (
									<Tooltip key={topic.id}>
										<TooltipTrigger asChild>
											<Button
												variant={selectedTopic?.id === topic.id ? 'default' : 'outline'}
												className="flex flex-col items-start h-auto py-2 px-3 min-w-[120px]"
												onClick={() => handleTopicSelect(topic)}
												disabled={isShowingExampleData && selectedTopic?.id !== topic.id}
											>
												<span className="font-medium text-left line-clamp-1">{topic.name}</span>
												<span className="text-xs text-muted-foreground mt-1">{formatViewCount(topic.view_num)}次浏览</span>
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>
												{isShowingExampleData && selectedTopic?.id !== topic.id
													? '示例数据不支持切换话题'
													: `查看话题"${topic.name}"的笔记`}
											</p>
										</TooltipContent>
									</Tooltip>
								))}
							</div>
						</div>
					)}

					{notes.length > 0 && (
						<div className="mt-6 space-y-2">
							<div className="flex justify-between items-center">
								<div className="flex items-center gap-2">
									<h2 className="text-lg font-semibold">{selectedTopic?.name} 的笔记</h2>
									{isShowingExampleData && <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">示例数据</span>}
								</div>
								<div className="flex items-center gap-4">
									<Tooltip>
										<TooltipTrigger asChild>
											<div>
												<Select value={sortType} onValueChange={handleSortChange} disabled={isShowingExampleData}>
													<SelectTrigger className="w-[120px]">
														<SelectValue placeholder="排序方式" />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="hot">综合</SelectItem>
														<SelectItem value="time">最新</SelectItem>
														<SelectItem value="trend">最热</SelectItem>
													</SelectContent>
												</Select>
											</div>
										</TooltipTrigger>
										<TooltipContent>
											<p>{isShowingExampleData ? '示例数据不支持修改排序' : '选择排序方式'}</p>
										</TooltipContent>
									</Tooltip>
									<Tooltip>
										<TooltipTrigger asChild>
											<div>
												<Button
													variant="outline"
													onClick={isShowingExampleData ? undefined : handleViewBusinessData}
													disabled={isShowingExampleData || selectedNotes.length === 0}
												>
													查看选中笔记隐藏数据 ({selectedNotes.length})
												</Button>
											</div>
										</TooltipTrigger>
										<TooltipContent>
											<p>{isShowingExampleData ? '示例数据不支持查看隐藏数据' : '查看选中笔记的隐藏数据'}</p>
										</TooltipContent>
									</Tooltip>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="outline"
												onClick={isShowingExampleData ? undefined : handleExport}
												disabled={isShowingExampleData || notes.length === 0 || isExporting}
											>
												<Download className="w-4 h-4 mr-2" />
												{isExporting ? `导出中 ${exportProgress}%` : '导出'}
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>{isShowingExampleData ? '示例数据不支持导出' : '导出笔记数据'}</p>
										</TooltipContent>
									</Tooltip>
								</div>
							</div>
							<div className="border rounded-md">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead className="w-[50px]">
												<Checkbox
													checked={selectedNotes.length === notes.length && notes.length > 0}
													onCheckedChange={handleSelectAllNotes}
												/>
											</TableHead>
											<TableHead className="w-[100px]">封面</TableHead>
											<TableHead className="w-[120px]">标题</TableHead>
											<TableHead>作者</TableHead>
											<TableHead className="w-[180px]">描述</TableHead>
											<TableHead className="w-[80px]">点赞</TableHead>
											<TableHead className="w-[80px]">收藏</TableHead>
											<TableHead className="w-[80px]">评论</TableHead>
											<TableHead className="w-[80px]">分享</TableHead>
											<TableHead className="w-[80px]">曝光</TableHead>
											<TableHead className="w-[80px]">阅读</TableHead>
											<TableHead className="w-[80px]">粉丝</TableHead>
											<TableHead className="w-[100px]">品牌</TableHead>
											<TableHead className="w-[120px]">报价</TableHead>
											<TableHead className="w-[160px]">发布时间</TableHead>
											{/* <TableHead className="w-[80px]">操作</TableHead> */}
										</TableRow>
									</TableHeader>
									<TableBody>
										{notes.map((note) => (
											<TableRow key={note.id}>
												<TableCell>
													<Checkbox
														checked={selectedNotes.includes(note.id)}
														onCheckedChange={(checked) => handleNoteSelect(note.id, checked as boolean)}
													/>
												</TableCell>
												<TableCell>
													{isShowingExampleData ? (
														<ExampleImageSVG width={80} height={80} type="video" />
													) : note.images_list?.[0] ? (
														<img
															src={note.images_list[0].url}
															alt={note.title || note.desc}
															className="w-20 h-20 object-cover rounded"
															loading="lazy"
															referrerPolicy="no-referrer"
														/>
													) : null}
												</TableCell>
												<TableCell className="max-w-[150px] truncate">
													{isShowingExampleData ? (
														<span className="text-gray-700">{note.title || note.desc?.split('\n')[0]}</span>
													) : (
														<a
															href={note.share_info.link}
															target="_blank"
															rel="noopener noreferrer"
															className="text-blue-600 hover:underline"
														>
															{note.title || note.desc?.split('\n')[0]}
														</a>
													)}
												</TableCell>
												<TableCell>
													{isShowingExampleData ? (
														<span className="text-gray-700">{note.user.nickname}</span>
													) : (
														<a
															href={`https://www.xiaohongshu.com/user/profile/${note.user.user_id}`}
															target="_blank"
															rel="noopener noreferrer"
															className="text-blue-600 hover:underline"
														>
															{note.user.nickname}
														</a>
													)}
												</TableCell>
												<TableCell className="max-w-[200px] truncate">{note.desc}</TableCell>
												<TableCell>{formatNumber(note.interaction_info.like_count)}</TableCell>
												<TableCell>{formatNumber(note.interaction_info.collect_count)}</TableCell>
												<TableCell>{formatNumber(note.interaction_info.comment_count)}</TableCell>
												<TableCell>{formatNumber(note.interaction_info.share_count || 0)}</TableCell>
												<TableCell>
													{note.businessDataLoading ? (
														<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
													) : note.businessData ? (
														formatNumber(note.businessData.impNum)
													) : (
														'-'
													)}
												</TableCell>
												<TableCell>
													{note.businessDataLoading ? (
														<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
													) : note.businessData ? (
														formatNumber(note.businessData.readNum)
													) : (
														'-'
													)}
												</TableCell>
												<TableCell>
													{note.businessDataLoading ? (
														<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
													) : note.businessData ? (
														formatNumber(note.businessData.fansNum)
													) : (
														'-'
													)}
												</TableCell>
												<TableCell className="max-w-[100px] truncate">
													{note.businessDataLoading ? (
														<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
													) : note.businessData?.reportBrandName ? (
														note.businessData.reportBrandName
													) : (
														'-'
													)}
												</TableCell>
												<TableCell className="whitespace-pre-line">
													{note.businessDataLoading ? (
														<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
													) : note.businessData ? (
														<div className="text-sm">
															<div>图文：{note.businessData.picturePrice ? `¥${note.businessData.picturePrice}` : '-'}</div>
															<div>视频：{note.businessData.videoPrice ? `¥${note.businessData.videoPrice}` : '-'}</div>
														</div>
													) : (
														<div className="text-sm">
															<div>图文：-</div>
															<div>视频：-</div>
														</div>
													)}
												</TableCell>
												<TableCell className="whitespace-pre-line w-[160px]">
													{new Date(note.create_time)
														.toLocaleString('zh-CN', {
															year: 'numeric',
															month: '2-digit',
															day: '2-digit',
															hour: '2-digit',
															minute: '2-digit',
															second: '2-digit',
															hour12: false,
														})
														.replace(/\//g, '-')
														.replace(',', '\n')}
												</TableCell>
												{/* <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewSingleNoteBusinessData(note)}
                          disabled={note.businessDataLoading}
                        >
                          {note.businessDataLoading ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                          ) : (
                            '查看隐藏数据'
                          )}
                        </Button>
                      </TableCell> */}
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{hasMore && (
								<div className="flex justify-center mt-4">
									<Tooltip>
										<TooltipTrigger asChild>
											<div>
                        <Button
												onClick={isShowingExampleData ? undefined : handleLoadMore}
												disabled={isShowingExampleData || isLoading || isLoadingMore}
												variant="outline"
											>
												{isLoadingMore ? (
													<div className="flex items-center">
														<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
														加载中...
													</div>
												) : (
													'加载更多'
												)}
											</Button>
                      </div>
										</TooltipTrigger>
										<TooltipContent>
											<p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多笔记数据'}</p>
										</TooltipContent>
									</Tooltip>
								</div>
							)}
						</div>
					)}

					{isLoading && (
						<div className="flex justify-center py-4">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
						</div>
					)}
				</Card>
			</div>
		</TooltipProvider>
	);
};

export default TopicSearch;
