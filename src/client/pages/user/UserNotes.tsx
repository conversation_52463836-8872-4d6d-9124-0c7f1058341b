import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Download } from 'lucide-react';
import { ApiError, userApi, UserNote } from '../../lib/api';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { exportArrayToCSV, formatNumber } from '../../lib/utils';
import { useToast } from '../../components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';
import { Checkbox } from '../../components/ui/checkbox';
import { xhsApi, XhsBusinessData } from '../../lib/api';

// 扩展UserNote类型以包含商业数据
interface NoteWithBusinessData extends UserNote {
	businessData?: XhsBusinessData;
	businessDataLoading?: boolean;
}

const UserNotes: React.FC = () => {
	const [userUrl, setUserUrl] = useState('');
	const [userId, setUserId] = useState('');
	const [notes, setNotes] = useState<NoteWithBusinessData[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [hasMore, setHasMore] = useState(false);
	const [lastNote, setLastNote] = useState<UserNote | null>(null);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);
	const { toast } = useToast();
	const [selectedNotes, setSelectedNotes] = useState<string[]>([]);

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			const response = await fetch('/test/data/xhs/userNotes.json');
			const data = await response.json();

			// 直接断言数据类型，避免大量赋值代码
			const convertedNotes: NoteWithBusinessData[] = data.data.data.notes.map((note: any) => ({
				...note,
				businessDataLoading: false
			})) as NoteWithBusinessData[];

			setNotes(convertedNotes);
			setHasMore(false);

			toast({
				title: '示例数据已加载',
				description: `已加载 ${convertedNotes.length} 条示例笔记数据`,
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	const extractUserId = (url: string): string => {
		// Extract user ID from URL like https://www.xiaohongshu.com/user/profile/5dc1746f000000000100ae57
		const match = url.match(/\/profile\/([^/?]+)/);
		return match ? match[1] : '';
	};

	const handleNoteSelect = (noteId: string, checked: boolean) => {
		if (checked) {
			setSelectedNotes((prev) => [...prev, noteId]);
		} else {
			setSelectedNotes((prev) => prev.filter((id) => id !== noteId));
		}
	};

	const handleSelectAllNotes = (checked: boolean) => {
		if (checked) {
			setSelectedNotes(notes.map((note) => note.id));
		} else {
			setSelectedNotes([]);
		}
	};

	const handleViewBusinessData = async () => {
		if (selectedNotes.length === 0) {
			toast({
				variant: 'destructive',
				title: '请选择笔记',
				description: '请先选择要查看隐藏数据的笔记',
			});
			return;
		}

		const selectedNotesData = notes.filter((note) => selectedNotes.includes(note.id));
		toast({
			variant: 'success',
			title: '开始获取商业数据',
			description: `正在为${selectedNotes.length}条笔记获取商业数据...`,
		});

		fetchBusinessDataForNotesAsync(selectedNotesData).catch((error) => {
			console.error('Failed to fetch business data:', error);
			toast({
				variant: 'destructive',
				title: '获取商业数据失败',
				description: '获取商业数据时发生错误，请重试',
			});
		});
	};

	const fetchBusinessDataForNotesAsync = async (notesList: NoteWithBusinessData[]) => {
		// 首先设置选中笔记的加载状态
		const noteIds = notesList.map((note) => note.id);
		setNotes((prevNotes) =>
			prevNotes.map((prevNote) => (noteIds.includes(prevNote.id) ? { ...prevNote, businessDataLoading: true } : prevNote))
		);

		const batchSize = 3; // 减少并发数量以提高响应速度

		for (let i = 0; i < notesList.length; i += batchSize) {
			const batch = notesList.slice(i, i + batchSize);
			const promises = batch.map(async (note) => {
				try {
					const response = await xhsApi.getNoteBusinessData(note.id);
					if (response.success && response.data) {
						// 实时更新状态
						setNotes((prevNotes) =>
							prevNotes.map((prevNote) =>
								prevNote.id === note.id
									? {
											...prevNote,
											businessData: response.data as unknown as XhsBusinessData,
											businessDataLoading: false,
									  }
									: prevNote
							)
						);
					} else {
						// 即使失败也要更新加载状态
						setNotes((prevNotes) =>
							prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
						);
					}
				} catch (error) {
					console.error(`Failed to fetch business data for note ${note.id}:`, error);
					// 更新加载状态
					setNotes((prevNotes) =>
						prevNotes.map((prevNote) => (prevNote.id === note.id ? { ...prevNote, businessDataLoading: false } : prevNote))
					);
				}
			});
			await Promise.all(promises);
			// 添加小延迟避免请求过于频繁
			if (i + batchSize < notesList.length) {
				await new Promise((resolve) => setTimeout(resolve, 300));
			}
		}
	};

	const handleSearch = async () => {
		if (!userUrl.trim()) return;

		const extractedUserId = extractUserId(userUrl);
		if (!extractedUserId) {
			toast({
				variant: 'destructive',
				title: '无效的用户链接',
				description: '请输入正确的小红书用户主页链接',
			});
			return;
		}

		setUserId(extractedUserId);
		setIsLoading(true);
		setNotes([]);
		setLastNote(null);
		setIsShowingExampleData(false);

		try {
			const response = await userApi.getNotes(extractedUserId);
			const items = response.data?.data?.notes || [];
			setNotes(items);
			setHasMore(response.data?.data?.has_more || false);
			setLastNote(items[items.length - 1]);

			if (items.length > 0) {
				toast({
					variant: 'success',
					title: '数据获取成功',
					description: `已加载 ${items.length} 条笔记`,
				});
			} else {
				toast({
					title: '未找到数据',
					description: '该用户暂无笔记或数据不可见',
				});
			}
		} catch (error) {
			console.error('Failed to fetch user notes:', error);
			toast({
				variant: 'destructive',
				title: '获取数据失败',
				description: (error as ApiError) ? error.message : '获取用户笔记失败，请稍后重试',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleLoadMore = async () => {
		if (isLoadingMore || !hasMore || !userId) return;

		setIsLoadingMore(true);
		try {
			const response = await userApi.getNotes(userId, lastNote?.cursor);
			const items = response.data?.data?.notes || [];
			// Filter out duplicates
			const uniqueItems = items.filter((item) => !notes.some((note) => note.id === item.id));
			setNotes([...notes, ...uniqueItems.map((note) => ({ ...note, businessDataLoading: false }))]);
			setHasMore(response.data?.data?.has_more || false);
			setLastNote(items[items.length - 1]);

			if (uniqueItems.length > 0) {
				toast({
					variant: 'success',
					title: '加载成功',
					description: `已加载额外 ${uniqueItems.length} 条笔记`,
				});
			}
		} catch (error) {
			console.error('Failed to load more notes:', error);
			toast({
				variant: 'destructive',
				title: '加载更多数据失败',
				description: (error as ApiError) ? error.message : '获取更多笔记失败，请稍后重试',
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	const handleExport = () => {
		if (notes.length === 0) return;

		const headers = [
			'ID',
			'标题',
			'作者',
			'描述',
			'点赞',
			'收藏',
			'评论',
			'分享',
			'喜欢',
			'曝光量',
			'阅读量',
			'粉丝量',
			'笔记涨粉',
			// '品牌',
			'视频报价',
			'图文报价',
			'发布时间',
			'笔记链接',
			'博主链接',
		];

		const rows = [
			headers,
			...notes.map((note) => [
				note.id,
				note.title,
				note.user.nickname,
				note.desc,
				note.likes,
				note.collected_count,
				note.comments_count,
				note.share_count,
				note.nice_count,
				note.businessData?.impNum || '-',
				note.businessData?.readNum || '-',
				note.businessData?.fansNum || '-',
				note.businessData?.followCnt || '-',
				// note.businessData?.reportBrandName || '-',
				note.businessData?.videoPrice || '-',
				note.businessData?.picturePrice || '-',
				new Date(note.create_time * 1000).toLocaleString(),
				note.share_url,
				`https://www.xiaohongshu.com/user/profile/${note.user.userid}`,
			]),
		];

		exportArrayToCSV(rows, `用户作品_${notes[0]?.user.nickname || userId}_${new Date().toLocaleString()}.csv`);

		toast({
			variant: 'success',
			title: '导出成功',
			description: `已导出 ${notes.length} 条笔记数据`,
		});
	};

	return (
		<TooltipProvider>
			<div className="p-4 space-y-4">
			<Card className="p-6">
				<div className="space-y-4 mb-4">
					<div className="flex gap-2">
						<Input
							placeholder="请输入小红书用户主页链接，例如：https://www.xiaohongshu.com/user/profile/5dc1746f000000000100ae57"
							value={userUrl}
							onChange={(e) => setUserUrl(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
						/>
						<Button onClick={handleSearch} disabled={isLoading}>
							<Search className="w-4 h-4 mr-2" />
							搜索
						</Button>
					</div>
				</div>

				{notes.length > 0 && (
					<div className="mt-6 space-y-2">
						<div className="flex justify-between items-center gap-4">
							<div className="flex items-center gap-2">
								<h2 className="text-lg font-semibold">
									用户: {notes[0]?.user.nickname || userId} - 已加载 {notes.length} 条笔记
								</h2>
								{isShowingExampleData && (
									<span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
										示例数据
									</span>
								)}
							</div>
							<div className="flex items-center gap-2">
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											onClick={isShowingExampleData ? undefined : handleViewBusinessData}
											disabled={isShowingExampleData || selectedNotes.length === 0}
											variant="outline"
										>
											查看选中笔记隐藏数据 ({selectedNotes.length})
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>{isShowingExampleData ? '示例数据不支持查看隐藏数据' : '查看选中笔记的隐藏数据'}</p>
									</TooltipContent>
								</Tooltip>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="outline"
											onClick={isShowingExampleData ? undefined : handleExport}
											disabled={isShowingExampleData}
										>
											<Download className="w-4 h-4 mr-2" />
											导出
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>{isShowingExampleData ? '示例数据不支持导出' : '导出笔记数据'}</p>
									</TooltipContent>
								</Tooltip>
							</div>
						</div>
						<div className="border rounded-md">
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead className="w-[40px]">
											<Checkbox
												checked={selectedNotes.length === notes.length && notes.length > 0}
												onCheckedChange={handleSelectAllNotes}
											/>
										</TableHead>
										<TableHead className="w-[100px]">封面</TableHead>
										<TableHead className="w-[120px]">标题</TableHead>
										<TableHead>作者</TableHead>
										<TableHead className="w-[180px]">描述</TableHead>
										<TableHead className="w-[80px]">点赞</TableHead>
										<TableHead className="w-[80px]">收藏</TableHead>
										<TableHead className="w-[80px]">评论</TableHead>
										<TableHead className="w-[80px]">分享</TableHead>
										<TableHead className="w-[60px]">喜欢</TableHead>
										<TableHead className="w-[80px]">曝光</TableHead>
										<TableHead className="w-[80px]">阅读</TableHead>
										<TableHead className="w-[80px]">粉丝</TableHead>
										<TableHead className="w-[80px]">笔记涨粉</TableHead>
										{/* <TableHead className="w-[100px]">品牌</TableHead> */}
										<TableHead className="w-[120px]">报价</TableHead>
										<TableHead className="w-[100px]">发布时间</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{notes.map((note) => (
										<TableRow key={note.id}>
											<TableCell>
												<Checkbox
													checked={selectedNotes.includes(note.id)}
													onCheckedChange={(checked) => handleNoteSelect(note.id, checked as boolean)}
													aria-label={`Select note ${note.id}`}
													disabled={note.businessDataLoading}
												/>
											</TableCell>
											<TableCell>
												{isShowingExampleData ? (
													<ExampleImageSVG width={80} height={80} type="video" />
												) : note.images_list?.[0] ? (
													<img
														src={note.images_list[0].url.replace('/heif/', '/webp/')}
														alt={note.title || note.desc}
														className="w-20 h-20 object-cover rounded"
														loading="lazy"
														referrerPolicy="no-referrer"
													/>
												) : null}
											</TableCell>
											<TableCell className="max-w-[200px] truncate">
												{isShowingExampleData ? (
													<span className="text-gray-700">
														{note.title || note.desc?.split('\n')[0]}
													</span>
												) : (
													<a href={note.share_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
														{note.title || note.desc?.split('\n')[0]}
													</a>
												)}
											</TableCell>
											<TableCell>
												{isShowingExampleData ? (
													<span className="text-gray-700">
														{note.user.nickname}
													</span>
												) : (
													<a
														href={`https://www.xiaohongshu.com/user/profile/${note.user.userid}`}
														target="_blank"
														rel="noopener noreferrer"
														className="text-blue-600 hover:underline"
													>
														{note.user.nickname}
													</a>
												)}
											</TableCell>
											<TableCell className="max-w-[300px] truncate">{note.desc}</TableCell>
											<TableCell>{formatNumber(note.likes)}</TableCell>
											<TableCell>{formatNumber(note.collected_count)}</TableCell>
											<TableCell>{formatNumber(note.comments_count)}</TableCell>
											<TableCell>{formatNumber(note.share_count || 0)}</TableCell>
											<TableCell>{formatNumber(note.nice_count || 0)}</TableCell>
											<TableCell>
												{note.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
												) : note.businessData?.impNum ? (
													formatNumber(note.businessData.impNum)
												) : (
													'-'
												)}
											</TableCell>
											<TableCell>
												{note.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
												) : note.businessData?.readNum ? (
													formatNumber(note.businessData.readNum)
												) : (
													'-'
												)}
											</TableCell>
											<TableCell>
												{note.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
												) : note.businessData?.fansNum ? (
													formatNumber(note.businessData.fansNum)
												) : (
													'-'
												)}
											</TableCell>
											<TableCell>
												{note.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
												) : note.businessData?.followCnt ? (
													formatNumber(note.businessData.followCnt)
												) : (
													'-'
												)}
											</TableCell>
											{/* <TableCell className="max-w-[100px] truncate">
												{note.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
												) : note.businessData?.reportBrandName ? (
													note.businessData.reportBrandName
												) : (
													'-'
												)}
											</TableCell> */}
											<TableCell className="whitespace-pre-line">
												{note.businessDataLoading ? (
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
												) : note.businessData ? (
													<div className="text-sm">
														<div>图文：{note.businessData.picturePrice ? `¥${note.businessData.picturePrice}` : '-'}</div>
														<div>视频：{note.businessData.videoPrice ? `¥${note.businessData.videoPrice}` : '-'}</div>
													</div>
												) : (
													<div className="text-sm">
														<div>图文：-</div>
														<div>视频：-</div>
													</div>
												)}
											</TableCell>
											<TableCell className="whitespace-pre-line w-[160px]">
												{new Date(note.create_time * 1000)
													.toLocaleString('zh-CN', {
														year: 'numeric',
														month: '2-digit',
														day: '2-digit',
														hour: '2-digit',
														minute: '2-digit',
														second: '2-digit',
														hour12: false,
													})
													.replace(/\/\//g, '-')
													.replace(',', '\n')}
											</TableCell>
										</TableRow>
									))}
								</TableBody>
							</Table>
						</div>

						{hasMore && (
							<div className="flex justify-center mt-4">
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											onClick={isShowingExampleData ? undefined : handleLoadMore}
											disabled={isShowingExampleData || isLoadingMore}
											variant="outline"
										>
											{isLoadingMore ? (
												<div className="flex items-center">
													<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
													加载中...
												</div>
											) : (
												'加载更多'
											)}
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多笔记数据'}</p>
									</TooltipContent>
								</Tooltip>
							</div>
						)}
					</div>
				)}

				{isLoading && (
					<div className="flex justify-center py-4">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
					</div>
				)}
			</Card>
		</div>
	</TooltipProvider>
);
};

export default UserNotes;
