import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Label } from '../../components/ui/label';
import { Checkbox } from '../../components/ui/checkbox';
import { Search, Download, MessageSquare, AlertTriangle, Calendar } from 'lucide-react';
import { ApiError, apiRequest } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from '../../components/ui/dialog';
import { Progress } from '../../components/ui/progress';
import { exportArrayToCSV, formatNumber } from '../../lib/utils';
import '../../styles/date-picker.css';

interface DouyinComment {
    cid: string;
    text: string;
    aweme_id: string;
    create_time: number;
    digg_count: number;
    status: number;
    user: {
        uid: string;
        short_id: string;
        nickname: string;
        signature: string;
        avatar_thumb: {
            url_list: string[];
        };
        unique_id: string;
        sec_uid: string;
    };
    reply_id: string;
    user_digged: number;
    reply_comment_total: number;
    is_author_digged: boolean;
    is_hot: boolean;
    ip_label: string;
    level: number;
    image_list?: {
        origin_url: {
            uri: string;
            url_list: string[];
            width: number;
            height: number;
        };
        crop_url: {
            uri: string;
            url_list: string[];
            width: number;
            height: number;
        };
        thumb_url: {
            uri: string;
            url_list: string[];
            width: number;
            height: number;
        };
        medium_url: {
            uri: string;
            url_list: string[];
            width: number;
            height: number;
        };
        download_url: {
            uri: string;
            url_list: string[];
            width: number;
            height: number;
        };
    }[];
}

interface DouyinCommentData {
    status_code: number;
    comments: DouyinComment[];
    cursor?: string;
    total?: number;
    has_more?: boolean;
}

interface DouyinCommentResponse {
    code: number;
    message: string;
    success: boolean;
    data: DouyinCommentData;
}

const DouyinCommentExtractor: React.FC = () => {
    const [videoUrl, setVideoUrl] = useState('');
    const [comments, setComments] = useState<DouyinComment[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(false);
    const [cursor, setCursor] = useState<string | undefined>(undefined);
    const [total, setTotal] = useState<number>(0);
    const [isShowingExampleData, setIsShowingExampleData] = useState(true);

    // 导出弹窗状态
    const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
    const [exportStartDate, setExportStartDate] = useState('');
    const [exportEndDate, setExportEndDate] = useState('');
    const [includeReplies, setIncludeReplies] = useState(false);
    const [exportCount, setExportCount] = useState('');
    const [exportKeywords, setExportKeywords] = useState('');
    const [isExporting, setIsExporting] = useState(false);
    const [exportProgress, setExportProgress] = useState(0);

    const { toast } = useToast();

    // 加载示例数据
    const loadExampleData = async () => {
        try {
            const response = await fetch('/test/data/douyin/commentList.json');
            const data = await response.json();

            // 直接断言数据类型，避免大量赋值代码
            const convertedComments: DouyinComment[] = data.data.comments as DouyinComment[];

            setComments(convertedComments);
            setHasMore(data.data.has_more || false);
            setCursor(data.data.cursor?.toString());
            setTotal(data.data.total || 0);

            toast({
                title: '示例数据已加载',
                description: `已加载 ${convertedComments.length} 条示例评论数据`,
            });
        } catch (error) {
            console.error('Failed to load example data:', error);
            toast({
                variant: 'destructive',
                title: '加载示例数据失败',
                description: '无法加载示例数据，请检查文件是否存在',
            });
        }
    };

    // 组件挂载时加载示例数据
    useEffect(() => {
        loadExampleData();
    }, []);

    const handleExtract = async () => {
        if (!videoUrl.trim()) {
            toast({
                title: '提示',
                description: '请输入抖音视频链接',
                variant: 'destructive',
            });
            return;
        }

        setIsLoading(true);
        setComments([]);
        setCursor(undefined);
        setHasMore(false);
        setTotal(0);
        setIsShowingExampleData(false);

        try {
            const response = await apiRequest<DouyinCommentResponse>(`/dy/video/comments`, {
                method: 'POST',
                body: JSON.stringify({
                    url: videoUrl
                }),
            });

            if (response.success && response.data) {
                const commentData = response.data as unknown as DouyinCommentData;
                if (commentData.comments) {
                    // 按 cid 去重
                    const uniqueComments = commentData.comments.filter((comment, index, self) =>
                        index === self.findIndex(c => c.cid === comment.cid)
                    );
                    setComments(uniqueComments);
                    setCursor(commentData.cursor);
                    setHasMore(commentData.has_more || false);
                    setTotal(commentData.total || 0);

                    toast({
                        title: '提取成功',
                        description: `成功提取 ${uniqueComments.length} 条评论${uniqueComments.length !== commentData.comments.length ? `（去重后，原始 ${commentData.comments.length} 条）` : ''}`,
                    });
                } else {
                    toast({
                        title: '提取失败',
                        description: '未能获取评论数据',
                        variant: 'destructive',
                    });
                }
            } else {
                toast({
                    title: '提取失败',
                    description: response.message || '未能获取评论数据',
                    variant: 'destructive',
                });
            }
        } catch (error) {
            console.error('Failed to extract comments:', error);
            toast({
                title: '提取失败',
                description: error instanceof ApiError ? error.message : '获取评论数据时发生错误',
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleLoadMore = async () => {
        if (!cursor || !videoUrl.trim()) {
            return;
        }

        setIsLoadingMore(true);

        try {
            const response = await apiRequest<DouyinCommentResponse>(`/dy/video/comments`, {
                method: 'POST',
                body: JSON.stringify({
                    url: videoUrl,
                    cursor: cursor
                }),
            });

            if (response.success && response.data) {
                const commentData = response.data as unknown as DouyinCommentData;
                if (commentData.comments) {
                    const newComments = commentData.comments;

                    setComments(prevComments => {
                        // 合并所有评论
                        const allComments = [...prevComments, ...newComments];
                        // 按 cid 去重
                        const uniqueComments = allComments.filter((comment, index, self) =>
                            index === self.findIndex(c => c.cid === comment.cid)
                        );
                        return uniqueComments;
                    });

                    setCursor(commentData.cursor);
                    setHasMore(commentData.has_more || false);

                    // 计算实际新增的评论数量
                    const actualNewCount = newComments.filter(newComment =>
                        !comments.some(existingComment => existingComment.cid === newComment.cid)
                    ).length;

                    toast({
                        title: '加载成功',
                        description: `新增 ${actualNewCount} 条评论${actualNewCount !== newComments.length ? `（去重后，原始 ${newComments.length} 条）` : ''}`,
                    });
                } else {
                    toast({
                        title: '加载失败',
                        description: '未能获取更多评论数据',
                        variant: 'destructive',
                    });
                }
            } else {
                toast({
                    title: '加载失败',
                    description: response.message || '未能获取更多评论数据',
                    variant: 'destructive',
                });
            }
        } catch (error) {
            console.error('Failed to load more comments:', error);
            toast({
                title: '加载失败',
                description: error instanceof ApiError ? error.message : '获取更多评论数据时发生错误',
                variant: 'destructive',
            });
        } finally {
            setIsLoadingMore(false);
        }
    };

    const handleClear = () => {
        setVideoUrl('');
        setComments([]);
        setCursor(undefined);
        setHasMore(false);
        setTotal(0);
    };

    const handleExportClick = () => {
        if (comments.length === 0) {
            toast({
                title: '无数据可导出',
                description: '请先提取评论数据',
                variant: 'destructive',
            });
            return;
        }
        setIsExportDialogOpen(true);
    };

    // 自动加载更多评论数据
    const loadMoreCommentsForExport = async (targetCount: number): Promise<DouyinComment[]> => {
        let allComments = [...comments];
        let currentCursor = cursor;
        let currentHasMore = hasMore;

        // 先去重现有评论
        allComments = allComments.filter((comment, index, self) =>
            index === self.findIndex(c => c.cid === comment.cid)
        );

        while (allComments.length < targetCount && currentHasMore && currentCursor) {
            try {
                const response = await apiRequest<DouyinCommentResponse>(`/dy/video/comments`, {
                    method: 'POST',
                    body: JSON.stringify({
                        url: videoUrl,
                        cursor: currentCursor
                    }),
                });

                if (response.success && response.data) {
                    const commentData = response.data as unknown as DouyinCommentData;
                    if (commentData.comments) {
                        // 合并新评论
                        const combinedComments = [...allComments, ...commentData.comments];
                        // 去重处理
                        allComments = combinedComments.filter((comment, index, self) =>
                            index === self.findIndex(c => c.cid === comment.cid)
                        );

                        currentCursor = commentData.cursor;
                        currentHasMore = commentData.has_more || false;

                        // 去重后检查是否已达到目标数量
                        if (allComments.length >= targetCount) {
                            break;
                        }
                    } else {
                        break;
                    }
                } else {
                     toast({
                        title: '评论内容加载失败',
                        description: response.message || '获取评论失败',
                        variant: 'destructive',
                    });
                    break;
                }
            } catch (error) {
                console.error('Failed to load more comments:', error);
                 toast({
                        title: '评论内容加载失败',
                        description: error.message || '获取评论失败',
                        variant: 'destructive',
                    });
                break;
            }
        }

        return allComments;
    };

    // 获取评论回复数据
    const getCommentReplies = async (awemeId: string, commentId: string): Promise<any[]> => {
        let allReplies: any[] = [];
        let currentCursor = '0';
        let hasMoreReplies = true;

        while (hasMoreReplies) {
            try {
                const response = await apiRequest<any>(`/dy/comment/replies`, {
                    method: 'POST',
                    body: JSON.stringify({
                        aweme_id: awemeId,
                        comment_id: commentId,
                        cursor: currentCursor
                    }),
                });

                if (response.success && response.data?.comments) {
                    allReplies = [...allReplies, ...response.data.comments];
                    currentCursor = response.data.cursor || '';
                    hasMoreReplies = response.data.has_more || false;
                } else {
                    toast({
                        title: '回复内容加载失败',
                        description: response.message || '获取评论回复失败',
                        variant: 'destructive',
                    });
                    break;
                }
            } catch (error) {
                console.error('Failed to get comment replies:', error);
                toast({
                        title: '回复内容加载失败',
                        description: error.message || '获取评论回复失败',
                        variant: 'destructive',
                    });
                break;
            }
        }

        return allReplies;
    };

    const handleExport = async () => {
        // 验证导出数量
        const count = parseInt(exportCount);
        if (!exportCount || isNaN(count) || count <= 0) {
            toast({
                title: '参数错误',
                description: '请输入有效的导出条数',
                variant: 'destructive',
            });
            return;
        }

        if (count > total && total > 0) {
            toast({
                title: '参数错误',
                description: `导出条数不能超过总评论数 ${total.toLocaleString()}`,
                variant: 'destructive',
            });
            return;
        }

        setIsExporting(true);
        setExportProgress(0);

        try {
            // 先去重现有评论数据
            let allComments = [...comments];
            allComments = allComments.filter((comment, index, self) =>
                index === self.findIndex(c => c.cid === comment.cid)
            );

            // 检查是否需要加载更多评论数据
            // 注意：这里先加载足够的主评论，后续会根据实际需要的总行数（包含回复）进行调整
            if (allComments.length < count && hasMore) {
                setExportProgress(10);
                toast({
                    title: '正在加载更多数据',
                    description: `当前已有 ${allComments.length} 条评论（去重后），需要总计 ${count} 行数据（含回复），正在自动加载...`,
                });

                // 预估需要加载的评论数量（考虑到回复会增加总行数）
                const estimatedCommentsNeeded = includeReplies ? Math.ceil(count * 0.7) : count;
                allComments = await loadMoreCommentsForExport(estimatedCommentsNeeded);
                setExportProgress(30);
            } else {
                setExportProgress(30);
            }

            // 时间筛选
            let filteredComments = [...allComments];
            if (exportStartDate || exportEndDate) {
                const startTime = exportStartDate ? new Date(exportStartDate).getTime() / 1000 : 0;
                const endTime = exportEndDate ? new Date(exportEndDate + ' 23:59:59').getTime() / 1000 : Infinity;

                filteredComments = filteredComments.filter(comment => {
                    return comment.create_time >= startTime && comment.create_time <= endTime;
                });
            }

            // 关键词筛选
            if (exportKeywords.trim()) {
                const keywords = exportKeywords.trim().split(/\s+/).filter(Boolean);
                filteredComments = filteredComments.filter(comment => {
                    const commentText = (comment.text || '').toLowerCase();
                    return keywords.some(keyword => commentText.includes(keyword.toLowerCase()));
                });
            }

            // 筛选后再次去重，确保数据唯一性
            filteredComments = filteredComments.filter((comment, index, self) =>
                index === self.findIndex(c => c.cid === comment.cid)
            );

            // 注意：这里不再限制主评论数量，而是在构建CSV时控制总行数
            setExportProgress(50);

            // 如果需要包含回复，获取回复数据
            let repliesData: { [commentId: string]: any[] } = {};
            if (includeReplies) {
                toast({
                    title: '正在获取回复数据',
                    description: `正在获取 ${filteredComments.length} 条评论的回复...`,
                });

                for (let i = 0; i < filteredComments.length; i++) {
                    const comment = filteredComments[i];
                    if (comment.reply_comment_total > 0) {
                        try {
                            const replies = await getCommentReplies(comment.aweme_id, comment.cid);
                            // 对回复数据进行去重
                            const uniqueReplies = replies.filter((reply, index, self) =>
                                index === self.findIndex(r => r.cid === reply.cid)
                            );
                            repliesData[comment.cid] = uniqueReplies;
                        } catch (error) {
                            console.error(`Failed to get replies for comment ${comment.cid}:`, error);
                        }
                    }

                    // 更新进度
                    const progress = 50 + (i / filteredComments.length) * 30;
                    setExportProgress(Math.round(progress));
                }
            }

            setExportProgress(80);

            // 构建导出数据
            const headers = [
                '评论ID',
                '主评论ID',
                '是否是回复',
                '用户昵称',
                '用户ID',
                '评论内容',
                '图片数量',
                '图片链接',
                '点赞数',
                '回复数',
                '是否热门',
                '是否作者点赞',
                'IP归属地',
                '发布时间',
                '用户链接',
            ];

            const rows: any[][] = [headers];

            // 筛选条件
            const startTime = exportStartDate ? new Date(exportStartDate).getTime() / 1000 : 0;
            const endTime = exportEndDate ? new Date(exportEndDate + ' 23:59:59').getTime() / 1000 : Infinity;
            const hasTimeFilter = exportStartDate || exportEndDate;
            const keywords = exportKeywords.trim() ? exportKeywords.trim().split(/\s+/).filter(Boolean) : [];
            const hasKeywordFilter = keywords.length > 0;

            // 处理主评论和回复，控制总行数
            let currentRowCount = 0; // 当前已添加的行数（不包括表头）

            for (const comment of filteredComments) {
                // 检查是否已达到导出条数限制
                if (currentRowCount >= count) {
                    break;
                }

                // 添加主评论行
                const mainCommentRow = [
                    comment.cid,
                    '', // 主评论ID为空
                    '否', // 不是回复
                    comment.user.nickname,
                    comment.user.unique_id || comment.user.short_id,
                    comment.text,
                    comment.image_list?.length || 0,
                    comment.image_list?.map(img => img.origin_url?.url_list?.slice(-1)[0] || '').filter(Boolean).join('; ') || '-',
                    comment.digg_count,
                    comment.reply_comment_total,
                    comment.is_hot ? '是' : '否',
                    comment.is_author_digged ? '是' : '否',
                    comment.ip_label || '-',
                    new Date(comment.create_time * 1000).toLocaleString(),
                    `https://www.douyin.com/user/${comment.user.sec_uid}`,
                ];
                rows.push(mainCommentRow);
                currentRowCount++;

                // 如果包含回复，添加回复行（需要对回复也进行时间筛选）
                if (includeReplies && repliesData[comment.cid] && currentRowCount < count) {
                    const replies = repliesData[comment.cid] || [];
                    for (const reply of replies) {
                        // 检查是否已达到导出条数限制
                        if (currentRowCount >= count) {
                            break;
                        }

                        // 对回复进行时间筛选
                        const replyTime = reply.create_time || 0;
                        if (hasTimeFilter && (replyTime < startTime || replyTime > endTime)) {
                            continue; // 跳过不满足时间条件的回复
                        }

                        // 对回复进行关键词筛选
                        if (hasKeywordFilter) {
                            const replyText = (reply.text || '').toLowerCase();
                            const matchesKeyword = keywords.some(keyword => replyText.includes(keyword.toLowerCase()));
                            if (!matchesKeyword) {
                                continue; // 跳过不包含关键词的回复
                            }
                        }

                        const replyRow = [
                            reply.cid || `${comment.cid}_reply_${reply.reply_id || Math.random()}`, // 回复ID
                            comment.cid, // 主评论ID
                            '是', // 是回复
                            reply.user?.nickname || '未知用户',
                            reply.user?.unique_id || reply.user?.short_id || '-',
                            reply.text || '',
                            reply.image_list?.length || 0,
                            reply.image_list?.map((img: any) => img.origin_url?.url_list?.slice(-1)[0] || '').filter(Boolean).join('; ') || '-',
                            reply.digg_count || 0,
                            reply.reply_comment_total || 0,
                            reply.is_hot ? '是' : '否',
                            reply.is_author_digged ? '是' : '否',
                            reply.ip_label || '-',
                            new Date((reply.create_time || 0) * 1000).toLocaleString(),
                            reply.user?.sec_uid ? `https://www.douyin.com/user/${reply.user.sec_uid}` : '-',
                        ];
                        rows.push(replyRow);
                        currentRowCount++;
                    }
                }
            }

            setExportProgress(90);

            exportArrayToCSV(rows, `抖音评论提取结果_${new Date().toLocaleString()}.csv`);

            setExportProgress(100);

            // 计算总行数（不包括表头）
            const totalRows = rows.length - 1;
            // 计算主评论和回复的数量
            let mainCommentCount = 0;
            let replyCount = 0;
            for (let i = 1; i < rows.length; i++) { // 跳过表头
                if (rows[i][2] === '否') { // 是否是回复列
                    mainCommentCount++;
                } else {
                    replyCount++;
                }
            }

            // 构建筛选提示信息
            const filterInfo: string[] = [];
            if (hasTimeFilter) filterInfo.push('时间筛选');
            if (hasKeywordFilter) filterInfo.push('关键词筛选');
            const filterText = filterInfo.length > 0 ? `（已应用${filterInfo.join('、')}）` : '';

            toast({
                title: '导出成功',
                description: `已导出 ${mainCommentCount} 条主评论${includeReplies ? `，${replyCount} 条回复，共 ${totalRows} 行数据` : ''}${filterText}`,
            });

            // 重置状态
            setTimeout(() => {
                setIsExportDialogOpen(false);
                setExportProgress(0);
                setExportStartDate('');
                setExportEndDate('');
                setIncludeReplies(false);
                setExportCount('');
                setExportKeywords('');
            }, 1000);

        } catch (error) {
            console.error('Export failed:', error);
            toast({
                title: '导出失败',
                description:  error as Error ? error.message : "导出过程中发生错误，请重试",
                variant: 'destructive',
            });
        } finally {
            setIsExporting(false);
        }
    };



    return (
        <TooltipProvider>
            <div className="space-y-4">
            <Card className="p-6">
                <div className="space-y-4">
                    <div className="flex items-center gap-2">
                        <MessageSquare className="h-5 w-5" />
                        <Label htmlFor="video-url" className="text-base font-medium">
                            抖音视频评论提取
                        </Label>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="video-url">视频链接</Label>
                        <Input
                            id="video-url"
                            placeholder="请输入抖音视频链接，例如：https://www.douyin.com/video/7527310034141859135"
                            value={videoUrl}
                            onChange={(e) => setVideoUrl(e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && handleExtract()}
                            disabled={isLoading}
                        />
                    </div>
                </div>

                <div className="flex justify-end mt-4 gap-2">
                    <Button onClick={handleExtract} disabled={isLoading || !videoUrl.trim()}>
                        <Search className="w-4 h-4 mr-2" />
                        {isLoading ? '提取中...' : '提取评论'}
                    </Button>
                    <Button 
                        variant="outline" 
                        onClick={handleClear} 
                        disabled={isLoading || (!videoUrl.trim() && comments.length === 0)}
                    >
                        清空
                    </Button>
                </div>
            </Card>

            {isLoading && (
                <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
            )}

            {comments.length > 0 && (
                <div>
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-2">
                            <h2 className="text-xl font-semibold">
                                评论列表 - 已加载 {comments.length.toLocaleString()} 条{total > 0 && ` / 总计 ${total.toLocaleString()} 条`}
                            </h2>
                            {isShowingExampleData && (
                                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                                    示例数据
                                </span>
                            )}
                        </div>
                        <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
                            <DialogTrigger asChild>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="outline"
                                            onClick={isShowingExampleData ? undefined : handleExportClick}
                                            disabled={isShowingExampleData}
                                        >
                                            <Download className="w-4 h-4 mr-2" />
                                            导出 CSV
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{isShowingExampleData ? '示例数据不支持导出' : '导出评论数据'}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </DialogTrigger>
                            <DialogContent className="max-w-lg max-h-[90vh] overflow-hidden flex flex-col">
                                <DialogHeader className="flex-shrink-0">
                                    <DialogTitle>导出评论数据</DialogTitle>
                                </DialogHeader>

                                <div className="flex-1 overflow-y-auto pr-1 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                    {/* 重要提示 */}
                                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                        <div className="flex items-start gap-2">
                                            <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                                            <div className="text-sm text-yellow-800">
                                                <p className="font-medium mb-1">重要提示：</p>
                                                <ul className="space-y-1 text-xs">
                                                    <li>• 由于接口返回数据存在乱序，筛选时间或关键词时可能导致积分消耗与导出内容不完全对应。</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 导出条数 */}
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                            <Label htmlFor="export-count" className="text-sm font-medium text-gray-700">
                                                导出条数
                                            </Label>
                                            <span className="text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full font-medium">必填</span>
                                        </div>
                                        <div className="relative">
                                            <Input
                                                id="export-count"
                                                type="number"
                                                placeholder={`最多 ${total > 0 ? total.toLocaleString() : '未知'} 行`}
                                                value={exportCount}
                                                onChange={(e) => setExportCount(e.target.value)}
                                                disabled={isExporting}
                                                min="1"
                                                max={total > 0 ? total : undefined}
                                                className="text-sm border border-gray-200 focus:border-orange-400 focus:ring-orange-400/20 transition-colors pl-3 pr-16 rounded-md"
                                            />
                                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500 font-medium">
                                                行数据
                                            </div>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="text-xs text-gray-600 bg-gray-50 border border-gray-200 rounded-lg p-2">
                                                <div className="flex items-center gap-2 mb-1">
                                                    <div className="w-3 h-3 rounded-full bg-gray-200 flex items-center justify-center">
                                                        <div className="w-1.5 h-1.5 rounded-full bg-gray-500"></div>
                                                    </div>
                                                    <span className="font-medium text-gray-700">数据统计</span>
                                                </div>
                                                <p>
                                                    当前已加载 <span className="font-medium text-blue-600">{comments.length.toLocaleString()}</span> 条评论
                                                    {total > 0 && (
                                                        <span> / 总计 <span className="font-medium text-green-600">{total.toLocaleString()}</span> 条</span>
                                                    )}
                                                </p>
                                            </div>
                                            <div className="text-xs text-orange-700 bg-orange-50 border border-orange-200 rounded-lg p-2">
                                                <div className="flex items-start gap-2">
                                                    <div className="w-3 h-3 rounded-full bg-orange-100 flex items-center justify-center mt-0.5">
                                                        <div className="w-1.5 h-1.5 rounded-full bg-orange-500"></div>
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-orange-800 mb-1">重要提醒</p>
                                                        <p>导出条数是 <span className="font-medium">评论+回复</span> 的总行数，不仅仅是主评论数量</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 时间筛选 */}
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                            <Label className="text-sm font-medium text-gray-700">时间筛选</Label>
                                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">可选</span>
                                        </div>
                                        <div className="grid grid-cols-2 gap-3">
                                            <div className="space-y-1.5">
                                                <Label htmlFor="start-date" className="text-xs font-medium text-gray-600 flex items-center gap-1">
                                                    <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                                                    开始时间
                                                </Label>
                                                <div className="relative group">
                                                    <Input
                                                        id="start-date"
                                                        type="date"
                                                        value={exportStartDate}
                                                        onChange={(e) => setExportStartDate(e.target.value)}
                                                        disabled={isExporting}
                                                        className="text-sm border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 transition-all duration-200 pl-10 pr-10 py-2.5 rounded-lg bg-white hover:bg-gray-50 focus:bg-white custom-date-picker"
                                                        style={{
                                                            colorScheme: 'light',
                                                        }}
                                                    />
                                                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-blue-500 transition-colors duration-200">
                                                        <Calendar className="w-4 h-4" />
                                                    </div>
                                                    {exportStartDate && (
                                                        <button
                                                            type="button"
                                                            onClick={() => setExportStartDate('')}
                                                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200 z-10"
                                                            disabled={isExporting}
                                                        >
                                                            <div className="w-4 h-4 rounded-full bg-gray-200 hover:bg-red-100 flex items-center justify-center">
                                                                <span className="text-xs">×</span>
                                                            </div>
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="space-y-1.5">
                                                <Label htmlFor="end-date" className="text-xs font-medium text-gray-600 flex items-center gap-1">
                                                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                                                    结束时间
                                                </Label>
                                                <div className="relative group">
                                                    <Input
                                                        id="end-date"
                                                        type="date"
                                                        value={exportEndDate}
                                                        onChange={(e) => setExportEndDate(e.target.value)}
                                                        disabled={isExporting}
                                                        className="text-sm border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 transition-all duration-200 pl-10 pr-10 py-2.5 rounded-lg bg-white hover:bg-gray-50 focus:bg-white custom-date-picker"
                                                        style={{
                                                            colorScheme: 'light',
                                                        }}
                                                    />
                                                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-blue-500 transition-colors duration-200">
                                                        <Calendar className="w-4 h-4" />
                                                    </div>
                                                    {exportEndDate && (
                                                        <button
                                                            type="button"
                                                            onClick={() => setExportEndDate('')}
                                                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200 z-10"
                                                            disabled={isExporting}
                                                        >
                                                            <div className="w-4 h-4 rounded-full bg-gray-200 hover:bg-red-100 flex items-center justify-center">
                                                                <span className="text-xs">×</span>
                                                            </div>
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        
                                    </div>

                                    {/* 关键词筛选 */}
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                            <Label htmlFor="export-keywords" className="text-sm font-medium text-gray-700">关键词筛选</Label>
                                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">可选</span>
                                        </div>
                                        <div className="relative">
                                            <Input
                                                id="export-keywords"
                                                type="text"
                                                placeholder="输入关键词，多个关键词用空格分隔"
                                                value={exportKeywords}
                                                onChange={(e) => setExportKeywords(e.target.value)}
                                                disabled={isExporting}
                                                className="text-sm border-gray-200 focus:border-purple-400 focus:ring-purple-400/20 transition-colors pl-3 pr-10"
                                            />
                                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                                <div className="w-4 h-4 text-gray-400">
                                                    <svg viewBox="0 0 16 16" fill="currentColor">
                                                        <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-xs text-gray-500 bg-purple-50 border border-purple-200 rounded-lg p-2">
                                            <div className="flex items-start gap-2">
                                                <div className="w-3 h-3 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                                                    <div className="w-1.5 h-1.5 rounded-full bg-purple-500"></div>
                                                </div>
                                                <div>
                                                    <p className="font-medium text-purple-800 mb-1">关键词筛选说明</p>
                                                    <ul className="space-y-0.5 text-purple-700">
                                                        <li>• 支持多个关键词，用空格分隔</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 是否包含回复 */}
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <Label className="text-sm font-medium text-gray-700">回复选项</Label>
                                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">可选</span>
                                        </div>
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                            <div className="flex items-start space-x-3">
                                                <Checkbox
                                                    id="include-replies"
                                                    checked={includeReplies}
                                                    onCheckedChange={(checked) => setIncludeReplies(checked as boolean)}
                                                    disabled={isExporting}
                                                    className="mt-0.5"
                                                />
                                                <div className="flex-1">
                                                    <Label htmlFor="include-replies" className="text-sm font-medium text-green-800 cursor-pointer">
                                                        包含评论回复内容
                                                    </Label>
                                                    <p className="text-xs text-green-700 mt-1">
                                                        勾选后将获取每条评论的回复数据，会增加积分消耗和导出时间
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                                </div>

                                {/* 操作按钮 - 固定在底部 */}
                                <div className="flex-shrink-0 border-t border-gray-200 pt-4 mt-4">
                                    <div className="flex items-center justify-between">
                                        {/* 进度条 - 左侧 */}
                                        <div className="flex-1 mr-4">
                                            {isExporting && (
                                                <div className="space-y-1">
                                                    <div className="flex justify-between text-xs text-gray-600">
                                                        <span>导出进度</span>
                                                        <span>{exportProgress}%</span>
                                                    </div>
                                                    <Progress value={exportProgress} className="w-full h-2" />
                                                </div>
                                            )}
                                        </div>

                                        {/* 按钮 - 右侧 */}
                                        <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            onClick={() => setIsExportDialogOpen(false)}
                                            disabled={isExporting}
                                        >
                                            取消
                                        </Button>
                                        <Button
                                            onClick={handleExport}
                                            disabled={isExporting || !exportCount}
                                        >
                                            {isExporting ? '导出中...' : '开始导出'}
                                        </Button>
                                        </div>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                    <div className="border rounded-md overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[60px]">头像</TableHead>
                                    <TableHead className="w-[120px]">用户</TableHead>
                                    <TableHead className="w-[300px]">评论内容</TableHead>
                                    <TableHead className="w-[120px]">图片</TableHead>
                                    <TableHead className="w-[80px]">点赞</TableHead>
                                    <TableHead className="w-[80px]">回复</TableHead>
                                    <TableHead className="w-[80px]">热门</TableHead>
                                    <TableHead className="w-[100px]">IP归属地</TableHead>
                                    <TableHead className="w-[160px]">发布时间</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {comments.map((comment) => (
                                    <TableRow key={comment.cid}>
                                        <TableCell>
                                            {isShowingExampleData ? (
                                                <ExampleImageSVG width={40} height={40} type="avatar" />
                                            ) : (
                                                <img
                                                    src={comment.user.avatar_thumb?.url_list?.slice(-1)[0] || ''}
                                                    alt={comment.user.nickname}
                                                    className="w-10 h-10 rounded-full object-cover"
                                                    loading="lazy"
                                                    referrerPolicy="no-referrer"
                                                />
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <div className="space-y-1">
                                                {isShowingExampleData ? (
                                                    <span className="text-gray-700 text-sm font-medium">
                                                        {comment.user.nickname}
                                                    </span>
                                                ) : (
                                                    <a
                                                        href={`https://www.douyin.com/user/${comment.user.sec_uid}`}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-blue-600 hover:underline text-sm font-medium"
                                                    >
                                                        {comment.user.nickname}
                                                    </a>
                                                )}
                                                <p className="text-xs text-gray-500">
                                                    @{comment.user.unique_id || comment.user.short_id}
                                                </p>
                                            </div>
                                        </TableCell>
                                        <TableCell className="max-w-[400px]">
                                            <div className="line-clamp-3 text-sm">{comment.text}</div>
                                        </TableCell>
                                        <TableCell>
                                            {isShowingExampleData ? (
                                                // 示例数据模式：显示一个示例图片
                                                <div className="flex flex-wrap gap-1">
                                                    <div className="relative">
                                                        <ExampleImageSVG width={64} height={64} type="video" />
                                                    </div>
                                                </div>
                                            ) : (
                                                // 真实数据模式：显示真实图片
                                                comment.image_list && comment.image_list.length > 0 && (
                                                    <div className="flex flex-wrap gap-1">
                                                        {comment.image_list.slice(0, 3).map((image, index) => (
                                                            <div key={index} className="relative">
                                                                <img
                                                                    src={image.thumb_url?.url_list?.slice(-1)[0] || image.crop_url?.url_list?.slice(-1)[0] || ''}
                                                                    alt={`评论图片 ${index + 1}`}
                                                                    className="w-16 h-16 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                                                                    loading="lazy"
                                                                    referrerPolicy="no-referrer"
                                                                    onClick={() => {
                                                                        const fullImageUrl = image.origin_url?.url_list?.slice(-1)[0] || image.medium_url?.url_list?.slice(-1)[0] || '';
                                                                        if (fullImageUrl) {
                                                                            window.open(fullImageUrl, '_blank');
                                                                        }
                                                                    }}
                                                                />
                                                                {comment.image_list!.length > 3 && index === 2 && (
                                                                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded flex items-center justify-center text-white text-xs font-medium">
                                                                        +{comment.image_list!.length - 3}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        ))}
                                                    </div>
                                                )
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-1">
                                                {comment.is_author_digged && (
                                                    <span className="text-red-500 text-xs">❤️</span>
                                                )}
                                                {formatNumber(comment.digg_count)}
                                            </div>
                                        </TableCell>
                                        <TableCell>{formatNumber(comment.reply_comment_total)}</TableCell>
                                        <TableCell>
                                            {comment.is_hot && (
                                                <span className="text-orange-500 text-xs">🔥</span>
                                            )}
                                        </TableCell>
                                        <TableCell>{comment.ip_label || '-'}</TableCell>
                                        <TableCell className="whitespace-pre-line text-sm">
                                            {new Date(comment.create_time * 1000).toLocaleString()}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>

                    {/* 加载更多按钮 */}
                    {hasMore && (
                        <div className="flex justify-center mt-4">
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        onClick={isShowingExampleData ? undefined : handleLoadMore}
                                        disabled={isShowingExampleData || isLoadingMore}
                                        variant="outline"
                                    >
                                        {isLoadingMore ? (
                                            <>
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                                                加载中...
                                            </>
                                        ) : (
                                            <>
                                                <Search className="w-4 h-4 mr-2" />
                                                加载更多评论
                                            </>
                                        )}
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多评论数据'}</p>
                                </TooltipContent>
                            </Tooltip>
                        </div>
                    )}
                </div>
            )}
        </div>
        </TooltipProvider>
    );
};

export default DouyinCommentExtractor;
