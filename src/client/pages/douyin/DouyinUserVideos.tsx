import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Checkbox } from '../../components/ui/checkbox';
import { Search, Download, BarChart3 } from 'lucide-react';
import { useToast } from '../../components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { exportArrayToCSV, formatDuration, formatNumber } from '../../lib/utils';
import { douyinApi, DouyinUserVideo, DouyinStarDataResponse } from '../../lib/api';

// 扩展视频接口以支持星图数据
interface ExtendedDouyinUserVideo extends DouyinUserVideo {
  selected?: boolean;
  starDataLoading?: boolean;
  showStarData?: boolean;
  starData?: {
    vv_cnt?: number;
    finish_rate?: number;
  };
}

const DouyinUserVideos: React.FC = () => {
  const [userUrl, setUserUrl] = useState('');
  const [userId, setUserId] = useState('');
  const [videos, setVideos] = useState<ExtendedDouyinUserVideo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [maxCursor, setMaxCursor] = useState<number>(0);
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());
  const [isLoadingStarData, setIsLoadingStarData] = useState(false);
  const [isShowingExampleData, setIsShowingExampleData] = useState(true);
  const { toast } = useToast();

  // 加载示例数据
  const loadExampleData = async () => {
    try {
      const response = await fetch('/test/data/douyin/userVideos.json');
      const data = await response.json();

      // 直接断言数据类型，避免大量赋值代码
      const convertedVideos: ExtendedDouyinUserVideo[] = data.data.aweme_list.map((video: any) => ({
        ...video,
        starDataLoading: false,
        showStarData: false
      })) as ExtendedDouyinUserVideo[];

      setVideos(convertedVideos);
      setHasMore(false);

      toast({
        title: '示例数据已加载',
        description: `已加载 ${convertedVideos.length} 条示例视频数据`,
      });
    } catch (error) {
      console.error('Failed to load example data:', error);
      toast({
        variant: 'destructive',
        title: '加载示例数据失败',
        description: '无法加载示例数据，请检查文件是否存在',
      });
    }
  };

  // 组件挂载时加载示例数据
  useEffect(() => {
    loadExampleData();
  }, []);

  const extractUserId = (url: string): string => {
    // 提取抖音用户ID的逻辑
    // 支持多种抖音用户链接格式
    const patterns = [
      /user\/([^/?]+)/,
      /profile\/([^/?]+)/,
      /sec_uid=([^&]+)/,
      /uid=([^&]+)/,
      /\/([^/?]+)$/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return '';
  };

  // 获取星图数据
  const fetchStarData = async (videoUrl: string, videoId: string) => {
    try {
      // 设置loading状态
      setVideos(prev => prev.map(video =>
        video.aweme_id === videoId ? { ...video, starDataLoading: true } : video
      ));

      const response = await douyinApi.getStarData(videoUrl) as DouyinStarDataResponse;

      if (response.success && response.data?.video_info) {
        const starInfo = response.data.video_info;
        setVideos(prev => prev.map(video =>
          video.aweme_id === videoId ? {
            ...video,
            starData: {
              vv_cnt: starInfo.vv_cnt,
              finish_rate: starInfo.finish_rate
            },
            showStarData: true,
            starDataLoading: false
          } : video
        ));

        toast({
          title: '星图数据获取成功',
          description: `已获取视频的星图数据`
        });
      } else {
        // 获取失败时清除loading状态
        setVideos(prev => prev.map(video =>
          video.aweme_id === videoId ? { ...video, starDataLoading: false } : video
        ));

        toast({
          title: '星图数据获取失败',
          description: '该视频可能没有星图数据或数据不可见',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('获取星图数据失败:', error);
      // 获取失败时清除loading状态
      setVideos(prev => prev.map(video =>
        video.aweme_id === videoId ? { ...video, starDataLoading: false } : video
      ));
      toast({
        title: '星图数据获取失败',
        description: '请稍后重试',
        variant: 'destructive'
      });
    }
  };

  // 处理单个视频勾选
  const handleVideoSelect = (videoId: string, checked: boolean) => {
    setSelectedVideos(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(videoId);
      } else {
        newSet.delete(videoId);
      }
      return newSet;
    });
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVideos(new Set(videos.map(video => video.aweme_id)));
    } else {
      setSelectedVideos(new Set());
    }
  };

  // 批量查看星图数据
  const handleBatchViewStarData = async () => {
    if (selectedVideos.size === 0) {
      toast({
        title: '请先选择视频',
        description: '请勾选要查看星图数据的视频',
        variant: 'destructive'
      });
      return;
    }

    setIsLoadingStarData(true);
    const selectedVideoList = videos.filter(video => selectedVideos.has(video.aweme_id));

    try {
      // 并发获取星图数据，但限制并发数量
      const batchSize = 3; // 每批处理3个视频
      for (let i = 0; i < selectedVideoList.length; i += batchSize) {
        const batch = selectedVideoList.slice(i, i + batchSize);
        await Promise.all(
          batch.map(video =>
            fetchStarData(`https://www.douyin.com/video/${video.aweme_id}`, video.aweme_id)
          )
        );

        // 批次间稍作延迟，避免请求过于频繁
        if (i + batchSize < selectedVideoList.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      toast({
        title: '批量获取完成',
        description: `已完成 ${selectedVideos.size} 个视频的星图数据获取`
      });
    } catch (error) {
      console.error('批量获取星图数据失败:', error);
      toast({
        title: '批量获取失败',
        description: '部分视频的星图数据获取失败',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingStarData(false);
    }
  };

  const handleSearch = async () => {
    if (!userUrl.trim()) return;
    
    const extractedUserId = extractUserId(userUrl);
    if (!extractedUserId) {
      toast({
        variant: "destructive",
        title: "无效的用户链接",
        description: "请输入正确的抖音用户主页链接",
      });
      return;
    }
    
    setUserId(extractedUserId);
    setIsLoading(true);
    setVideos([]);
    setMaxCursor(0);
    setSelectedVideos(new Set()); // 重置选择状态
    setIsShowingExampleData(false);
    
    try {
      const response = await douyinApi.getUserVideos(userUrl);
      if (response.success && response.data) {
        const videoData = response.data as any;
        const items = videoData.aweme_list || [];
        setVideos(items);
        setHasMore(videoData.has_more === 1);
        setMaxCursor(videoData.max_cursor || 0);

        if (items.length > 0) {
          toast({
            variant: "default",
            title: "数据获取成功",
            description: `已加载 ${items.length} 条视频`,
          });
        } else {
          toast({
            title: "未找到数据",
            description: "该用户暂无视频或数据不可见",
          });
        }
      } else {
        toast({
          variant: "destructive",
          title: "获取数据失败",
          description: response.message || "获取用户视频失败，请稍后重试",
        });
      }
    } catch (error) {
      console.error('Failed to fetch user videos:', error);
      toast({
        variant: "destructive",
        title: "获取数据失败",
        description: error instanceof Error ? error.message : "获取用户视频失败，请稍后重试",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadMore = async () => {
    if (isLoadingMore || !hasMore || !userUrl) return;

    setIsLoadingMore(true);
    try {
      const response = await douyinApi.getUserVideos(userUrl, maxCursor);
      if (response.success && response.data) {
        const videoData = response.data as any;
        const items = videoData.aweme_list || [];
        // Filter out duplicates
        const uniqueItems = items.filter((item: any) => !videos.some(video => video.aweme_id === item.aweme_id));
        setVideos([...videos, ...uniqueItems]);
        setHasMore(videoData.has_more === 1);
        setMaxCursor(videoData.max_cursor || maxCursor);

        if (uniqueItems.length > 0) {
          toast({
            variant: "default",
            title: "加载成功",
            description: `已加载额外 ${uniqueItems.length} 条视频`,
          });
        }
      } else {
        toast({
          variant: "destructive",
          title: "加载更多数据失败",
          description: response.message || "获取更多视频失败，请稍后重试",
        });
      }
    } catch (error) {
      console.error('Failed to load more videos:', error);
      toast({
        variant: "destructive",
        title: "加载更多数据失败",
        description: error instanceof Error ? error.message : "获取更多视频失败，请稍后重试",
      });
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleExport = () => {
    if (videos.length === 0) return;

    const headers = ['视频ID', '标题', '作者', '作者ID', '点赞', '评论', '分享', '收藏', '推荐', '时长(秒)', '发布时间', '视频链接', '星图播放量', '星图完播率'];

    const rows = [
      headers,
      ...videos.map(video => [
        video.aweme_id,
        video.desc,
        video.author.nickname,
        video.author.uid,
        video.statistics?.digg_count || 0,
        video.statistics?.comment_count || 0,
        video.statistics?.share_count || 0,
        video.statistics?.collect_count || 0,
        video.statistics?.recommend_count || 0,
        video.video?.duration ? Math.round(video.video.duration / 1000) : 0,
        new Date(video.create_time * 1000).toLocaleString(),
        `https://www.douyin.com/video/${video.aweme_id}`,
        video.starData?.vv_cnt || '-',
        video.starData?.finish_rate ? `${(video.starData.finish_rate * 100).toFixed(2)}%` : '-',
      ])
    ];

    exportArrayToCSV(rows, `抖音用户作品_${videos[0]?.author.nickname || userId}_${new Date().toLocaleString()}.csv`);

    toast({
      variant: "default",
      title: "导出成功",
      description: `已导出 ${videos.length} 条视频数据`,
    });
  };

  return (
    <TooltipProvider>
      <div className="p-4 space-y-4">
      <Card className="p-6">
        <div className="space-y-4 mb-4">
          <div className="flex gap-2">
            <Input
              placeholder="请输入抖音用户主页链接，例如：https://www.douyin.com/user/MS4wLjABAAAA..."
              value={userUrl}
              onChange={(e) => setUserUrl(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch} disabled={isLoading}>
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
          </div>
        </div>

        {videos.length > 0 && (
          <div className="mt-6 space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <h2 className="text-lg font-semibold">用户: {videos[0]?.author.nickname || userId} - 已加载 {videos.length} 条视频</h2>
                  {isShowingExampleData && (
                    <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      示例数据
                    </span>
                  )}
                </div>
                {selectedVideos.size > 0 && (
                  <span className="text-sm text-gray-600">已选择 {selectedVideos.size} 个视频</span>
                )}
              </div>
              <div className="flex gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={isShowingExampleData ? undefined : handleBatchViewStarData}
                      disabled={isShowingExampleData || selectedVideos.size === 0 || isLoadingStarData}
                    >
                      <BarChart3 className="w-4 h-4 mr-2" />
                      {isLoadingStarData ? '获取中...' : '查看星图数据'}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isShowingExampleData ? '示例数据不支持查看星图数据' : '查看选中视频的星图数据'}</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={isShowingExampleData ? undefined : handleExport}
                      disabled={isShowingExampleData}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      导出
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isShowingExampleData ? '示例数据不支持导出' : '导出视频数据'}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox
                        checked={selectedVideos.size === videos.length && videos.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="w-[100px]">封面</TableHead>
                    <TableHead>标题</TableHead>
                    <TableHead>作者</TableHead>
                    <TableHead className="w-[80px]">点赞</TableHead>
                    <TableHead className="w-[80px]">评论</TableHead>
                    <TableHead className="w-[80px]">分享</TableHead>
                    <TableHead className="w-[80px]">收藏</TableHead>
                    <TableHead className="w-[80px]">推荐</TableHead>
                    <TableHead className="w-[80px]">时长</TableHead>
                    <TableHead className="w-[160px]">发布时间</TableHead>
                    <TableHead className="w-[100px]">播放量</TableHead>
                    <TableHead className="w-[100px]">完播率</TableHead>
                    <TableHead className="w-[120px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {videos.map((video) => (
                    <TableRow key={video.aweme_id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedVideos.has(video.aweme_id)}
                          onCheckedChange={(checked) => handleVideoSelect(video.aweme_id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        {isShowingExampleData ? (
                          <ExampleImageSVG width={80} height={80} type="video" />
                        ) : video.video?.cover?.url_list?.[0] ? (
                          <img
                            src={video.video.cover.url_list[0]}
                            alt={video.desc}
                            className="w-20 h-20 object-cover rounded"
                            loading="lazy"
                            referrerPolicy="no-referrer"
                          />
                        ) : null}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {isShowingExampleData ? (
                          <span className="text-gray-700">
                            {video.desc || '无标题'}
                          </span>
                        ) : (
                          <a
                            href={`https://www.douyin.com/video/${video.aweme_id}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {video.desc || '无标题'}
                          </a>
                        )}
                      </TableCell>
                      <TableCell>
                        {isShowingExampleData ? (
                          <span className="text-gray-700">
                            {video.author.nickname}
                          </span>
                        ) : (
                          <a
                            href={`https://www.douyin.com/user/${video.author.sec_uid}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {video.author.nickname}
                          </a>
                        )}
                      </TableCell>
                      <TableCell>{formatNumber(video.statistics?.digg_count || 0)}</TableCell>
                      <TableCell>{formatNumber(video.statistics?.comment_count || 0)}</TableCell>
                      <TableCell>{formatNumber(video.statistics?.share_count || 0)}</TableCell>
                      <TableCell>{formatNumber(video.statistics?.collect_count || 0)}</TableCell>
                      <TableCell>{formatNumber(video.statistics?.recommend_count || 0)}</TableCell>
                      <TableCell>
                        {formatDuration(Number(video.video?.duration) / 1000) || '-'}
                      </TableCell>
                      <TableCell className="whitespace-pre-line w-[160px]">
                        {new Date(video.create_time * 1000).toLocaleString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                          hour12: false
                        }).replace(/\/\//g, '-').replace(',', '\n')}
                      </TableCell>
                      <TableCell className="w-[100px]">
                        {video.starDataLoading ? (
                          <div className="flex items-center text-sm text-gray-500">
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1"></div>
                            加载中...
                          </div>
                        ) : video.showStarData && video.starData ? (
                          formatNumber(video.starData.vv_cnt || 0)
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell className="w-[100px]">
                        {video.starDataLoading ? (
                          <div className="flex items-center text-sm text-gray-500">
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1"></div>
                            加载中...
                          </div>
                        ) : video.showStarData && video.starData ? (
                          video.starData.finish_rate ? `${(video.starData.finish_rate * 100).toFixed(1)}%` : '-'
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell className="w-[120px]">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={isShowingExampleData ? undefined : () => fetchStarData(`https://www.douyin.com/video/${video.aweme_id}`, video.aweme_id)}
                              disabled={isShowingExampleData || video.starDataLoading}
                              className="h-7 px-2 text-xs"
                            >
                              {video.starDataLoading ? (
                                <div className="flex items-center">
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1"></div>
                                  加载中
                                </div>
                              ) : video.showStarData ? (
                                '已获取'
                              ) : (
                                <>
                                  <BarChart3 className="w-3 h-3 mr-1" />
                                  查看星图数据
                                </>
                              )}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{isShowingExampleData ? '示例数据不支持查看星图数据' : '查看该视频的星图数据'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {hasMore && (
              <div className="flex justify-center mt-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={isShowingExampleData ? undefined : handleLoadMore}
                      disabled={isShowingExampleData || isLoadingMore}
                      variant="outline"
                    >
                      {isLoadingMore ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                          加载中...
                        </div>
                      ) : (
                        '加载更多'
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多视频数据'}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>
        )}

        {isLoading && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
      </Card>
    </div>
    </TooltipProvider>
  );
};

export default DouyinUserVideos;
