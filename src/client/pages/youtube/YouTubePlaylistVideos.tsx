import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';

import { Search } from 'lucide-react';
import YouTubeVideoTable, { YouTubeVideoDetail } from '../../components/youtube/YouTubeVideoTable';
import { useYouTubeVideoHandler } from '../../components/youtube/YouTubeVideoHandler';
import { useYouTubeVideoExporter } from '../../components/youtube/YouTubeVideoExporter';
import { useYouTubeVideoDetails } from '../../components/youtube/useYouTubeVideoDetails';
import { ApiError, youtubeApi, YouTubeVideo } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import { TooltipProvider } from '../../components/ui/tooltip';

const YouTubePlaylistVideos: React.FC = () => {
	const { toast } = useToast();
	const [playlistUrl, setPlaylistUrl] = useState('');

	const [videos, setVideos] = useState<YouTubeVideo[]>([]);
	const [videoDetails, setVideoDetails] = useState<Map<string, YouTubeVideoDetail>>(new Map());
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [currentPage, setCurrentPage] = useState(0);
	const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());
	const [isLoadingDetails, setIsLoadingDetails] = useState(false);
	const [hasMoreData, setHasMoreData] = useState(true);
	const [pageSize] = useState(20);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);
	// 跟踪正在加载详情的视频
	const [loadingVideoDetails, setLoadingVideoDetails] = useState<Set<string>>(new Set());

	// 使用通用的视频详情处理hook
	const { fetchVideoDetails } = useYouTubeVideoDetails({
		videos,
		videoDetails,
		setVideoDetails,
		includeDetails: false, // 不再自动获取详情，改为按需获取
	});

	// 使用公共hooks
	const videoHandler = useYouTubeVideoHandler({
		videos,
		videoDetails,
		setVideoDetails,
		loadingVideoDetails,
		setLoadingVideoDetails,
		toast,
	});

	const exporter = useYouTubeVideoExporter({
		videos,
		videoDetails,
		setVideoDetails,
		setVideos,
		currentPage,
		setCurrentPage,
		hasMoreData,
		setHasMoreData,
		pageSize,
		toast,
		userInfo: null,
		contentType: 'playlist' as const,
		userUrl: playlistUrl,
		youtubeApi,
	});

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			// 加载播放列表视频示例数据
			const response = await fetch('/test/data/YouTube/playlistsVideo.json');
			const data = await response.json();

			// 转换视频数据
			const convertedVideos: YouTubeVideo[] = data.data.map((video: any) => ({
				id: video.id,
				title: video.title,
				description: video.description,
				thumbnails: video.thumbnails,
				duration: video.duration,
				uploadDate: video.upload_date || '',
				viewCount: video.view_count,
				url: video.url,
				channel: {
					id: video.channel_id,
					name: video.channel,
				},
			}));

			setVideos(convertedVideos);
			setHasMoreData(false);

			toast({
				title: '示例数据已加载',
				description: `已加载 ${convertedVideos.length} 条示例播放列表视频数据`,
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	// 处理视频选择
	const handleVideoSelect = (videoId: string, selected: boolean) => {
		setSelectedVideos(prev => {
			const newSet = new Set(prev);
			if (selected) {
				newSet.add(videoId);
			} else {
				newSet.delete(videoId);
			}
			return newSet;
		});
	};

	// 处理全选/取消全选
	const handleSelectAll = (selected: boolean) => {
		if (selected) {
			setSelectedVideos(new Set(videos.map(v => v.id!)));
		} else {
			setSelectedVideos(new Set());
		}
	};

	// 查看选中视频的详情
	const handleViewDetails = async () => {
		if (selectedVideos.size === 0) {
			toast({
				title: '请选择视频',
				description: '请先选择要查看详情的视频',
				variant: 'destructive',
			});
			return;
		}

		setIsLoadingDetails(true);
		try {
			const selectedVideoList = videos.filter(v => selectedVideos.has(v.id!));
			await fetchVideoDetails(selectedVideoList, false);

			toast({
				title: '详情获取成功',
				description: `已获取 ${selectedVideos.size} 个视频的详情信息`,
			});
		} catch (error) {
			toast({
				title: '获取详情失败',
				description: '获取视频详情时出现错误，请重试',
				variant: 'destructive',
			});
		} finally {
			setIsLoadingDetails(false);
		}
	};

	const handleSearch = async () => {
		if (!playlistUrl.trim()) return;

		setIsLoading(true);
		setVideos([]);
		setVideoDetails(new Map());
		setCurrentPage(0);
		setHasMoreData(true);
		setIsShowingExampleData(false);

		try {
			// 处理播放列表URL
			const cleanUrl = playlistUrl.trim();

			// 获取播放列表中的视频
			let videosResponse = await youtubeApi.getUserVideos(cleanUrl, 1, pageSize, 'playlists');
			const videoList = videosResponse.data as YouTubeVideo[];
			setVideos(videoList);

			// 检查是否还有更多数据
			setHasMoreData(videoList.length === pageSize);

			// 清空之前的详情数据（详情将按需获取）
			setVideoDetails(new Map());

			toast({
				title: '数据获取成功',
				description: `已加载 ${videoList.length} 条播放列表视频`,
			});
		} catch (error) {
			console.error('Failed to fetch YouTube playlist videos:', error);
			toast({
				variant: 'destructive',
				title: '获取数据失败',
				description: (error as ApiError) ? error.message : '获取播放列表视频失败，请稍后重试',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleLoadMore = async () => {
		if (!playlistUrl.trim() || !hasMoreData || isLoadingMore) return;

		setIsLoadingMore(true);
		const nextPage = currentPage + 1;

		try {
			const cleanUrl = playlistUrl.trim();

			// 获取下一页播放列表视频
			const startIndex = nextPage * pageSize + 1;
			const endIndex = (nextPage + 1) * pageSize;
			let videosResponse = await youtubeApi.getUserVideos(cleanUrl, startIndex, endIndex, 'playlists');
			const newVideoList = videosResponse.data as YouTubeVideo[];

			// 检查是否还有更多数据
			setHasMoreData(newVideoList.length === pageSize);

			// 合并新数据到现有列表
			setVideos((prevVideos) => [...prevVideos, ...newVideoList]);
			setCurrentPage(nextPage);

			// 新视频的详情将按需获取

			toast({
				title: '加载成功',
				description: `已加载 ${newVideoList.length} 条新播放列表视频`,
			});
		} catch (error) {
			console.error('Failed to load more YouTube playlist videos:', error);
			toast({
				variant: 'destructive',
				title: '加载更多失败',
				description: '加载更多数据失败，请稍后重试',
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	return (
		<TooltipProvider>
			<div className="p-4 space-y-4">
			<Card className="p-6">
				<div className="space-y-4">
					<div className="flex gap-2">
						<Input
							type="text"
							placeholder="请输入YouTube播放列表链接，例如：https://www.youtube.com/playlist?list=PLua-L-o3nGJqfuiEsckS0M2KzIxLeLHHx"
							value={playlistUrl}
							onChange={(e) => setPlaylistUrl(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
							disabled={isLoading}
							className="flex-1"
						/>
						<Button onClick={handleSearch} disabled={isLoading || !playlistUrl.trim()} className="flex items-center gap-2">
							<Search className="h-4 w-4" />
							{isLoading ? '获取中...' : '获取播放列表视频'}
						</Button>
					</div>

				</div>
			</Card>

			{/* 视频表格区域 */}
			{videos.length > 0 && (
				<Card className="p-6">
					<YouTubeVideoTable
						videos={videos}
						videoDetails={videoDetails}
						loadingVideoDetails={loadingVideoDetails}
						includeDetails={true} // 默认显示详情列
						contentType="playlist"
						userInfo={null}
						isLoadingMore={isLoadingMore}
						hasMoreData={hasMoreData}
						isShowingExampleData={isShowingExampleData}
						selectedVideos={selectedVideos}
						onVideoSelect={handleVideoSelect}
						onSelectAll={handleSelectAll}
						onViewDetails={handleViewDetails}
						isLoadingDetails={isLoadingDetails}
						onDownloadVideo={videoHandler.handleDownloadVideo}
						onDownloadSubtitles={videoHandler.handleDownloadSubtitles}
						onExport={exporter.handleExport}
						onLoadMore={handleLoadMore}
						downloadDialogProps={videoHandler.downloadDialogProps}
						subtitleDialogProps={videoHandler.subtitleDialogProps}
						exportDialogProps={exporter.exportDialogProps}
					/>
				</Card>
			)}

			{/* 加载状态 */}
			{isLoading && (
				<div className="flex justify-center py-4">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
				</div>
			)}
		</div>
		</TooltipProvider>
	);
};

export default YouTubePlaylistVideos;
