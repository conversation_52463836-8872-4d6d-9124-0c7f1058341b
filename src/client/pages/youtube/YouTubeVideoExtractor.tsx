import React, { useState, useRef, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Textarea } from '../../components/ui/textarea';
import { Label } from '../../components/ui/label';
import { Upload, Search, FileText, X, Download } from 'lucide-react';
import { useToast } from '../../components/ui/use-toast';
import { ApiError, youtubeApi, YouTubeVideo } from '../../lib/api';
import YouTubeVideoTable, { YouTubeVideoDetail } from '../../components/youtube/YouTubeVideoTable';
import { useYouTubeVideoHandler } from '../../components/youtube/YouTubeVideoHandler';
import * as XLSX from 'xlsx';
import { formatTimestamp } from '../../lib/utils';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, Toolt<PERSON><PERSON>rovider, TooltipTrigger } from '../../components/ui/tooltip';

interface VideoInfo {
	url: string;
	id?: string;
	title?: string;
	duration?: number;
	channel?: {
		id: string;
		name: string;
	};
	description?: string;
	tags?: string[];
	thumbnail?: string;
	uploadDate?: string;
	viewCount?: number;
	likeCount?: number;
	status: 'pending' | 'loading' | 'success' | 'error';
	error?: string;
}

const YouTubeVideoExtractor: React.FC = () => {
	const { toast } = useToast();
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [videoUrls, setVideoUrls] = useState<string>('');
	const [videoInfos, setVideoInfos] = useState<VideoInfo[]>([]);
	const [youtubeVideos, setYoutubeVideos] = useState<YouTubeVideo[]>([]);
	const [videoDetails, setVideoDetails] = useState<Map<string, YouTubeVideoDetail>>(new Map());
	const [loadingVideoDetails, setLoadingVideoDetails] = useState<Set<string>>(new Set());
	const [isProcessing, setIsProcessing] = useState(false);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);

	// 使用通用的视频处理hook
	const videoHandler = useYouTubeVideoHandler({
		videos: youtubeVideos,
		videoDetails,
		setVideoDetails,
		loadingVideoDetails,
		setLoadingVideoDetails,
		toast,
	});

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			// 加载视频详情示例数据
			const response = await fetch('/test/data/YouTube/videoInfo.json');
			const data = await response.json();

			// 转换视频数据
			const videoData = data.data;
			const exampleVideoInfo: VideoInfo = {
				url: 'https://www.youtube.com/watch?v=' + videoData.id,
				id: videoData.id,
				title: videoData.title,
				duration: videoData.duration,
				channel: {
					id: videoData.channel_id,
					name: videoData.channel
				},
				description: videoData.description,
				tags: videoData.tags,
				thumbnail: videoData.thumbnail,
				uploadDate: videoData.upload_date,
				viewCount: videoData.view_count,
				likeCount: videoData.like_count,
				status: 'success'
			};

			// 转换为YouTubeVideo格式
			const youtubeVideo: YouTubeVideo = {
				id: videoData.id,
				title: videoData.title,
				description: videoData.description,
				thumbnails: videoData.thumbnails,
				view_count: videoData.view_count,
				like_count: videoData.like_count,
				comment_count: videoData.comment_count || 0,
				duration: videoData.duration?.toString() || '0',
				url: 'https://www.youtube.com/watch?v=' + videoData.id,
			};

			// 转换为YouTubeVideoDetail格式
			const videoDetail: YouTubeVideoDetail = {
				id: videoData.id,
				title: videoData.title,
				formats: videoData.formats || [],
				thumbnails: videoData.thumbnails || [],
				description: videoData.description || '',
				channel_id: videoData.channel_id || '',
				channel_url: videoData.channel_url || '',
				duration: videoData.duration || 0,
				view_count: videoData.view_count || 0,
				tags: videoData.tags || [],
				timestamp: videoData.timestamp || 0,
				comment_count: videoData.comment_count || 0,
				like_count: videoData.like_count || 0,
				channel_follower_count: videoData.channel_follower_count || 0,
				thumbnail: videoData.thumbnail || '',
				fulltitle: videoData.fulltitle || videoData.title || '',
				duration_string: videoData.duration_string || '',
				subtitles: videoData.subtitles,
				upload_date: videoData.upload_date || '',
				uploader: videoData.uploader
			};

			setVideoInfos([exampleVideoInfo]);
			setYoutubeVideos([youtubeVideo]);
			setVideoDetails(new Map([[videoData.id, videoDetail]]));

			toast({
				title: '示例数据已加载',
				description: '已加载 1 条示例视频数据',
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	// 解析视频链接
	const parseVideoUrls = (text: string): string[] => {
		const lines = text
			.split('\n')
			.map((line) => line.trim())
			.filter((line) => line);
		const urls: string[] = [];

		lines.forEach((line) => {
			// 检查是否包含YouTube链接
			if (line.includes('youtube.com/watch') || line.includes('youtu.be/')) {
				urls.push(line);
			}
		});

		return urls;
	};

	// 处理Excel文件上传
	const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		const reader = new FileReader();
		reader.onload = (e) => {
			try {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: 'array' });
				const sheetName = workbook.SheetNames[0];
				const worksheet = workbook.Sheets[sheetName];
				const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

				// 提取所有包含YouTube链接的单元格
				const urls: string[] = [];
				jsonData.forEach((row) => {
					row.forEach((cell) => {
						if (typeof cell === 'string' && (cell.includes('youtube.com/watch') || cell.includes('youtu.be/'))) {
							urls.push(cell);
						}
					});
				});

				if (urls.length > 0) {
					setVideoUrls(urls.join('\n'));
					toast({
						title: '文件导入成功',
						description: `从Excel文件中提取到 ${urls.length} 个YouTube视频链接`,
					});
				} else {
					toast({
						title: '未找到视频链接',
						description: 'Excel文件中没有找到有效的YouTube视频链接',
						variant: 'destructive',
					});
				}
			} catch (error) {
				console.error('Excel文件解析失败:', error);
				toast({
					title: '文件解析失败',
					description: '请确保上传的是有效的Excel文件',
					variant: 'destructive',
				});
			}
		};
		reader.readAsArrayBuffer(file);
	};

	// 获取单个视频信息
	const fetchVideoInfo = async (url: string, index: number): Promise<void> => {
		try {
			// 更新状态为加载中
			setVideoInfos((prev) => prev.map((info, i) => (i === index ? { ...info, status: 'loading' } : info)));
			
			// 同时更新loadingVideoDetails状态
			setLoadingVideoDetails((prev) => {
				const newSet = new Set(prev);
				newSet.add(`loading-${index}`);
				return newSet;
			});

			const response = await youtubeApi.getVideoInfo(url);
			const video = response.data as YouTubeVideoDetail;
			
			if (video && video.id) {
				// 移除加载状态
			setLoadingVideoDetails((prev) => {
				const newSet = new Set(prev);
				newSet.delete(`loading-${index}`);
				newSet.delete(`placeholder-${index}`);
				return newSet;
			});

				// 更新VideoInfo状态
				setVideoInfos((prev) =>
					prev.map((info, i) =>
						i === index
							? {
									...info,
									id: video.id,
									title: video.title,
									duration: video.duration,
									description: video.description,
									tags: video.tags,
									thumbnail: video.thumbnail,
									uploadDate: video.upload_date,
									viewCount: video.view_count,
									likeCount: video.like_count,
									status: 'success',
							  }
							: info
					)
				);

				// 转换为YouTubeVideo格式并立即更新表格
				const youtubeVideo: YouTubeVideo = {
					id: video.id,
					title: video.title,
					description: video.description,
					thumbnails: video.thumbnails,
					view_count: video.view_count,
					like_count: video.like_count,
					comment_count: video.comment_count,
					duration: typeof video.duration === 'number' ? video.duration.toString() : video.duration || '0',
					url: url,
				};

				// 立即更新YouTubeVideo数组，实现实时渲染
				setYoutubeVideos((prev) => {
					const newVideos = [...prev];
					newVideos[index] = youtubeVideo;
					return newVideos;
				});

				// 如果有详细信息，立即添加到videoDetails
				if (video.formats || video.subtitles) {
					const videoDetail: YouTubeVideoDetail = {
						id: video.id,
						title: video.title!,
						formats: video.formats || [],
						thumbnails: video.thumbnails || [],
						description: video.description || '',
						channel_id: video.channel_id || '',
						channel_url: video.channel_url || '',
						duration: typeof video.duration === 'string' ? parseInt(video.duration) || 0 : video.duration || 0,
						view_count: video.view_count || 0,
						tags: video.tags || [],
						timestamp: video.timestamp || 0,
						comment_count: video.comment_count || 0,
						like_count: video.like_count || 0,
						channel_follower_count: video.channel_follower_count || 0,
						thumbnail: video.thumbnail || '',
						fulltitle: video.fulltitle || video.title || '',
						duration_string: video.duration_string || '',
						subtitles: video.subtitles,
						upload_date: video.upload_date || '',
						uploader: video.uploader
					};

					setVideoDetails((prev) => {
						const newMap = new Map(prev);
						newMap.set(video.id!, videoDetail);
						return newMap;
					});
				}

				// 移除加载状态
				setLoadingVideoDetails((prev) => {
					const newSet = new Set(prev);
					// 删除可能的加载状态key
					newSet.delete(video.id!);
					newSet.delete(`placeholder-${index}`);
					newSet.delete(`loading-${index}`);
					return newSet;
				});
			} else {
				throw new Error('视频信息获取失败');
			}
		} catch (error) {
			console.error(`获取视频信息失败 (${url}):`, error);
			const errorMessage = error instanceof ApiError ? error.message : '获取视频信息失败';

			// 移除加载状态
			setLoadingVideoDetails((prev) => {
				const newSet = new Set(prev);
				// 尝试删除可能的加载状态key
				newSet.delete(`loading-${index}`);
				newSet.delete(`placeholder-${index}`);
				return newSet;
			});

			// 更新错误状态
			setVideoInfos((prev) =>
				prev.map((info, i) =>
					i === index
						? {
								...info,
								status: 'error',
								error: errorMessage,
						  }
						: info
				)
			);

			// 更新YouTubeVideo数组显示错误状态
			setYoutubeVideos((prev) => {
				const newVideos = [...prev];
				newVideos[index] = {
					...newVideos[index],
					title: `错误: ${errorMessage}`,
					description: `无法获取视频信息: ${url}`,
				};
				return newVideos;
			});
		}
	};

	// 开始提取视频信息
	const handleExtract = async () => {
		const urls = parseVideoUrls(videoUrls);

		if (urls.length === 0) {
			toast({
				title: '请输入视频链接',
				description: '请输入至少一个有效的YouTube视频链接',
				variant: 'destructive',
			});
			return;
		}

		if (urls.length > 50) {
			toast({
				title: '链接数量过多',
				description: '单次最多支持50个视频链接',
				variant: 'destructive',
			});
			return;
		}

		setIsProcessing(true);
		setIsShowingExampleData(false);

		// 初始化视频信息列表
		const initialInfos: VideoInfo[] = urls.map((url) => ({
			url,
			status: 'pending',
		}));
		setVideoInfos(initialInfos);

		// 立即初始化YouTubeVideo数组并显示占位符，实现实时渲染
		const initialYoutubeVideos: YouTubeVideo[] = urls.map((url, index) => ({
			id: `placeholder-${index}`,
			url,
			title: '正在获取视频信息...',
			description: '请稍候，正在从YouTube获取详细信息',
			thumbnails: [],
			view_count: 0,
			duration: '0',
		}));
		setYoutubeVideos(initialYoutubeVideos);
		setVideoDetails(new Map());
		setLoadingVideoDetails(new Set());

		toast({
			title: '开始提取',
			description: `正在提取 ${urls.length} 个视频的信息，请查看下方表格实时更新`,
		});

		// 并发获取视频信息，实现真正的实时更新
		const fetchPromises = urls.map((url, index) => 
			fetchVideoInfo(url, index).catch(error => {
				console.error(`视频 ${index + 1} 获取失败:`, error);
			})
		);

		// 等待所有请求完成
		await Promise.allSettled(fetchPromises);

		setIsProcessing(false);
		
		// 统计成功和失败的数量
		setTimeout(() => {
			const currentInfos = videoInfos;
			const successCount = currentInfos.filter(info => info.status === 'success').length;
			const errorCount = currentInfos.filter(info => info.status === 'error').length;
			
			toast({
				title: '提取完成',
				description: `已完成 ${urls.length} 个视频的信息提取 (成功: ${successCount}, 失败: ${errorCount})`,
			});
		}, 100);
	};

	// 清空结果
	const handleClear = () => {
		setVideoUrls('');
		setVideoInfos([]);
		setYoutubeVideos([]);
		setVideoDetails(new Map());
		setLoadingVideoDetails(new Set());
	};



	// 导出表格数据
	const handleExportTableData = () => {
		if (isShowingExampleData) {
			toast({
				title: '示例数据不支持导出',
				description: '请输入真实的视频链接进行提取后再导出',
				variant: 'destructive',
			});
			return;
		}

		// 过滤掉占位符和无效数据
		const validVideos = youtubeVideos.filter(video =>
			video.id &&
			!video.id.startsWith('placeholder-') &&
			video.title !== '正在获取视频信息...' &&
			!video.title?.startsWith('错误:')
		);

		if (validVideos.length === 0) {
			toast({
				title: '没有有效数据可导出',
				description: '请先成功提取视频信息后再导出',
				variant: 'destructive',
			});
			return;
		}

		const exportData = validVideos.map((video) => {
			const detail = videoDetails.get(video.id || '');
			return {
				视频ID: video.id || '',
				标题: video.title || '',
				描述: (video.description || '').replace(/\n/g, ' ').substring(0, 500), // 限制长度并移除换行
				时长: video.duration || '',
				时长秒数: detail?.duration || 0,
				观看次数: video.view_count || 0,
				点赞数: video.like_count || 0,
				评论数: video.comment_count || 0,
				频道ID: detail?.channel_id || '',
				频道关注者数: detail?.channel_follower_count || 0,
				上传者: detail?.uploader || '',
				上传日期: detail?.upload_date || '',
				标签: Array.isArray(detail?.tags) ? detail.tags.join(', ') : Array.isArray(video.tags) ? video.tags.join(', ') : '',
				视频链接: video.url,
				缩略图: video.thumbnails?.[0]?.url || detail?.thumbnail || '',
				发布时间: formatTimestamp(detail?.timestamp) || '-',
			};
		});

		const ws = XLSX.utils.json_to_sheet(exportData);
		const wb = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(wb, ws, 'YouTube视频详细信息');


		XLSX.writeFile(wb, `YouTube视频详细信息_${new Date().toISOString().slice(0, 10)}.xlsx`);

		toast({
			title: '导出成功',
			description: `已导出 ${validVideos.length} 个视频的详细信息到Excel文件`,
		});
	};

	// 移除单个视频
	const removeVideo = (index: number) => {
		setVideoInfos((prev) => prev.filter((_, i) => i !== index));
	};

	// 下载模板
	const handleDownloadTemplate = () => {
		const templateData = [
			{
				视频链接: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
				备注: '请在此列填入YouTube视频链接，每行一个',
			},
			{
				视频链接: 'https://youtu.be/dQw4w9WgXcQ',
				备注: '支持完整链接和短链接格式',
			},
			{
				视频链接: '',
				备注: '可以删除示例数据，填入您的视频链接',
			},
		];

		const ws = XLSX.utils.json_to_sheet(templateData);
		const wb = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(wb, ws, 'YouTube视频链接模板');

		// 设置列宽
		ws['!cols'] = [
			{ wch: 50 }, // 视频链接列宽度
			{ wch: 30 }, // 备注列宽度
		];

		XLSX.writeFile(wb, 'YouTube视频链接导入模板.xlsx');

		toast({
			title: '模板下载成功',
			description: '请在模板中填入视频链接后重新上传',
		});
	};

	// 导出结果
	const handleExport = () => {
		if (videoInfos.length === 0) {
			toast({
				title: '没有数据可导出',
				description: '请先提取视频信息',
				variant: 'destructive',
			});
			return;
		}

		const exportData = videoInfos.map((info) => ({
			视频链接: info.url,
			视频ID: info.id || '',
			标题: info.title || '',
			频道名称: info.channel?.name || '',
			频道ID: info.channel?.id || '',
			'时长(秒)': info.duration || '',
			上传日期: info.uploadDate || '',
			观看次数: info.viewCount || '',
			点赞数: info.likeCount || '',
			标签: info.tags?.join(', ') || '',
			状态: info.status === 'success' ? '成功' : info.status === 'error' ? '失败' : '处理中',
			错误信息: info.error || '',
		}));

		const ws = XLSX.utils.json_to_sheet(exportData);
		const wb = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(wb, ws, 'YouTube视频信息');
		XLSX.writeFile(wb, `YouTube视频信息_${new Date().toISOString().slice(0, 10)}.xlsx`);

		toast({
			title: '导出成功',
			description: '视频信息已导出到Excel文件',
		});
	};

	return (
		<TooltipProvider>
			<div className="space-y-6">
			{/* 输入区域 */}
			<Card className="p-6">
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<Label htmlFor="video-urls" className="text-base font-medium">
							视频链接输入
						</Label>
						<div className="flex gap-2">
							<Button variant="outline" size="sm" onClick={handleDownloadTemplate} disabled={isProcessing}>
								<Download className="h-4 w-4 mr-2" />
								下载模板
							</Button>
							<Button variant="outline" size="sm" onClick={() => fileInputRef.current?.click()} disabled={isProcessing}>
								<Upload className="h-4 w-4 mr-2" />
								导入Excel
							</Button>
							<input ref={fileInputRef} type="file" accept=".xlsx,.xls" onChange={handleFileUpload} className="hidden" />
						</div>
					</div>

					<Textarea
						id="video-urls"
						placeholder={`请输入YouTube视频链接，每行一个...

示例：
https://www.youtube.com/watch?v=dQw4w9WgXcQ
https://youtu.be/dQw4w9WgXcQ`}
						value={videoUrls}
						onChange={(e) => setVideoUrls(e.target.value)}
						className="min-h-[120px]"
						disabled={isProcessing}
					/>

					<div className="flex justify-end mt-4 gap-2">
						<Button onClick={handleExtract} disabled={isProcessing || !videoUrls.trim()}>
							<Search className="w-4 h-4 mr-2" />
							{isProcessing ? '提取中...' : '提取'}
						</Button>
						<Button variant="outline" onClick={handleClear} disabled={isProcessing || (!videoUrls.trim() && youtubeVideos.length === 0)}>
							清空
						</Button>
					</div>
				</div>
			</Card>

			{/* 渲染视频详情表格 - 实时更新显示 */}
			{youtubeVideos.length > 0 && (
				<YouTubeVideoTable
					videos={youtubeVideos.filter(video =>
						video.id &&
						!video.id.startsWith('placeholder-') &&
						video.title !== '正在获取视频信息...'
					)}
					videoDetails={videoDetails}
					loadingVideoDetails={loadingVideoDetails}
					onDownloadVideo={videoHandler.handleDownloadVideo}
					onDownloadSubtitles={videoHandler.handleDownloadSubtitles}
					onExport={() => handleExportTableData()}
					userInfo={null}
					includeDetails={true}
					isShowingExampleData={isShowingExampleData}
					downloadDialogProps={videoHandler.downloadDialogProps}
					subtitleDialogProps={videoHandler.subtitleDialogProps}
				/>
			)}
			
			{/* 显示处理中的视频占位符 */}
			{isProcessing && youtubeVideos.some(video => video.id?.startsWith('placeholder-')) && (
				<Card className="p-4">
					<div className="text-sm text-gray-600">
						<div className="flex items-center gap-2 mb-2">
							<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
							正在实时获取视频信息...
						</div>
						<div className="text-xs">
							已处理: {youtubeVideos.filter(v => v.id && !v.id.startsWith('placeholder-')).length} / {youtubeVideos.length}
						</div>
					</div>
				</Card>
			)}
		</div>
	</TooltipProvider>
);
};

export default YouTubeVideoExtractor;
