import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Search } from 'lucide-react';
import YouTubeVideoTable, { YouTubeVideoDetail } from '../../components/youtube/YouTubeVideoTable';
import { useYouTubeVideoHandler } from '../../components/youtube/YouTubeVideoHandler';
import { useYouTubeVideoExporter } from '../../components/youtube/YouTubeVideoExporter';
import { useYouTubeVideoDetails } from '../../components/youtube/useYouTubeVideoDetails';
import { ApiError, youtubeApi, YouTubeVideo, YouTubeUserInfo } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import { TooltipProvider } from '../../components/ui/tooltip';

import { ExampleImageSVG } from '../../components/ui/example-image';

const YouTubeUserVideos: React.FC = () => {
	const { toast } = useToast();
	const [userUrl, setUserUrl] = useState('');
	const [userInfo, setUserInfo] = useState<YouTubeUserInfo | null>(null);
	const [videos, setVideos] = useState<YouTubeVideo[]>([]);
	const [videoDetails, setVideoDetails] = useState<Map<string, YouTubeVideoDetail>>(new Map());
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [contentType, setContentType] = useState<'videos' | 'shorts'>('videos');
	const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());
	const [isLoadingDetails, setIsLoadingDetails] = useState(false);
	const [currentPage, setCurrentPage] = useState(0);
	const [hasMoreData, setHasMoreData] = useState(true);
	const [pageSize] = useState(20);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);

	// 跟踪正在加载详情的视频
	const [loadingVideoDetails, setLoadingVideoDetails] = useState<Set<string>>(new Set());

	// 使用通用的视频详情处理hook
	const { fetchVideoDetails } = useYouTubeVideoDetails({
		videos,
		videoDetails,
		setVideoDetails,
		includeDetails: false, // 不再自动获取详情，改为按需获取
	});

	// 使用通用的视频处理hook
	const videoHandler = useYouTubeVideoHandler({
		videos,
		videoDetails,
		setVideoDetails,
		loadingVideoDetails,
		setLoadingVideoDetails,
		toast,
	});

	// 使用通用的导出处理hook
	const exporter = useYouTubeVideoExporter({
		videos,
		videoDetails,
		setVideoDetails,
		setVideos,
		currentPage,
		setCurrentPage,
		hasMoreData,
		setHasMoreData,
		pageSize,
		toast,
		userInfo,
		contentType,
		userUrl,
		youtubeApi,
	});

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			// 加载用户信息示例数据
			const userResponse = await fetch('/test/data/YouTube/user.json');
			const userData = await userResponse.json();

			// 加载视频列表示例数据
			const videosResponse = await fetch('/test/data/YouTube/userVideos.json');
			const videosData = await videosResponse.json();

			// 转换用户信息数据
			const convertedUserInfo: YouTubeUserInfo = {
				id: userData.data.metadata.externalId,
				name: userData.data.metadata.title,
				description: userData.data.metadata.description,
				thumbnail: userData.data.metadata.avatar.thumbnails[0]?.url,
				subscriberCount: parseInt(userData.data.channel_follower_count.replace(/[^\d]/g, '')) || 0,
				videoCount: parseInt(userData.data.videos_count.replace(/[^\d]/g, '')) || 0,
			};

			// 转换视频数据
			const convertedVideos: YouTubeVideo[] = videosData.data.entries.map((video: any) => ({
				id: video.id,
				title: video.title,
				description: video.description,
				thumbnail: video.thumbnail || '',
				duration: video.duration,
				uploadDate: video.upload_date || '',
				viewCount: video.view_count,
				url: video.url,
			}));

			setUserInfo(convertedUserInfo);
			setVideos(convertedVideos);
			setHasMoreData(false);

			toast({
				title: '示例数据已加载',
				description: `已加载 ${convertedVideos.length} 条示例视频数据`,
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	// 处理视频选择
	const handleVideoSelect = (videoId: string, selected: boolean) => {
		setSelectedVideos(prev => {
			const newSet = new Set(prev);
			if (selected) {
				newSet.add(videoId);
			} else {
				newSet.delete(videoId);
			}
			return newSet;
		});
	};

	// 处理全选/取消全选
	const handleSelectAll = (selected: boolean) => {
		if (selected) {
			setSelectedVideos(new Set(videos.map(v => v.id!)));
		} else {
			setSelectedVideos(new Set());
		}
	};

	// 查看选中视频的详情
	const handleViewDetails = async () => {
		if (selectedVideos.size === 0) {
			toast({
				title: '请选择视频',
				description: '请先选择要查看详情的视频',
				variant: 'destructive',
			});
			return;
		}

		setIsLoadingDetails(true);
		try {
			const selectedVideoList = videos.filter(v => selectedVideos.has(v.id!));
			await fetchVideoDetails(selectedVideoList, false);

			toast({
				title: '详情获取成功',
				description: `已获取 ${selectedVideos.size} 个视频的详情信息`,
			});
		} catch (error) {
			toast({
				title: '获取详情失败',
				description: '获取视频详情时出现错误，请重试',
				variant: 'destructive',
			});
		} finally {
			setIsLoadingDetails(false);
		}
	};

	const handleSearch = async () => {
		if (!userUrl.trim()) return;

		setIsLoading(true);
		setVideos([]);
		setUserInfo(null);
		setVideoDetails(new Map());
		setCurrentPage(0);
		setHasMoreData(true);
		setIsShowingExampleData(false);

		try {
			// 处理URL，移除/videos等路径后缀
			const cleanUrl = userUrl.trim().replace(/\/(videos|shorts|playlists|community|channels|about)\/?$/, '');

			// 并行获取用户信息和视频列表，分别处理以便及时渲染
			const userInfoPromise = youtubeApi.getUserInfo(cleanUrl).then((response) => {
				setUserInfo(response.data as YouTubeUserInfo);
				return response;
			});

			const videosPromise = youtubeApi.getUserVideos(cleanUrl + '/' + contentType, 1, pageSize, contentType).then((response) => {
				const videoList = response.data as YouTubeVideo[];
				setVideos(videoList);
				return videoList;
			});

			// 等待视频列表获取完成以便后续处理
			const videoList = await videosPromise;
			// 确保用户信息也已获取完成
			await userInfoPromise;

			// 检查是否还有更多数据
			setHasMoreData(videoList.length === pageSize);

			// 清空之前的详情数据（详情将按需获取）
			setVideoDetails(new Map());

			toast({
				title: '数据获取成功',
				description: `已加载 ${videoList.length} 条${contentType === 'videos' ? '视频' : 'Shorts'}`,
			});
		} catch (error) {
			console.error('Failed to fetch YouTube user videos:', error);
			toast({
				variant: 'destructive',
				title: '获取数据失败',
				description: (error as ApiError) ? error.message : `获取用户${contentType === 'videos' ? '视频' : 'Shorts'}失败，请稍后重试`,
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleLoadMore = async () => {
		if (!userUrl.trim() || !hasMoreData || isLoadingMore) return;

		setIsLoadingMore(true);
		const nextPage = currentPage + 1;

		try {
			// 处理URL，移除/videos等路径后缀
			const cleanUrl = userUrl.trim().replace(/\/(videos|shorts|playlists|community|channels|about)\/?$/, '');

			// 根据选择的内容类型获取下一页视频或shorts列表
			const startIndex = nextPage * pageSize + 1;
			const endIndex = (nextPage + 1) * pageSize;
			let videosResponse = await youtubeApi.getUserVideos(cleanUrl + '/' + contentType, startIndex, endIndex, contentType);
			const newVideoList = videosResponse.data as YouTubeVideo[];

			// 检查是否还有更多数据
			setHasMoreData(newVideoList.length === pageSize);

			// 合并新数据到现有列表
			setVideos((prevVideos) => [...prevVideos, ...newVideoList]);
			setCurrentPage(nextPage);

			// 新视频的详情将按需获取

			toast({
				title: '加载成功',
				description: `已加载 ${newVideoList.length} 条新${contentType === 'videos' ? '视频' : 'Shorts'}`,
			});
		} catch (error) {
			console.error('Failed to load more YouTube videos:', error);
			toast({
				variant: 'destructive',
				title: '加载更多失败',
				description: '加载更多数据失败，请稍后重试',
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	return (
		<TooltipProvider>
			<div className="p-4 space-y-4">
			<Card className="p-6">
				<div className="space-y-4">
					<div className="flex gap-2">
						<Input
							placeholder="请输入YouTube频道链接或频道ID，例如：https://www.youtube.com/@channelname 或 UCxxxxx"
							value={userUrl}
							onChange={(e) => setUserUrl(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
							className="flex-1"
						/>
						<Select value={contentType} onValueChange={(value: 'videos' | 'shorts') => setContentType(value)}>
							<SelectTrigger className="w-[120px]">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="videos">视频</SelectItem>
								<SelectItem value="shorts">Shorts</SelectItem>
							</SelectContent>
						</Select>
						<Button onClick={handleSearch} disabled={isLoading}>
							<Search className="w-4 h-4 mr-2" />
							{isLoading ? '搜索中...' : '搜索'}
						</Button>
					</div>


				</div>

				{userInfo && (
					<div className="mt-4 p-4 bg-gray-50 rounded-lg">
						<div className="flex items-center space-x-4">
							{isShowingExampleData ? (
								<ExampleImageSVG width={64} height={64} type="avatar" />
							) : (
								<img
									src={userInfo.thumbnail || '/default-avatar.png'}
									alt={userInfo.name || 'YouTube频道'}
									className="w-16 h-16 rounded-full"
									referrerPolicy="no-referrer"
								/>
							)}
							<div className="flex-1">
								<div className="flex items-center gap-2">
									<h3 className="text-lg font-semibold">{userInfo.name || '未知频道'}</h3>
									{isShowingExampleData && (
										<span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
											示例数据
										</span>
									)}
								</div>
								<p className="text-sm text-gray-500 max-w-2xl line-clamp-2">{userInfo.description || '暂无描述'}</p>
								<div className="flex space-x-4 text-sm text-gray-600 mt-1">
									<span>订阅者: {userInfo.subscriberCount || '0'}</span>
									<span>视频数: {userInfo.videoCount || '0'}</span>
								</div>
							</div>
						</div>
					</div>
				)}
			</Card>

			{/* 视频表格区域 */}
			{videos.length > 0 && (
				<Card className="p-6">
					<YouTubeVideoTable
						videos={videos}
						videoDetails={videoDetails}
						loadingVideoDetails={loadingVideoDetails}
						includeDetails={true} // 默认显示详情列
						contentType={contentType}
						isShowingExampleData={isShowingExampleData}
						selectedVideos={selectedVideos}
						onVideoSelect={handleVideoSelect}
						onSelectAll={handleSelectAll}
						onViewDetails={handleViewDetails}
						isLoadingDetails={isLoadingDetails}
						onDownloadVideo={videoHandler.handleDownloadVideo}
						onDownloadSubtitles={videoHandler.handleDownloadSubtitles}
						onExport={exporter.handleExport}
						hasMoreData={hasMoreData}
						isLoadingMore={isLoadingMore}
						onLoadMore={handleLoadMore}
						downloadDialogProps={videoHandler.downloadDialogProps}
						subtitleDialogProps={videoHandler.subtitleDialogProps}
						exportDialogProps={exporter.exportDialogProps}
					/>
				</Card>
			)}

			{/* 加载状态 */}
			{isLoading && (
				<div className="flex justify-center py-4">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
				</div>
			)}
		</div>
	</TooltipProvider>
);
};

export default YouTubeUserVideos;
