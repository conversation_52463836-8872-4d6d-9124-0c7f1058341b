import React, { useState } from 'react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Textarea } from '../components/ui/textarea';
import { useToast } from '../components/ui/use-toast';
import { Loader2, Download, Globe } from 'lucide-react';
import { Checkbox } from '../components/ui/checkbox';

interface RenderOptions {
  // PDF选项
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
  landscape?: boolean;
  printBackground?: boolean;
  margin?: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
  // HTML选项
  includeStyles?: boolean;
  cleanContent?: boolean;
  // Markdown选项
  headingStyle?: 'atx' | 'setext';
  codeBlockStyle?: 'fenced' | 'indented';

  // 通用选项
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle0' | 'networkidle2';
  timeout?: number;
  headers?: Record<string, string>;
}

interface RenderRequest {
  url: string;
  renderType: 'pdf' | 'html' | 'markdown';
  options: RenderOptions;
}

const BrowserRenderer: React.FC = () => {
  const [url, setUrl] = useState('');
  const [renderType, setRenderType] = useState<'pdf' | 'html' | 'markdown'>('pdf');
  const [options, setOptions] = useState<RenderOptions>({
    format: 'A4',
    landscape: false,
    printBackground: true,
    includeStyles: true,
    cleanContent: false,
    headingStyle: 'atx',
    codeBlockStyle: 'fenced',
    waitUntil: 'domcontentloaded',
    timeout: 30000,
  });
  const [isLoading, setIsLoading] = useState(false);
  // 默认使用Chrome Windows请求头
  const defaultHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
  };
  const { toast } = useToast();

  const handleRender = async () => {
    if (!url.trim()) {
      toast({
        title: '错误',
        description: '请输入要渲染的网页URL',
        variant: 'destructive',
      });
      return;
    }



    setIsLoading(true);

    try {
      // 使用默认Chrome Windows请求头
      const headers = defaultHeaders;

      const requestData: RenderRequest = {
        url: url.trim(),
        renderType,
        options: {
          ...options,
          headers: headers,
        },
      };

      const response = await fetch('/api/browser/render', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '渲染失败');
      }

      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `output.${renderType}`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/i);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // 下载文件
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      toast({
        title: '成功',
        description: `${renderType.toUpperCase()} 文件已生成并开始下载`,
        variant: 'success',
      });
    } catch (error) {
      console.error('渲染失败:', error);
      toast({
        title: '渲染失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateOption = <K extends keyof RenderOptions>(key: K, value: RenderOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const updateMargin = (side: keyof NonNullable<RenderOptions['margin']>, value: string) => {
    setOptions(prev => ({
      ...prev,
      margin: {
        ...prev.margin,
        [side]: value,
      },
    }));
  };



  return (
    <div className="p-4 space-y-4">
      <Card className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2 flex items-center gap-2">
            <Globe className="h-6 w-6" />
            网页内容提取器
          </h1>
          <p className="text-muted-foreground">
            将任何网页转换为多种格式，支持批量下载
          </p>
        </div>

        <div className="flex gap-2 mb-6">
          <Input
            placeholder="请输入网页URL，例如：https://example.com"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleRender()}
          />
          <Button 
            onClick={handleRender} 
            disabled={isLoading || !url.trim()}
            className="whitespace-nowrap"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                渲染中...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                开始渲染
              </>
            )}
          </Button>
        </div>

        {/* 输出格式选择 */}
        <div className="mb-6">
          <Label className="text-base font-medium mb-3 block">选择输出格式</Label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer ${
              renderType === 'pdf' ? 'border-blue-500 bg-blue-50' : ''
            }`} onClick={() => setRenderType('pdf')}>
              <input
                type="radio"
                id="pdf"
                name="renderType"
                checked={renderType === 'pdf'}
                onChange={() => setRenderType('pdf')}
                className="text-blue-600"
              />
              <Label htmlFor="pdf" className="cursor-pointer">
                <div>
                  <div className="font-medium">PDF</div>
                  <div className="text-xs text-gray-500">便携式文档</div>
                </div>
              </Label>
            </div>
            
            <div className={`flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer ${
              renderType === 'html' ? 'border-blue-500 bg-blue-50' : ''
            }`} onClick={() => setRenderType('html')}>
              <input
                type="radio"
                id="html"
                name="renderType"
                checked={renderType === 'html'}
                onChange={() => setRenderType('html')}
                className="text-blue-600"
              />
              <Label htmlFor="html" className="cursor-pointer">
                <div>
                  <div className="font-medium">HTML</div>
                  <div className="text-xs text-gray-500">网页源码</div>
                </div>
              </Label>
            </div>
            
            <div className={`flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer ${
              renderType === 'markdown' ? 'border-blue-500 bg-blue-50' : ''
            }`} onClick={() => setRenderType('markdown')}>
              <input
                type="radio"
                id="markdown"
                name="renderType"
                checked={renderType === 'markdown'}
                onChange={() => setRenderType('markdown')}
                className="text-blue-600"
              />
              <Label htmlFor="markdown" className="cursor-pointer">
                <div>
                  <div className="font-medium">Markdown</div>
                  <div className="text-xs text-gray-500">标记语言</div>
                </div>
              </Label>
            </div>
          </div>
        </div>

        {/* 高级选项 */}
        <div className="space-y-4">
          <Label className="text-base font-medium">高级选项</Label>
          
          {/* PDF选项 */}
          {renderType === 'pdf' && (
              <Card className="p-4">
                <h3 className="font-medium mb-3">PDF 选项</h3>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="format">页面格式</Label>
                    <Select value={options.format} onValueChange={(value: any) => updateOption('format', value)}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="A4">A4</SelectItem>
                        <SelectItem value="A3">A3</SelectItem>
                        <SelectItem value="A5">A5</SelectItem>
                        <SelectItem value="Letter">Letter</SelectItem>
                        <SelectItem value="Legal">Legal</SelectItem>
                        <SelectItem value="Tabloid">Tabloid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="landscape"
                        checked={options.landscape}
                        onCheckedChange={(checked) => updateOption('landscape', !!checked)}
                      />
                      <Label htmlFor="landscape">横向布局</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="printBackground"
                        checked={options.printBackground}
                        onCheckedChange={(checked) => updateOption('printBackground', !!checked)}
                      />
                      <Label htmlFor="printBackground">打印背景</Label>
                    </div>
                  </div>
                </div>
              </Card>
            )}

          {/* HTML选项 */}
          {renderType === 'html' && (
              <Card className="p-4">
                <h3 className="font-medium mb-3">HTML 选项</h3>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeStyles"
                      checked={options.includeStyles}
                      onCheckedChange={(checked) => updateOption('includeStyles', !!checked)}
                    />
                    <Label htmlFor="includeStyles">包含样式</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="cleanContent"
                      checked={options.cleanContent}
                      onCheckedChange={(checked) => updateOption('cleanContent', !!checked)}
                    />
                    <Label htmlFor="cleanContent">清理内容</Label>
                  </div>
                </div>
              </Card>
            )}

          {/* Markdown选项 */}
           {renderType === 'markdown' && (
             <Card className="p-4">
               <h3 className="font-medium mb-3">Markdown 选项</h3>
               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                 <div>
                   <Label htmlFor="headingStyle">标题样式</Label>
                   <Select value={options.headingStyle} onValueChange={(value: any) => updateOption('headingStyle', value)}>
                     <SelectTrigger className="mt-1">
                       <SelectValue />
                     </SelectTrigger>
                     <SelectContent>
                       <SelectItem value="atx">ATX (# 标题)</SelectItem>
                       <SelectItem value="setext">Setext (下划线)</SelectItem>
                     </SelectContent>
                   </Select>
                 </div>
                 
                 <div>
                   <Label htmlFor="codeBlockStyle">代码块样式</Label>
                   <Select value={options.codeBlockStyle} onValueChange={(value: any) => updateOption('codeBlockStyle', value)}>
                     <SelectTrigger className="mt-1">
                       <SelectValue />
                     </SelectTrigger>
                     <SelectContent>
                       <SelectItem value="fenced">围栏式 (```)</SelectItem>
                       <SelectItem value="indented">缩进式</SelectItem>
                     </SelectContent>
                   </Select>
                 </div>
               </div>
             </Card>
           )}


        </div>
      </Card>
    </div>
  );
};

export default BrowserRenderer;