import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Download, Video, Subtitles } from 'lucide-react';
import { ApiError, apiRequest } from '../../lib/api';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { exportArrayToCSV } from '../../lib/utils';
import { useToast } from '../../components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';
import MediaDownloadDialog, { DownloadOption } from '../../components/MediaDownloadDialog';
import SubtitleDownloadDialog from '../../components/SubtitleDownloadDialog';
import { buildProxyUrl } from '../../lib/buildPorxy';

interface TikTokVideo {
	id: string;
	desc: string;
	createTime: number;
	video: {
		id: string;
		height: number;
		width: number;
		duration: number;
		ratio: string;
		cover: string;
		originCover: string;
		dynamicCover: string;
		playAddr: string;
		downloadAddr: string;
		shareCover: string[];
		reflowCover: string;
		bitrate: number;
		encodedType: string;
		format: string;
		videoQuality: string;
		encodeUserTag: string;
		codecType: string;
		definition: string;
		PlayAddrStruct: {
			DataSize: number;
			Height: number;
			Width: number;
			UrlList: string[];
		};
		bitrateInfo: Array<{
			Format: string;
			PlayAddr: {
				DataSize: number;
				Height: number;
				Width: number;
				UrlList: string[];
			};
		}>;
		subtitleInfos: Array<{
			LanguageID: string;
			LanguageCodeName: string;
			Url: string;
			UrlExpire: number;
			Format: string;
			Version: string;
			Source: string;
			VideoSubtitleID: number;
			Size: number;
		}>;
	};
	author: {
		id: string;
		shortId: string;
		uniqueId: string;
		nickname: string;
		avatarLarger: string;
		avatarMedium: string;
		avatarThumb: string;
		signature: string;
		createTime: number;
		verified: boolean;
		secUid: string;
		ftc: boolean;
		relation: number;
		openFavorite: boolean;
		commentSetting: number;
		duetSetting: number;
		stitchSetting: number;
		privateAccount: boolean;
		secret: boolean;
		isADVirtual: boolean;
		roomId: string;
	};
	music: {
		id: string;
		title: string;
		playUrl: string;
		coverLarge: string;
		coverMedium: string;
		coverThumb: string;
		authorName: string;
		original: boolean;
		duration: number;
		album: string;
	};
	challenges: Array<{
		id: string;
		title: string;
		desc: string;
		profileLarger: string;
		profileMedium: string;
		profileThumb: string;
		coverLarger: string;
		coverMedium: string;
		coverThumb: string;
		isCommerce: boolean;
	}>;
	stats: {
		diggCount: number;
		shareCount: number;
		commentCount: number;
		playCount: number;
		collectCount: number;
	};
	duetInfo: {
		duetFromId: string;
	};
	originalItem: boolean;
	officalItem: boolean;
	textExtra: Array<{
		awemeId: string;
		start: number;
		end: number;
		hashtagName: string;
		hashtagId: string;
		type: number;
		userId: string;
		isCommerce: boolean;
		userUniqueId: string;
		secUid: string;
	}>;
	secret: boolean;
	forFriend: boolean;
	digged: boolean;
	itemCommentStatus: number;
	showNotPass: boolean;
	vl1: boolean;
	itemMute: boolean;
	embedsDisabled: boolean;
	shareEnabled: boolean;
	comments: any[];
	duetEnabled: boolean;
	stitchEnabled: boolean;
	shareInfo: {
		shareUrl: string;
		shareTitleMyself: string;
		shareTitleOther: string;
		shareDesc: string;
		shareQuote: string;
	};
}

interface TikTokUserInfo {
	userInfo: {
		user: {
			id: string;
			shortId: string;
			uniqueId: string;
			nickname: string;
			avatarLarger: string;
			avatarMedium: string;
			avatarThumb: string;
			signature: string;
			createTime: number;
			verified: boolean;
			secUid: string;
			ftc: boolean;
			relation: number;
			openFavorite: boolean;
			commentSetting: number;
			duetSetting: number;
			stitchSetting: number;
			privateAccount: boolean;
			secret: boolean;
			isADVirtual: boolean;
			roomId: string;
		};
		stats: {
			followingCount: number;
			followerCount: number;
			heartCount: number;
			videoCount: number;
			diggCount: number;
			heart: number;
		};
	};
}

interface TikTokUserVideosResponse {
	itemList: TikTokVideo[];
	hasMorePrevious: boolean;
}

const TikTokUserVideos: React.FC = () => {
	const [userUrl, setUserUrl] = useState('');
	const [userInfo, setUserInfo] = useState<TikTokUserInfo['userInfo'] | null>(null);
	const [videos, setVideos] = useState<TikTokVideo[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [hasMore, setHasMore] = useState(false);
	const [cursor, setCursor] = useState<number>(0);
	const [selectedVideo, setSelectedVideo] = useState<TikTokVideo | null>(null);
	const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false);
	const [isSubtitleDialogOpen, setIsSubtitleDialogOpen] = useState(false);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);
	const { toast } = useToast();

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			// 并行加载视频数据和用户数据
			const [videosResponse, userResponse] = await Promise.all([
				fetch('/test/data/tiktok/userPost.json'),
				fetch('/test/data/tiktok/user.json'),
			]);

			const videosData = await videosResponse.json();
			const userData = await userResponse.json();

			// 直接断言数据类型，避免大量赋值代码
			const convertedVideos: TikTokVideo[] = videosData.itemList as TikTokVideo[];
			const convertedUserInfo: TikTokUserInfo['userInfo'] = userData.data.userInfo as TikTokUserInfo['userInfo'];

			setVideos(convertedVideos);
			setUserInfo(convertedUserInfo);
			setHasMore(false);

			toast({
				title: '示例数据已加载',
				description: `已加载 ${convertedVideos.length} 条示例视频数据`,
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	const extractUserId = (url: string): string => {
		// Extract user ID from TikTok URL like https://www.tiktok.com/@username
		const match = url.match(/@([^/?]+)/);
		return match ? match[1] : url.trim();
	};

	const handleSearch = async () => {
		if (!userUrl.trim()) return;

		const extractedUserId = extractUserId(userUrl);
		if (!extractedUserId) {
			toast({
				variant: 'destructive',
				title: '无效的用户链接',
				description: '请输入正确的TikTok用户主页链接或用户名',
			});
			return;
		}

		setIsLoading(true);
		setVideos([]);
		setUserInfo(null);
		setCursor(0);
		setIsShowingExampleData(false);

		try {
			// 第一步：获取用户信息
			const userInfoResponse = await apiRequest<TikTokUserInfo>('/tiktok/user/info', {
				method: 'POST',
				body: JSON.stringify({ url: extractedUserId }),
			});

			if (!userInfoResponse.data?.userInfo?.user?.secUid) {
				throw new Error('无法获取用户的secUid');
			}

			setUserInfo(userInfoResponse.data.userInfo);

			// 第二步：使用secUid获取用户作品
			const videosResponse = await apiRequest<TikTokUserVideosResponse>('/tiktok/user/posts', {
				method: 'POST',
				body: JSON.stringify({
					secUserId: userInfoResponse.data.userInfo.user.secUid,
					count: 15,
				}),
			});

			const videoList = videosResponse.data?.itemList || [];
			setVideos(videoList);
			setHasMore(videosResponse.data?.hasMorePrevious || false);
			setCursor(videoList.length > 0 ? videoList[videoList.length - 1].createTime : 0);

			if (videoList.length > 0) {
				toast({
					title: '数据获取成功',
					description: `已加载 ${videoList.length} 条视频`,
				});
			} else {
				toast({
					title: '未找到数据',
					description: '该用户暂无视频或数据不可见',
				});
			}
		} catch (error) {
			console.error('Failed to fetch TikTok user videos:', error);
			toast({
				variant: 'destructive',
				title: '获取数据失败',
				description: (error as ApiError) ? error.message : '获取用户视频失败，请稍后重试',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleLoadMore = async () => {
		console.info(cursor);
		console.info(!userInfo?.user?.secUid);
		console.info(!hasMore);
		if (isLoadingMore || !hasMore || !userInfo?.user?.secUid || cursor === 0) return;

		setIsLoadingMore(true);
		try {
			const videosResponse = await apiRequest<TikTokUserVideosResponse>('/tiktok/user/posts', {
				method: 'POST',
				body: JSON.stringify({
					secUserId: userInfo.user.secUid,
					cursor: cursor.toString(),
					count: 15,
				}),
			});

			const moreVideos = videosResponse.data?.itemList || [];
			const uniqueItems = moreVideos.filter((item) => !videos.some((video) => video.id === item.id));

			setVideos([...videos, ...uniqueItems]);
			setHasMore(videosResponse.data?.hasMorePrevious || false);
			setCursor(uniqueItems.length > 0 ? uniqueItems[uniqueItems.length - 1].createTime : cursor);

			if (uniqueItems.length > 0) {
				toast({
					title: '加载成功',
					description: `已加载额外 ${uniqueItems.length} 条视频`,
				});
			}
		} catch (error) {
			console.error('Failed to load more videos:', error);
			toast({
				variant: 'destructive',
				title: '加载更多数据失败',
				description: (error as ApiError) ? error.message : '获取更多视频失败，请稍后重试',
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	const handleExport = () => {
		if (videos.length === 0) return;

		const rows = [
			['视频ID', '作者', '描述', '播放量', '点赞', '评论', '分享', '收藏', '时长(秒)', '创建时间', '视频链接', '封面链接', '博主链接'],
			...videos.map((video) => [
				video.id,
				video.author.nickname,
				video.desc,
				video.stats.playCount,
				video.stats.diggCount,
				video.stats.commentCount,
				video.stats.shareCount,
				video.stats.collectCount,
				video.video.duration,
				new Date(video.createTime * 1000).toLocaleString(),
				`https://www.tiktok.com/@${video.author.uniqueId}/video/${video.id}?is_from_webapp=1`,
				video.video.cover,
				`https://www.tiktok.com/@${video.author.uniqueId}`,
			]),
		];

		exportArrayToCSV(rows, `TikTok用户作品_${userInfo?.user?.nickname || 'unknown'}_${new Date().toLocaleString()}.csv`);

		toast({
			title: '导出成功',
			description: `已导出 ${videos.length} 条视频数据`,
		});
	};

	const formatDuration = (seconds: number): string => {
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
	};

	// 处理视频下载
	const handleVideoDownload = (video: TikTokVideo) => {
		setSelectedVideo(video);
		setIsMediaDialogOpen(true);
	};

	// 处理字幕下载
	const handleSubtitleDownload = (video: TikTokVideo) => {
		setSelectedVideo(video);
		setIsSubtitleDialogOpen(true);
	};

	// 转换TikTok视频数据为MediaDownloadDialog所需的格式
	const convertToVideoDetail = (video: TikTokVideo) => {
		if (!video) return undefined;

		interface VideoFormat {
			format_id: string;
			format_note: string;
			ext: string;
			protocol: string;
			acodec: string;
			vcodec: string;
			url: string;
			width: number | null;
			height: number | null;
			fps: number | null;
			audio_channels: number | null;
			quality: number;
			has_drm: boolean;
			tbr: number;
			filesize_approx: number | null;
			resolution: string;
			aspect_ratio: number | null;
			format: string;
		}

		interface VideoThumbnail {
			url: string;
			height: number;
			width: number;
			id: string;
			resolution: string;
		}

		const formats: VideoFormat[] = [];

		// 添加主视频格式
		let mainPlayUrl = '';
		for (const url of video.video.PlayAddrStruct?.UrlList || []) {
			if (url.includes('www.tiktok.com')) {
				mainPlayUrl = url;
			}
		}
		if (mainPlayUrl) {
			formats.push({
				format_id: 'main',
				format_note: video.video.videoQuality || '标准',
				ext: video.video.format || 'mp4',
				protocol: 'https',
				acodec: 'aac',
				vcodec: 'h264',
				url: mainPlayUrl,
				width: video.video.PlayAddrStruct?.Width,
				height: video.video.PlayAddrStruct?.Height,
				fps: null,
				audio_channels: 2,
				quality: 1,
				has_drm: false,
				tbr: video.video.bitrate || 0,
				filesize_approx: null,
				resolution: `${video.video.width}x${video.video.height}`,
				aspect_ratio: video.video.width / video.video.height,
				format: `${video.video.height}p - ${video.video.format || 'mp4'}`,
			});
		}

		// 添加bitrateInfo中的其他格式
		if (video.video.bitrateInfo) {
			video.video.bitrateInfo.forEach((info, index) => {
				if (info.PlayAddr && info.PlayAddr.UrlList && info.PlayAddr.UrlList.length > 0) {
					let bitPlayUrl = '';
					for (const playUrl of info.PlayAddr.UrlList) {
						if (playUrl.includes('www.tiktok.com')) {
							bitPlayUrl = playUrl;
						}
					}
					formats.push({
						format_id: `bitrate_${index}`,
						format_note: info.Format || '标准',
						ext: 'mp4',
						protocol: 'https',
						acodec: 'aac',
						vcodec: 'h264',
						url: bitPlayUrl,
						width: info.PlayAddr.Width,
						height: info.PlayAddr.Height,
						fps: null,
						audio_channels: 2,
						quality: 1,
						has_drm: false,
						tbr: 0,
						filesize_approx: info.PlayAddr.DataSize,
						resolution: `${info.PlayAddr.Width}x${info.PlayAddr.Height}`,
						aspect_ratio: info.PlayAddr.Width / info.PlayAddr.Height,
						format: `${info.PlayAddr.Height}p - ${info.Format || 'mp4'}`,
					});
				}
			});
		}

		// 添加缩略图
		const thumbnails: VideoThumbnail[] = [
			{
				url: video.video.cover,
				height: video.video.height,
				width: video.video.width,
				id: 'cover',
				resolution: `${video.video.width}x${video.video.height}`,
			},
		].filter((thumb) => thumb.url);

		return {
			id: video.id,
			title: video.desc || `TikTok视频_${video.id}`,
			formats,
			thumbnails,
		};
	};

	// 转换TikTok字幕数据为SubtitleDownloadDialog所需的格式
	const convertToSubtitleDetail = (video: TikTokVideo) => {
		if (!video || !video.video.subtitleInfos || video.video.subtitleInfos.length === 0) {
			return undefined;
		}

		const subtitles: Record<string, any[]> = {};

		video.video.subtitleInfos.forEach((subtitle) => {
			const langKey = subtitle.LanguageCodeName || subtitle.LanguageID;
			if (!subtitles[langKey]) {
				subtitles[langKey] = [];
			}

			subtitles[langKey].push({
				ext: subtitle.Format?.toLowerCase() || 'srt',
				url: subtitle.Url,
				name: subtitle.LanguageCodeName || subtitle.LanguageID,
			});
		});

		return {
			id: video.id,
			title: video.desc || `TikTok视频_${video.id}`,
			subtitles,
		};
	};

	// 处理下载请求
	const handleDownload = async (url: string, option: DownloadOption) => {
		try {
			if (!option.url) {
				throw new Error('请选择下载的资源');
			}

			// 查找当前视频信息
			const currentVideo = videos.find((v) => v.video?.playAddr === url || v.video?.downloadAddr === url);
			const videoTitle = currentVideo?.desc || currentVideo?.id || 'download';

			// 清理文件名，移除特殊字符
			const cleanFilename = videoTitle
				.replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
				.replace(/[\s]+/g, '_') // 将空格替换为下划线
				.substring(0, 100); // 限制长度

			// 构建后端代理下载链接
			const downloadUrl = await buildProxyUrl(`${option.url}`, 60 * 60 * 24, 'tiktok', '', cleanFilename + '.' + option.format);

			try {
				// 尝试直接下载
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 10000);
				const response = await fetch(downloadUrl, {
					method: 'HEAD',
					signal: controller.signal,
				});

				clearTimeout(timeoutId);

				if (response.ok) {
					// 如果响应正常，创建隐藏的下载链接并触发下载
					const link = document.createElement('a');
					link.href = downloadUrl;
					link.style.display = 'none';
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);

					toast({
						title: '下载开始',
						description: `${option.type === 'video' ? '视频' : option.type === 'audio' ? '音频' : '封面'}下载已开始`,
					});
				} else {
					throw new Error('下载链接无效');
				}
			} catch (downloadError) {
				// 直接下载失败，提供备用方案
				console.warn('直接下载失败，使用备用方案:', downloadError);
				toast({
					title: '下载链接已打开',
					description: '已在新标签页打开下载链接，请在新页面右键选择"另存为"进行下载',
					variant: 'default',
					duration: 3000,
				});
				// 在新标签页打开下载链接
				window.open(option.url, '_blank');
			}
		} catch (error) {
			console.error('下载失败:', error);
			toast({
				title: '下载失败',
				description: error instanceof Error ? error.message : '下载过程中发生错误，请稍后重试',
				variant: 'destructive',
			});
		}
	};

	return (
		<TooltipProvider>
			<div className="p-4 space-y-4">
				<Card className="p-6">
					<div className="flex gap-2 mb-4">
						<Input
							placeholder="请输入TikTok用户主页链接或用户名，例如：https://www.tiktok.com/@username 或 username"
							value={userUrl}
							onChange={(e) => setUserUrl(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
						/>
						<Button onClick={handleSearch} disabled={isLoading}>
							<Search className="w-4 h-4 mr-2" />
							{isLoading ? '搜索中...' : '搜索'}
						</Button>
					</div>

					{userInfo && (
						<div className="mt-4 p-4 bg-gray-50 rounded-lg">
							<div className="flex items-center space-x-4">
								{isShowingExampleData ? (
									<ExampleImageSVG width={64} height={64} type="avatar" />
								) : (
									<img
										src={userInfo.user.avatarLarger}
										alt={userInfo.user.nickname}
										className="w-16 h-16 rounded-full"
										referrerPolicy="no-referrer"
									/>
								)}
								<div>
									<div className="flex items-center gap-2">
										<h3 className="text-lg font-semibold">{userInfo.user.nickname}</h3>
										{isShowingExampleData && <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">示例数据</span>}
									</div>
									<p className="text-gray-600">@{userInfo.user.uniqueId}</p>
									<p className="text-sm text-gray-500">{userInfo.user.signature}</p>
									<div className="flex space-x-4 text-sm text-gray-600 mt-1">
										<span>关注: {userInfo.stats.followingCount.toLocaleString()}</span>
										<span>粉丝: {userInfo.stats.followerCount.toLocaleString()}</span>
										<span>获赞: {userInfo.stats.heartCount.toLocaleString()}</span>
										<span>作品: {userInfo.stats.videoCount.toLocaleString()}</span>
									</div>
								</div>
							</div>
						</div>
					)}

					{videos.length > 0 && (
						<div className="mt-6 space-y-2">
							<div className="flex justify-between items-center">
								<div className="flex items-center gap-2">
									<h2 className="text-lg font-semibold">用户作品 - 已加载 {videos.length} 条视频</h2>
									{isShowingExampleData && <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">示例数据</span>}
								</div>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button variant="outline" onClick={isShowingExampleData ? undefined : handleExport} disabled={isShowingExampleData}>
											<Download className="w-4 h-4 mr-2" />
											导出
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>{isShowingExampleData ? '示例数据不支持导出' : '导出数据'}</p>
									</TooltipContent>
								</Tooltip>
							</div>
							<div className="border rounded-md">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead className="w-[100px]">封面</TableHead>
											<TableHead>作者</TableHead>
											<TableHead>描述</TableHead>
											<TableHead className="w-[80px]">播放量</TableHead>
											<TableHead className="w-[80px]">点赞</TableHead>
											<TableHead className="w-[80px]">评论</TableHead>
											<TableHead className="w-[80px]">分享</TableHead>
											<TableHead className="w-[80px]">收藏</TableHead>
											<TableHead className="w-[80px]">时长</TableHead>
											<TableHead className="w-[160px]">发布时间</TableHead>
											<TableHead className="w-[140px]">操作</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{videos.map((video) => (
											<TableRow key={video.id}>
												<TableCell>
													{isShowingExampleData ? (
														<ExampleImageSVG width={80} height={80} type="video" />
													) : (
														<img
															src={video.video.cover}
															alt={video.desc}
															className="w-20 h-20 object-cover rounded"
															loading="lazy"
															referrerPolicy="no-referrer"
														/>
													)}
												</TableCell>
												<TableCell>
													<div className="flex items-center space-x-2">
														{isShowingExampleData ? (
															<ExampleImageSVG width={32} height={32} type="avatar" />
														) : (
															<img
																src={video.author.avatarThumb}
																alt={video.author.nickname}
																className="w-8 h-8 rounded-full"
																referrerPolicy="no-referrer"
															/>
														)}
														<div>
															<a
																href={`https://www.tiktok.com/@${video.author.uniqueId}`}
																target="_blank"
																rel="noopener noreferrer"
																className="text-blue-600 hover:underline text-sm font-medium"
															>
																{video.author.nickname}
															</a>
															<p className="text-xs text-gray-500">@{video.author.uniqueId}</p>
														</div>
													</div>
												</TableCell>
												<TableCell className="max-w-[300px]">
													<a
														href={`https://www.tiktok.com/@${video.author.uniqueId}/video/${video.id}?is_from_webapp=1`}
														target="_blank"
														rel="noopener noreferrer"
														className="text-blue-600 hover:underline text-sm"
													>
														<div className="line-clamp-3">{video.desc}</div>
													</a>
												</TableCell>
												<TableCell>{video.stats.playCount.toLocaleString()}</TableCell>
												<TableCell>{video.stats.diggCount.toLocaleString()}</TableCell>
												<TableCell>{video.stats.commentCount.toLocaleString()}</TableCell>
												<TableCell>{video.stats.shareCount.toLocaleString()}</TableCell>
												<TableCell>{video.stats.collectCount.toLocaleString()}</TableCell>
												<TableCell>{formatDuration(video.video.duration)}</TableCell>
												<TableCell className="whitespace-pre-line w-[160px]">
													{new Date(video.createTime * 1000)
														.toLocaleString('zh-CN', {
															year: 'numeric',
															month: '2-digit',
															day: '2-digit',
															hour: '2-digit',
															minute: '2-digit',
															second: '2-digit',
															hour12: false,
														})
														.replace(/\//g, '-')
														.replace(',', '\n')}
												</TableCell>
												<TableCell className="w-[140px]">
													<div className="flex gap-2">
														{/* <Tooltip>
															<TooltipTrigger asChild>
																<Button
																	size="sm"
																	variant="outline"
																	onClick={() => (isShowingExampleData ? null : handleVideoDownload(video))}
																	disabled={isShowingExampleData}
																>
																	<Video className="w-4 h-4" />
																</Button>
															</TooltipTrigger>
															<TooltipContent>
																<p>{isShowingExampleData ? '示例数据不支持下载' : '视频下载'}</p>
															</TooltipContent>
														</Tooltip> */}
														<Tooltip>
															<TooltipTrigger asChild>
																<Button
																	size="sm"
																	variant="outline"
																	onClick={() => (isShowingExampleData ? null : handleSubtitleDownload(video))}
																	disabled={isShowingExampleData || !video.video.subtitleInfos || video.video.subtitleInfos.length === 0}
																>
																	<Subtitles className="w-4 h-4" />
																</Button>
															</TooltipTrigger>
															<TooltipContent>
																<p>{isShowingExampleData ? '示例数据不支持下载' : '字幕下载'}</p>
															</TooltipContent>
														</Tooltip>
													</div>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{hasMore && (
								<div className="flex justify-center mt-4">
									<Button onClick={handleLoadMore} disabled={isLoadingMore} variant="outline">
										{isLoadingMore ? (
											<div className="flex items-center">
												<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
												加载中...
											</div>
										) : (
											'加载更多'
										)}
									</Button>
								</div>
							)}
						</div>
					)}

					{isLoading && (
						<div className="flex justify-center py-4">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
						</div>
					)}
				</Card>

				{/* 视频下载对话框 */}
				<MediaDownloadDialog
					isOpen={isMediaDialogOpen}
					onClose={() => setIsMediaDialogOpen(false)}
					mediaUrl={selectedVideo ? `https://www.tiktok.com/@${selectedVideo.author.uniqueId}/video/${selectedVideo.id}` : ''}
					videoDetail={selectedVideo ? convertToVideoDetail(selectedVideo) : undefined}
					onDownload={handleDownload}
					title="TikTok视频下载"
				/>

				{/* 字幕下载对话框 */}
				<SubtitleDownloadDialog
					isOpen={isSubtitleDialogOpen}
					onClose={() => setIsSubtitleDialogOpen(false)}
					videoDetail={selectedVideo ? convertToSubtitleDetail(selectedVideo) : undefined}
					videoTitle={selectedVideo?.desc || `TikTok视频_${selectedVideo?.id}`}
				/>
			</div>
		</TooltipProvider>
	);
};

export default TikTokUserVideos;
