import React, { useState, useRef, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Textarea } from '../../components/ui/textarea';
import { Label } from '../../components/ui/label';
import { Upload, Download, Search, Video, Subtitles } from 'lucide-react';
import { ApiError, apiRequest } from '../../lib/api';
import { useToast } from '../../components/ui/use-toast';
import * as XLSX from 'xlsx';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { exportArrayToCSV } from '../../lib/utils';
import { formatDuration } from '../../lib/utils';
import MediaDownloadDialog, { DownloadOption } from '../../components/MediaDownloadDialog';
import SubtitleDownloadDialog from '../../components/SubtitleDownloadDialog';
import { buildProxyUrl } from '../../lib/buildPorxy';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';

interface TikTokVideo {
    id: string;
    desc: string;
    createTime: number;
    video: {
        id: string;
        height: number;
        width: number;
        duration: number;
        ratio: string;
        cover: string;
        originCover: string;
        dynamicCover: string;
        playAddr: string;
        downloadAddr: string;
        shareCover: string[];
        reflowCover: string;
        bitrate: number;
        encodedType: string;
        format: string;
        videoQuality: string;
        encodeUserTag: string;
        codecType: string;
        definition: string;
        PlayAddrStruct: {
            DataSize: number;
            Height: number;
            Width: number;
            UrlList: string[];
        };
        bitrateInfo: Array<{
            Format: string;
            PlayAddr: {
                DataSize: number;
                Height: number;
                Width: number;
                UrlList: string[];
            };
        }>;
        subtitleInfos: Array<{
            LanguageID: string;
            LanguageCodeName: string;
            Url: string;
            UrlExpire: number;
            Format: string;
            Version: string;
            Source: string;
            VideoSubtitleID: number;
            Size: number;
        }>;
    };
    author: {
        id: string;
        shortId: string;
        uniqueId: string;
        nickname: string;
        avatarLarger: string;
        avatarMedium: string;
        avatarThumb: string;
        signature: string;
        createTime: number;
        verified: boolean;
        secUid: string;
        ftc: boolean;
        relation: number;
        openFavorite: boolean;
        commentSetting: number;
        duetSetting: number;
        stitchSetting: number;
        privateAccount: boolean;
        secret: boolean;
        isADVirtual: boolean;
        roomId: string;
    };
    music: {
        id: string;
        title: string;
        playUrl: string;
        coverLarge: string;
        coverMedium: string;
        coverThumb: string;
        authorName: string;
        original: boolean;
        duration: number;
        album: string;
    };
    challenges: Array<{
        id: string;
        title: string;
        desc: string;
        profileLarger: string;
        profileMedium: string;
        profileThumb: string;
        coverLarger: string;
        coverMedium: string;
        coverThumb: string;
        isCommerce: boolean;
    }>;
    stats: {
        diggCount: number;
        shareCount: number;
        commentCount: number;
        playCount: number;
        collectCount: number;
    };
    duetInfo: {
        duetFromId: string;
    };
    originalItem: boolean;
    officalItem: boolean;
    textExtra: Array<{
        awemeId: string;
        start: number;
        end: number;
        hashtagName: string;
        hashtagId: string;
        type: number;
        userId: string;
        isCommerce: boolean;
        userUniqueId: string;
        secUid: string;
    }>;
    secret: boolean;
    forFriend: boolean;
    digged: boolean;
    itemCommentStatus: number;
    showNotPass: boolean;
    vl1: boolean;
    itemMute: boolean;
    embedsDisabled: boolean;
    shareEnabled: boolean;
    comments: any[];
    duetEnabled: boolean;
    stitchEnabled: boolean;
    shareInfo: {
        shareUrl: string;
        shareTitleMyself: string;
        shareTitleOther: string;
        shareDesc: string;
        shareQuote: string;
    };
}

const TikTokVideoExtractor: React.FC = () => {
    const [videoUrls, setVideoUrls] = useState('');
    const [videos, setVideos] = useState<TikTokVideo[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isShowingExampleData, setIsShowingExampleData] = useState(true);
    const [selectedVideo, setSelectedVideo] = useState<TikTokVideo | null>(null);
    const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false);
    const [isSubtitleDialogOpen, setIsSubtitleDialogOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();

    // 加载示例数据
    const loadExampleData = async () => {
        try {
            // 加载视频详情示例数据
            const response = await fetch('/test/data/tiktok/videoDetail.json');
            const data = await response.json();

            // 转换视频数据
            const itemStruct = data.data.itemInfo.itemStruct;
            const exampleVideo: TikTokVideo = {
                id: itemStruct.id,
                desc: itemStruct.desc,
                createTime: itemStruct.createTime,
                video: {
                    id: itemStruct.video.id,
                    height: itemStruct.video.height,
                    width: itemStruct.video.width,
                    duration: itemStruct.video.duration,
                    ratio: itemStruct.video.ratio,
                    cover: itemStruct.video.cover,
                    originCover: itemStruct.video.originCover,
                    dynamicCover: itemStruct.video.dynamicCover || '',
                    playAddr: itemStruct.video.playAddr,
                    downloadAddr: itemStruct.video.downloadAddr || '',
                    shareCover: itemStruct.video.shareCover || [],
                    reflowCover: itemStruct.video.reflowCover || '',
                    bitrate: itemStruct.video.bitrate || 0,
                    encodedType: itemStruct.video.encodedType || '',
                    format: itemStruct.video.format,
                    videoQuality: itemStruct.video.videoQuality || '',
                    encodeUserTag: itemStruct.video.encodeUserTag || '',
                    codecType: itemStruct.video.codecType || '',
                    definition: itemStruct.video.definition || '',
                    PlayAddrStruct: itemStruct.video.PlayAddrStruct,
                    bitrateInfo: itemStruct.video.bitrateInfo || [],
                    subtitleInfos: itemStruct.video.subtitleInfos || []
                },
                author: {
                    id: itemStruct.author.id,
                    shortId: itemStruct.author.shortId || '',
                    uniqueId: itemStruct.author.uniqueId,
                    nickname: itemStruct.author.nickname,
                    avatarLarger: itemStruct.author.avatarLarger,
                    avatarMedium: itemStruct.author.avatarMedium,
                    avatarThumb: itemStruct.author.avatarThumb,
                    signature: itemStruct.author.signature,
                    createTime: itemStruct.author.createTime || 0,
                    verified: itemStruct.author.verified,
                    secUid: itemStruct.author.secUid,
                    ftc: itemStruct.author.ftc,
                    relation: itemStruct.author.relation,
                    openFavorite: itemStruct.author.openFavorite,
                    commentSetting: itemStruct.author.commentSetting,
                    duetSetting: itemStruct.author.duetSetting,
                    stitchSetting: itemStruct.author.stitchSetting,
                    privateAccount: itemStruct.author.privateAccount,
                    secret: itemStruct.author.secret,
                    isADVirtual: itemStruct.author.isADVirtual,
                    roomId: itemStruct.author.roomId || ''
                },
                music: {
                    id: itemStruct.music.id,
                    title: itemStruct.music.title,
                    playUrl: itemStruct.music.playUrl,
                    coverLarge: itemStruct.music.coverLarge,
                    coverMedium: itemStruct.music.coverMedium,
                    coverThumb: itemStruct.music.coverThumb,
                    authorName: itemStruct.music.authorName,
                    original: itemStruct.music.original,
                    duration: itemStruct.music.duration,
                    album: itemStruct.music.album || ''
                },
                challenges: itemStruct.challenges || [],
                stats: itemStruct.stats,
                duetInfo: itemStruct.duetInfo || { duetFromId: '' },
                originalItem: itemStruct.originalItem || false,
                officalItem: itemStruct.officalItem || false,
                textExtra: itemStruct.textExtra || [],
                secret: itemStruct.secret,
                forFriend: itemStruct.forFriend,
                digged: itemStruct.digged,
                itemCommentStatus: itemStruct.itemCommentStatus,
                showNotPass: itemStruct.showNotPass || false,
                vl1: itemStruct.vl1 || false,
                itemMute: itemStruct.itemMute || false,
                embedsDisabled: itemStruct.embedsDisabled || false,
                shareEnabled: itemStruct.shareEnabled,
                comments: itemStruct.comments || [],
                duetEnabled: itemStruct.duetEnabled,
                stitchEnabled: itemStruct.stitchEnabled,
                shareInfo: itemStruct.shareInfo || {
                    shareUrl: '',
                    shareTitleMyself: '',
                    shareTitleOther: '',
                    shareDesc: '',
                    shareQuote: ''
                }
            };

            setVideos([exampleVideo]);

            toast({
                title: '示例数据已加载',
                description: '已加载 1 条示例视频数据',
            });
        } catch (error) {
            console.error('Failed to load example data:', error);
            toast({
                variant: 'destructive',
                title: '加载示例数据失败',
                description: '无法加载示例数据，请检查文件是否存在',
            });
        }
    };

    // 组件挂载时加载示例数据
    useEffect(() => {
        loadExampleData();
    }, []);

    const parseVideoUrls = (text: string): string[] => {
        const lines = text.split('\n').filter((line) => line.trim());
        const urls: string[] = [];

        const regexes = [
            /https:\/\/www\.tiktok\.com\/@[\w.-]+\/(video|photo)\/\d+/g,
            /https:\/\/(vm|vt)\.tiktok\.com\/[\w\d]+/g,
            /https:\/\/www\.tiktok\.com\/embed\/v2\/\d+/g,
        ];

        for (const line of lines) {
            for (const regex of regexes) {
                const matches = line.match(regex);
                if (matches) {
                    urls.push(...matches);
                }
            }
        }

        return [...new Set(urls)];
    };

    const handleExtract = async () => {
        const urls = parseVideoUrls(videoUrls);

        if (urls.length === 0) {
            toast({
                title: '提示',
                description: '请输入有效的 TikTok 视频链接',
                variant: 'destructive',
            });
            return;
        }

        setIsLoading(true);
        setIsShowingExampleData(false);
        setVideos([]);

        toast({
            title: '开始提取',
            description: `正在提取 ${urls.length} 个视频信息...`,
        });

        try {
            let successCount = 0;
            let failCount = 0;

            // 使用批处理方式，每次处理3个视频，实现实时渲染
            const batchSize = 3;
            for (let i = 0; i < urls.length; i += batchSize) {
                const batch = urls.slice(i, i + batchSize);

                const batchPromises = batch.map(async (url) => {
                    try {
                        const response = await apiRequest<any>(`/tiktok/video/detail`, {
                            method: 'POST',
                            body: JSON.stringify({ url: url }),
                        });

                        if (response.success && response.data?.itemInfo?.itemStruct) {
                            const video = response.data.itemInfo.itemStruct as TikTokVideo;
                            // 实时更新视频列表
                            setVideos(prevVideos => [...prevVideos, video]);
                            successCount++;
                            return video;
                        } else {
                            console.error('Failed to fetch video detail for', url, response);
                            failCount++;
                            return null;
                        }
                    } catch (error) {
                        console.error('Error fetching video detail for', url, error);
                        failCount++;
                        return null;
                    }
                });

                await Promise.all(batchPromises);

                // 添加小延迟避免请求过于频繁
                if (i + batchSize < urls.length) {
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
            }

            if (successCount > 0) {
                toast({
                    title: '提取完成',
                    description: `成功提取 ${successCount} 个视频信息${failCount > 0 ? `，失败 ${failCount} 个` : ''}`,
                });
            } else {
                toast({
                    title: '提取失败',
                    description: '未能提取到任何视频信息，请检查链接是否正确',
                    variant: 'destructive',
                });
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleClear = () => {
        setVideoUrls('');
        setVideos([]);
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target?.result as ArrayBuffer);
                const workbook = XLSX.read(data, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

                const urls = jsonData
                    .slice(1)
                    .map((row) => row[0])
                    .filter((url) => url && typeof url === 'string' && url.trim())
                    .join('\n');

                setVideoUrls((prev) => (prev ? `${prev}\n${urls}` : urls));

                toast({
                    title: '文件上传成功',
                    description: `已导入 ${urls.split('\n').length} 个链接`,
                });
            } catch (error) {
                console.error('文件解析失败:', error);
                toast({
                    title: '文件解析失败',
                    description: '请确保文件格式正确',
                    variant: 'destructive',
                });
            }
        };
        reader.readAsArrayBuffer(file);
    };

    const downloadTemplate = () => {
        const templateData = [['视频链接'], ['https://www.tiktok.com/@therock/video/7318343352015523115']];

        const ws = XLSX.utils.aoa_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'TikTok视频链接模板');
        XLSX.writeFile(wb, 'TikTok视频链接模板.xlsx');
        toast({
            title: '模板下载成功',
            description: '请在模板中填入视频链接后重新上传',
        });
    };

    const handleExport = () => {
        if (isShowingExampleData) {
            toast({
                title: '示例数据不支持导出',
                description: '请输入真实的视频链接进行提取后再导出',
                variant: 'destructive',
            });
            return;
        }

        if (videos.length === 0) return;

        const rows = [
            ['视频ID', '作者', '描述', '播放量', '点赞', '评论', '分享', '收藏', '时长', '创建时间', '视频链接', '封面链接', '博主链接'],
            ...videos.map((video) => [
                video.id,
                video.author.nickname,
                video.desc,
                video.stats.playCount,
                video.stats.diggCount,
                video.stats.commentCount,
                video.stats.shareCount,
                video.stats.collectCount,
                formatDuration(video.video.duration),
                new Date(video.createTime * 1000).toLocaleString(),
                `https://www.tiktok.com/@${video.author.uniqueId}/video/${video.id}?is_from_webapp=1`,
                video.video.cover,
                `https://www.tiktok.com/@${video.author.uniqueId}`,
            ]),
        ];

        exportArrayToCSV(rows, `TikTok视频提取结果_${new Date().toLocaleString()}.csv`);

        toast({
            title: '导出成功',
            description: `已导出 ${videos.length} 条视频数据`,
        });
    };

    const handleVideoDownload = async (url: string, option: DownloadOption) => {
        try {
            if (!option.url) {
                throw new Error('请选择下载的资源');
            }

            const cleanFilename = option.type === 'video' ? '视频下载' : option.type === 'audio' ? '音频下载' : '封面下载';

            const downloadUrl = await buildProxyUrl(`${option.url}`, 60 * 60 * 24, 'tiktok', '', cleanFilename + '.' + option.format);

            const link = document.createElement('a');
            link.href = downloadUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast({
                title: '下载开始',
                description: `${cleanFilename}已开始下载`,
            });
        } catch (error) {
            console.error('下载失败:', error);
            toast({
                title: '下载失败',
                description: error instanceof Error ? error.message : '下载过程中发生错误，请稍后重试',
                variant: 'destructive',
            });
        }
    };

    const handleSubtitleDownload = (video: TikTokVideo) => {
        setSelectedVideo(video);
        setIsSubtitleDialogOpen(true);
    };

    const convertToVideoDetail = (video: TikTokVideo) => {
        if (!video) return undefined;

        interface VideoFormat {
            format_id: string;
            format_note: string;
            ext: string;
            protocol: string;
            acodec: string;
            vcodec: string;
            url: string;
            width: number | null;
            height: number | null;
            fps: number | null;
            audio_channels: number | null;
            quality: number;
            has_drm: boolean;
            tbr: number;
            filesize_approx: number | null;
            resolution: string;
            aspect_ratio: number | null;
            format: string;
        }

        interface VideoThumbnail {
            url: string;
            height: number;
            width: number;
            id: string;
            resolution: string;
        }

        const formats: VideoFormat[] = [];

        // 添加主视频格式
        let mainPlayUrl = '';
        for (const url of video.video.PlayAddrStruct?.UrlList || []) {
            if (url.includes('www.tiktok.com')) {
                mainPlayUrl = url;
            }
        }
        if (mainPlayUrl) {
            formats.push({
                format_id: 'main',
                format_note: video.video.videoQuality || '标准',
                ext: video.video.format || 'mp4',
                protocol: 'https',
                acodec: 'aac',
                vcodec: 'h264',
                url: mainPlayUrl,
                width: video.video.PlayAddrStruct?.Width,
                height: video.video.PlayAddrStruct?.Height,
                fps: null,
                audio_channels: 2,
                quality: 1,
                has_drm: false,
                tbr: video.video.bitrate || 0,
                filesize_approx: null,
                resolution: `${video.video.width}x${video.video.height}`,
                aspect_ratio: video.video.width / video.video.height,
                format: `${video.video.height}p - ${video.video.format || 'mp4'}`,
            });
        }

        // 添加bitrateInfo中的其他格式
        if (video.video.bitrateInfo) {
            video.video.bitrateInfo.forEach((info, index) => {
                if (info.PlayAddr && info.PlayAddr.UrlList && info.PlayAddr.UrlList.length > 0) {
                    let bitPlayUrl = '';
                    for (const playUrl of info.PlayAddr.UrlList) {
                        if (playUrl.includes('www.tiktok.com')) {
                            bitPlayUrl = playUrl;
                        }
                    }
                    formats.push({
                        format_id: `bitrate_${index}`,
                        format_note: info.Format || '标准',
                        ext: 'mp4',
                        protocol: 'https',
                        acodec: 'aac',
                        vcodec: 'h264',
                        url: bitPlayUrl,
                        width: info.PlayAddr.Width,
                        height: info.PlayAddr.Height,
                        fps: null,
                        audio_channels: 2,
                        quality: 1,
                        has_drm: false,
                        tbr: 0,
                        filesize_approx: info.PlayAddr.DataSize,
                        resolution: `${info.PlayAddr.Width}x${info.PlayAddr.Height}`,
                        aspect_ratio: info.PlayAddr.Width / info.PlayAddr.Height,
                        format: `${info.PlayAddr.Height}p - ${info.Format || 'mp4'}`,
                    });
                }
            });
        }

        // 添加缩略图
        const thumbnails: VideoThumbnail[] = [
            {
                url: video.video.cover,
                height: video.video.height,
                width: video.video.width,
                id: 'cover',
                resolution: `${video.video.width}x${video.video.height}`,
            }
        ].filter((thumb) => thumb.url);

        return {
            id: video.id,
            title: video.desc || `TikTok视频_${video.id}`,
            formats,
            thumbnails,
        };
    };

    const convertToSubtitleDetail = (video: TikTokVideo) => {
        if (!video || !video.video.subtitleInfos || video.video.subtitleInfos.length === 0) {
            return undefined;
        }

        const subtitles: Record<string, any[]> = {};

        video.video.subtitleInfos.forEach((subtitle) => {
            const langKey = subtitle.LanguageCodeName || subtitle.LanguageID;
            if (!subtitles[langKey]) {
                subtitles[langKey] = [];
            }

            subtitles[langKey].push({
                ext: subtitle.Format?.toLowerCase() || 'srt',
                url: subtitle.Url,
                name: subtitle.LanguageCodeName || subtitle.LanguageID,
            });
        });

        return {
            id: video.id,
            title: video.desc || `TikTok视频_${video.id}`,
            subtitles,
        };
    };

    return (
        <TooltipProvider>
            <div className="space-y-4">
            <Card className="p-6">
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <Label htmlFor="video-urls" className="text-base font-medium">
                            TikTok 视频链接提取
                        </Label>
                        <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={downloadTemplate} disabled={isLoading}>
                                <Download className="h-4 w-4 mr-2" />
                                下载模板
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => fileInputRef.current?.click()} disabled={isLoading}>
                                <Upload className="h-4 w-4 mr-2" />
                                导入 Excel
                            </Button>
                            <input ref={fileInputRef} type="file" accept=".xlsx,.xls" onChange={handleFileUpload} className="hidden" />
                        </div>
                    </div>
                    <Textarea
                        id="video-urls"
                        placeholder="请输入 TikTok 视频链接，每行一个"
                        value={videoUrls}
                        onChange={(e) => setVideoUrls(e.target.value)}
                        className="min-h-[120px]"
                        disabled={isLoading}
                    />
                </div>

                <div className="flex justify-end mt-4 gap-2">
                    <Button onClick={handleExtract} disabled={isLoading || !videoUrls.trim()}>
                        <Search className="w-4 h-4 mr-2" />
                        {isLoading ? '提取中...' : '提取'}
                    </Button>
                    <Button variant="outline" onClick={handleClear} disabled={isLoading || (!videoUrls.trim() && videos.length === 0)}>
                        清空
                    </Button>
                </div>
            </Card>

            {isLoading && (
                <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
            )}

            {videos.length > 0 && (
                <div>
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-2">
                            <h2 className="text-xl font-semibold">提取结果 - 共 {videos.length} 条视频</h2>
                            {isShowingExampleData && (
                                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                                    示例数据
                                </span>
                            )}
                        </div>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    variant="outline"
                                    onClick={isShowingExampleData ? undefined : handleExport}
                                    disabled={isShowingExampleData}
                                >
                                    <Download className="w-4 h-4 mr-2" />
                                    导出 CSV
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{isShowingExampleData ? '示例数据不支持导出' : '导出视频数据'}</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                    <div className="border rounded-md overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[100px]">封面</TableHead>
                                    <TableHead>作者</TableHead>
                                    <TableHead>描述</TableHead>
                                    <TableHead className="w-[80px]">播放量</TableHead>
                                    <TableHead className="w-[80px]">点赞</TableHead>
                                    <TableHead className="w-[80px]">评论</TableHead>
                                    <TableHead className="w-[80px]">分享</TableHead>
                                    <TableHead className="w-[80px]">收藏</TableHead>
                                    <TableHead className="w-[80px]">时长</TableHead>
                                    <TableHead className="w-[160px]">发布时间</TableHead>
                                    <TableHead className="w-[140px]">操作</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {videos.map((video) => (
                                    <TableRow key={video.id}>
                                        <TableCell>
                                            {isShowingExampleData ? (
                                                <ExampleImageSVG width={80} height={80} type="video" />
                                            ) : (
                                                <img
                                                    src={video.video.cover}
                                                    alt={video.desc}
                                                    className="w-20 h-20 object-cover rounded"
                                                    loading="lazy"
                                                    referrerPolicy="no-referrer"
                                                />
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center space-x-2">
                                                {isShowingExampleData ? (
                                                    <ExampleImageSVG width={32} height={32} type="avatar" />
                                                ) : (
                                                    <img
                                                        src={video.author.avatarThumb}
                                                        alt={video.author.nickname}
                                                        className="w-8 h-8 rounded-full"
                                                        referrerPolicy="no-referrer"
                                                    />
                                                )}
                                                <div>
                                                    {isShowingExampleData ? (
                                                        <span className="text-gray-700 text-sm font-medium">
                                                            {video.author.nickname}
                                                        </span>
                                                    ) : (
                                                        <a
                                                            href={`https://www.tiktok.com/@${video.author.uniqueId}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="text-blue-600 hover:underline text-sm font-medium"
                                                        >
                                                            {video.author.nickname}
                                                        </a>
                                                    )}
                                                    <p className="text-xs text-gray-500">@{video.author.uniqueId}</p>
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell className="max-w-[300px]">
                                            {isShowingExampleData ? (
                                                <div className="text-gray-700 text-sm">
                                                    <div className="line-clamp-3">{video.desc}</div>
                                                </div>
                                            ) : (
                                                <a
                                                    href={`https://www.tiktok.com/@${video.author.uniqueId}/video/${video.id}?is_from_webapp=1`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:underline text-sm"
                                                >
                                                    <div className="line-clamp-3">{video.desc}</div>
                                                </a>
                                            )}
                                        </TableCell>
                                        <TableCell>{video.stats.playCount.toLocaleString()}</TableCell>
                                        <TableCell>{video.stats.diggCount.toLocaleString()}</TableCell>
                                        <TableCell>{video.stats.commentCount.toLocaleString()}</TableCell>
                                        <TableCell>{video.stats.shareCount.toLocaleString()}</TableCell>
                                        <TableCell>{video.stats.collectCount.toLocaleString()}</TableCell>
                                        <TableCell>{formatDuration(video.video.duration)}</TableCell>
                                        <TableCell className="whitespace-pre-line">
                                            {new Date(video.createTime * 1000).toLocaleString()}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex space-x-2">
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={isShowingExampleData ? undefined : () => {
                                                                setSelectedVideo(video);
                                                                setIsMediaDialogOpen(true);
                                                            }}
                                                            disabled={isShowingExampleData || !video.video.playAddr}
                                                        >
                                                            <Video className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>{isShowingExampleData ? '示例数据不支持下载视频' : '下载视频'}</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={isShowingExampleData ? undefined : () => handleSubtitleDownload(video)}
                                                            disabled={isShowingExampleData || !video.video.subtitleInfos || video.video.subtitleInfos.length === 0}
                                                        >
                                                            <Subtitles className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>{isShowingExampleData ? '示例数据不支持下载字幕' : '下载字幕'}</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            )}

            <MediaDownloadDialog
                isOpen={isMediaDialogOpen}
                onClose={() => setIsMediaDialogOpen(false)}
                mediaUrl={selectedVideo ? `https://www.tiktok.com/@${selectedVideo.author.uniqueId}/video/${selectedVideo.id}` : ''}
                videoDetail={selectedVideo ? convertToVideoDetail(selectedVideo) : undefined}
                onDownload={handleVideoDownload}
                title="TikTok视频下载"
            />

            <SubtitleDownloadDialog
                isOpen={isSubtitleDialogOpen}
                onClose={() => setIsSubtitleDialogOpen(false)}
                videoDetail={selectedVideo ? convertToSubtitleDetail(selectedVideo) : undefined}
                videoTitle={selectedVideo?.desc || `TikTok视频_${selectedVideo?.id}`}
            />
        </div>
        </TooltipProvider>
    );
};

export default TikTokVideoExtractor;