import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Search, Download, Users } from 'lucide-react';
import { ApiError, apiRequest } from '../../lib/api';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { exportArrayToCSV } from '../../lib/utils';
import { useToast } from '../../components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { ExampleImageSVG } from '../../components/ui/example-image';

interface TikTokUser {
	stats: {
		diggCount: number;
		followerCount: number;
		followingCount: number;
		friendCount: number;
		heart: number;
		heartCount: number;
		videoCount: number;
	};
	statsV2: {
		diggCount: string;
		followerCount: string;
		followingCount: string;
		friendCount: string;
		heart: string;
		heartCount: string;
		videoCount: string;
	};
	user: {
		UserStoryStatus: number;
		avatarLarger: string;
		avatarMedium: string;
		avatarThumb: string;
		commentSetting?: number;
		downloadSetting?: number;
		duetSetting?: number;
		ftc: boolean;
		id: string;
		isADVirtual: boolean;
		nickname: string;
		openFavorite: boolean;
		privateAccount: boolean;
		relation: number;
		secUid: string;
		secret: boolean;
		signature: string;
		stitchSetting?: number;
		ttSeller: boolean;
		uniqueId: string;
		verified: boolean;
	};
}

interface TikTokUserInfo {
	userInfo: {
		user: {
			id: string;
			shortId: string;
			uniqueId: string;
			nickname: string;
			avatarLarger: string;
			avatarMedium: string;
			avatarThumb: string;
			signature: string;
			createTime: number;
			verified: boolean;
			secUid: string;
			ftc: boolean;
			relation: number;
			openFavorite: boolean;
			commentSetting: number;
			duetSetting: number;
			stitchSetting: number;
			privateAccount: boolean;
			secret: boolean;
			isADVirtual: boolean;
			roomId: string;
		};
		stats: {
			followingCount: number;
			followerCount: number;
			heartCount: number;
			videoCount: number;
			diggCount: number;
			heart: number;
		};
	};
}

interface TikTokUserFansResponse {
	extra: {
		fatal_item_ids: any[];
		logid: string;
		now: number;
	};
	hasMore: boolean;
	log_pb: {
		impr_id: string;
	};
	minCursor: number;
	statusCode: number;
	status_code: number;
	status_msg: string;
	total: number;
	userList: TikTokUser[];
}

const TikTokUserFans: React.FC = () => {
	const [userUrl, setUserUrl] = useState('');
	const [userInfo, setUserInfo] = useState<TikTokUserInfo['userInfo'] | null>(null);
	const [fans, setFans] = useState<TikTokUser[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [hasMore, setHasMore] = useState(false);
	const [cursor, setCursor] = useState<number>(0);
	const [total, setTotal] = useState<number>(0);
	const [isShowingExampleData, setIsShowingExampleData] = useState(true);
	const { toast } = useToast();

	// 加载示例数据
	const loadExampleData = async () => {
		try {
			// 并行加载粉丝数据和用户数据
			const [fansResponse, userResponse] = await Promise.all([
				fetch('/test/data/tiktok/userFans.json'),
				fetch('/test/data/tiktok/user.json'),
			]);

			const fansData = await fansResponse.json();
			const userData = await userResponse.json();

			// 直接断言数据类型，避免大量赋值代码
			const convertedFans: TikTokUser[] = fansData.data.userList as TikTokUser[];
			const convertedUserInfo: TikTokUserInfo['userInfo'] = userData.data.userInfo as TikTokUserInfo['userInfo'];

			setFans(convertedFans);
			setUserInfo(convertedUserInfo);
			setHasMore(fansData.data.hasMore || false);
			setTotal(fansData.data.total || 0);

			toast({
				title: '示例数据已加载',
				description: `已加载 ${convertedFans.length} 条示例粉丝数据`,
			});
		} catch (error) {
			console.error('Failed to load example data:', error);
			toast({
				variant: 'destructive',
				title: '加载示例数据失败',
				description: '无法加载示例数据，请检查文件是否存在',
			});
		}
	};

	// 组件挂载时加载示例数据
	useEffect(() => {
		loadExampleData();
	}, []);

	const extractUserId = (url: string): string => {
		// Extract user ID from TikTok URL like https://www.tiktok.com/@username
		const match = url.match(/@([^/?]+)/);
		return match ? match[1] : url.trim();
	};

	const handleSearch = async () => {
		if (!userUrl.trim()) return;

		const extractedUserId = extractUserId(userUrl);
		if (!extractedUserId) {
			toast({
				variant: 'destructive',
				title: '无效的用户链接',
				description: '请输入正确的TikTok用户主页链接或用户名',
			});
			return;
		}

		setIsLoading(true);
		setFans([]);
		setUserInfo(null);
		setCursor(0);
		setTotal(0);
		setIsShowingExampleData(false);

		try {
			// 第一步：获取用户信息
			const userInfoResponse = await apiRequest<TikTokUserInfo>('/tiktok/user/info', {
				method: 'POST',
				body: JSON.stringify({ url: extractedUserId }),
			});

			if (!userInfoResponse.data?.userInfo?.user?.secUid) {
				throw new Error('无法获取用户的secUid');
			}

			setUserInfo(userInfoResponse.data.userInfo);

			// 第二步：使用secUid获取用户粉丝
			const response = await apiRequest<TikTokUserFansResponse>('/tiktok/user/fans', {
				method: 'POST',
				body: JSON.stringify({
					secUid: userInfoResponse.data.userInfo.user.secUid,
				}),
			});

			const fansList = response.data?.userList || [];
			setFans(fansList);
			setHasMore(response.data?.hasMore || false);
			setCursor(response.data?.minCursor || 0);
			setTotal(response.data?.total || 0);

			if (fansList.length > 0) {
				toast({
					title: '数据获取成功',
					description: `已加载 ${fansList.length} 个粉丝，总计 ${response.data?.total || 0} 个`,
				});
			} else {
				toast({
					title: '未找到数据',
					description: '该用户暂无粉丝数据或数据不可见',
				});
			}
		} catch (error) {
			console.error('Failed to fetch TikTok user fans:', error);
			toast({
				variant: 'destructive',
				title: '获取数据失败',
				description: (error as ApiError) ? error.message : '获取用户粉丝失败，请稍后重试',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleLoadMore = async () => {
		if (isLoadingMore || !hasMore || !userInfo?.user?.secUid || cursor === 0) return;

		setIsLoadingMore(true);
		try {
			const response = await apiRequest<TikTokUserFansResponse>('/tiktok/user/fans', {
				method: 'POST',
				body: JSON.stringify({
					secUid: userInfo.user.secUid,
					minCursor: cursor.toString(),
				}),
			});

			const moreFans = response.data?.userList || [];
			const uniqueItems = moreFans.filter((item) => !fans.some((fan) => fan.user.id === item.user.id));

			setFans([...fans, ...uniqueItems]);
			setHasMore(response.data?.hasMore || false);
			setCursor(response.data?.minCursor || cursor);

			if (uniqueItems.length > 0) {
				toast({
					title: '加载成功',
					description: `已加载额外 ${uniqueItems.length} 个粉丝`,
				});
			}
		} catch (error) {
			console.error('Failed to load more fans:', error);
			toast({
				variant: 'destructive',
				title: '加载更多数据失败',
				description: (error as ApiError) ? error.message : '获取更多粉丝失败，请稍后重试',
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	const handleExport = () => {
		if (fans.length === 0) return;

		const rows = [
			['用户ID', '用户名', '昵称', '签名', '粉丝数', '关注数', '获赞数', '作品数', '是否认证', '是否私密账号', '头像链接', '主页链接'],
			...fans.map((fan) => [
				fan.user.id,
				fan.user.uniqueId,
				fan.user.nickname,
				fan.user.signature,
				fan.stats.followerCount,
				fan.stats.followingCount,
				fan.stats.heartCount,
				fan.stats.videoCount,
				fan.user.verified ? '是' : '否',
				fan.user.privateAccount ? '是' : '否',
				fan.user.avatarLarger,
				`https://www.tiktok.com/@${fan.user.uniqueId}`,
			]),
		];

		exportArrayToCSV(rows, `TikTok用户粉丝_${userInfo?.user?.nickname || 'unknown'}_${new Date().toLocaleString()}.csv`);

		toast({
			title: '导出成功',
			description: `已导出 ${fans.length} 个粉丝数据`,
		});
	};

	return (
		<TooltipProvider>
			<div className="p-4 space-y-4">
				<Card className="p-6">
					<div className="flex gap-2 mb-4">
						<Input
							placeholder="请输入TikTok用户主页链接或用户名，例如：https://www.tiktok.com/@username 或 username"
							value={userUrl}
							onChange={(e) => setUserUrl(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
						/>
						<Button onClick={handleSearch} disabled={isLoading}>
							<Search className="w-4 h-4 mr-2" />
							{isLoading ? '搜索中...' : '搜索'}
						</Button>
					</div>

					{userInfo && (
						<div className="mt-4 p-4 bg-gray-50 rounded-lg">
							<div className="flex items-center space-x-4">
								{isShowingExampleData ? (
									<ExampleImageSVG width={64} height={64} type="avatar" />
								) : (
									<img
										src={userInfo.user.avatarLarger}
										alt={userInfo.user.nickname}
										className="w-16 h-16 rounded-full"
										referrerPolicy="no-referrer"
									/>
								)}
								<div>
									<div className="flex items-center gap-2">
										<h3 className="text-lg font-semibold">{userInfo.user.nickname}</h3>
										{isShowingExampleData && <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">示例数据</span>}
									</div>
									<p className="text-gray-600">@{userInfo.user.uniqueId}</p>
									<p className="text-sm text-gray-500">{userInfo.user.signature}</p>
									<div className="flex space-x-4 text-sm text-gray-600 mt-1">
										<span>关注: {userInfo.stats.followingCount.toLocaleString()}</span>
										<span>粉丝: {userInfo.stats.followerCount.toLocaleString()}</span>
										<span>获赞: {userInfo.stats.heartCount.toLocaleString()}</span>
										<span>作品: {userInfo.stats.videoCount.toLocaleString()}</span>
									</div>
								</div>
							</div>
						</div>
					)}

					{/* Loading状态显示 */}
					{isLoading && (
						<div className="mt-6 flex flex-col items-center justify-center py-12">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
							<p className="text-gray-600">正在获取用户信息和粉丝数据...</p>
						</div>
					)}

					{/* 粉丝列表 */}
					{!isLoading && fans.length > 0 && (
						<div className="mt-6 space-y-2">
							<div className="flex justify-between items-center">
								<div className="flex items-center gap-2">
									<h2 className="text-lg font-semibold">
										用户粉丝 - 已加载 {fans.length} 个{total > 0 && ` / 总计 ${total.toLocaleString()} 个`}
									</h2>
									{isShowingExampleData && <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">示例数据</span>}
								</div>
								<div className="flex gap-2">
									{hasMore && (
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													variant="outline"
													onClick={isShowingExampleData ? undefined : handleLoadMore}
													disabled={isShowingExampleData || isLoadingMore}
												>
													<Users className="w-4 h-4 mr-2" />
													{isLoadingMore ? '加载中...' : '加载更多'}
												</Button>
											</TooltipTrigger>
											<TooltipContent>
												<p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多粉丝数据'}</p>
											</TooltipContent>
										</Tooltip>
									)}
									<Tooltip>
										<TooltipTrigger asChild>
											<Button variant="outline" onClick={isShowingExampleData ? undefined : handleExport} disabled={isShowingExampleData}>
												<Download className="w-4 h-4 mr-2" />
												导出
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>{isShowingExampleData ? '示例数据不支持导出' : '导出粉丝数据'}</p>
										</TooltipContent>
									</Tooltip>
								</div>
							</div>
							<div className="border rounded-md">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead className="w-[80px]">头像</TableHead>
											<TableHead className="w-[180px]">用户信息</TableHead>
											<TableHead className="w-[150px]">签名</TableHead>
											<TableHead className="w-[70px]">粉丝数</TableHead>
											<TableHead className="w-[70px]">关注数</TableHead>
											<TableHead className="w-[70px]">获赞数</TableHead>
											<TableHead className="w-[70px]">作品数</TableHead>
											<TableHead className="w-[60px]">认证</TableHead>
											<TableHead className="w-[60px]">私密</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{fans.map((fan) => (
											<TableRow key={fan.user.id}>
												<TableCell>
													{isShowingExampleData ? (
														<ExampleImageSVG width={40} height={40} type="avatar" />
													) : (
														<img
															src={fan.user.avatarThumb}
															alt={fan.user.nickname}
															className="w-10 h-10 rounded-full object-cover"
															loading="lazy"
															referrerPolicy="no-referrer"
														/>
													)}
												</TableCell>
												<TableCell className="max-w-[180px]">
													<div>
														{isShowingExampleData ? (
															<span className="font-medium text-sm text-gray-700 truncate block" title={fan.user.nickname}>
																{fan.user.nickname}
															</span>
														) : (
															<a
																href={`https://www.tiktok.com/@${fan.user.uniqueId}`}
																target="_blank"
																rel="noopener noreferrer"
																className="text-blue-600 hover:underline font-medium text-sm truncate block"
																title={fan.user.nickname}
															>
																{fan.user.nickname}
															</a>
														)}
														<p className="text-xs text-gray-500 truncate">@{fan.user.uniqueId}</p>
													</div>
												</TableCell>
												<TableCell className="max-w-[150px]">
													<div className="line-clamp-2 text-xs" title={fan.user.signature || '无签名'}>
														{fan.user.signature || '无签名'}
													</div>
												</TableCell>
												<TableCell>{fan.stats.followerCount.toLocaleString()}</TableCell>
												<TableCell>{fan.stats.followingCount.toLocaleString()}</TableCell>
												<TableCell>{fan.stats.heartCount.toLocaleString()}</TableCell>
												<TableCell>{fan.stats.videoCount.toLocaleString()}</TableCell>
												<TableCell>
													<span
														className={`px-2 py-1 rounded-full text-xs ${
															fan.user.verified ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
														}`}
													>
														{fan.user.verified ? '已认证' : '未认证'}
													</span>
												</TableCell>
												<TableCell>
													<span
														className={`px-2 py-1 rounded-full text-xs ${
															fan.user.privateAccount ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
														}`}
													>
														{fan.user.privateAccount ? '私密' : '公开'}
													</span>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						</div>
					)}

					{/* 空状态提示 */}
					{!isLoading && fans.length === 0 && userInfo && (
						<div className="mt-6 flex flex-col items-center justify-center py-12 text-gray-500">
							<Users className="w-12 h-12 mb-4 text-gray-300" />
							<p className="text-lg font-medium mb-2">暂无粉丝数据</p>
							<p className="text-sm">该用户暂无粉丝数据或数据不可见</p>
						</div>
					)}
				</Card>
			</div>
		</TooltipProvider>
	);
};

export default TikTokUserFans;
