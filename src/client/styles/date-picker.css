/* 自定义日期选择器样式 */

/* 隐藏原生的日期选择器图标 */
.custom-date-picker::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    right: 28px; /* 避免与清除按钮重叠 */
    width: calc(100% - 28px);
    height: 100%;
    cursor: pointer;
    z-index: 1;
}

/* Chrome/Safari 日期选择器输入框样式 */
.custom-date-picker::-webkit-datetime-edit {
    padding: 0;
    padding-right: 28px; /* 为清除按钮留出空间，但不要太多 */
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.25rem;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.custom-date-picker::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
    display: flex;
    align-items: center;
    width: 100%;
    gap: 0; /* 移除字段之间的间隙 */
    justify-content: flex-start;
}

.custom-date-picker::-webkit-datetime-edit-text {
    color: #6b7280;
    padding: 0 1px; /* 减少分隔符的间距 */
    font-weight: 400;
    font-size: 0.875rem;
}

/* 日期字段样式 */
.custom-date-picker::-webkit-datetime-edit-month-field,
.custom-date-picker::-webkit-datetime-edit-day-field,
.custom-date-picker::-webkit-datetime-edit-year-field {
    color: #374151;
    background: transparent;
    border: none;
    padding: 1px 2px; /* 减少字段内边距 */
    margin: 0; /* 移除默认边距 */
    border-radius: 3px;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 0.875rem;
    min-width: auto; /* 允许字段自适应宽度 */
    text-align: center;
}

.custom-date-picker::-webkit-datetime-edit-month-field:focus,
.custom-date-picker::-webkit-datetime-edit-day-field:focus,
.custom-date-picker::-webkit-datetime-edit-year-field:focus {
    background-color: #dbeafe;
    outline: none;
    color: #1d4ed8;
}

.custom-date-picker::-webkit-datetime-edit-month-field:hover,
.custom-date-picker::-webkit-datetime-edit-day-field:hover,
.custom-date-picker::-webkit-datetime-edit-year-field:hover {
    background-color: #f3f4f6;
}

/* Firefox 日期选择器样式 */
.custom-date-picker[type="date"] {
    -moz-appearance: none;
    appearance: none;
}

/* 占位符样式 */
.custom-date-picker::-webkit-input-placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
}

.custom-date-picker::-moz-placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
    opacity: 1;
}

.custom-date-picker::placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
}

/* 禁用状态样式 */
.custom-date-picker:disabled {
    background-color: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
}

.custom-date-picker:disabled::-webkit-datetime-edit-month-field,
.custom-date-picker:disabled::-webkit-datetime-edit-day-field,
.custom-date-picker:disabled::-webkit-datetime-edit-year-field {
    color: #9ca3af;
    background: transparent;
}

.custom-date-picker:disabled::-webkit-datetime-edit-text {
    color: #d1d5db;
}

/* 日期选择器弹窗样式（有限支持） */
.custom-date-picker::-webkit-calendar-picker-indicator:hover {
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
}

/* 全局日期选择器弹窗样式 */
input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
    opacity: 0;
}

/* 改善日期选择器在不同浏览器中的表现 */
@supports (-webkit-appearance: none) {
    .custom-date-picker {
        -webkit-appearance: none;
        appearance: none;
    }
}

@supports (-moz-appearance: none) {
    .custom-date-picker {
        -moz-appearance: none;
        appearance: none;
    }
}

/* 响应式设计 */
@media (max-width: 640px) {
    .custom-date-picker::-webkit-datetime-edit {
        font-size: 0.8125rem;
    }
    
    .custom-date-picker::-webkit-datetime-edit-month-field,
    .custom-date-picker::-webkit-datetime-edit-day-field,
    .custom-date-picker::-webkit-datetime-edit-year-field {
        padding: 1px 3px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .custom-date-picker::-webkit-datetime-edit-month-field:focus,
    .custom-date-picker::-webkit-datetime-edit-day-field:focus,
    .custom-date-picker::-webkit-datetime-edit-year-field:focus {
        background-color: #000;
        color: #fff;
        outline: 2px solid #fff;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .custom-date-picker::-webkit-datetime-edit {
        color: #f3f4f6;
    }
    
    .custom-date-picker::-webkit-datetime-edit-text {
        color: #9ca3af;
    }
    
    .custom-date-picker::-webkit-datetime-edit-month-field,
    .custom-date-picker::-webkit-datetime-edit-day-field,
    .custom-date-picker::-webkit-datetime-edit-year-field {
        color: #f3f4f6;
    }
    
    .custom-date-picker::-webkit-datetime-edit-month-field:focus,
    .custom-date-picker::-webkit-datetime-edit-day-field:focus,
    .custom-date-picker::-webkit-datetime-edit-year-field:focus {
        background-color: #1e40af;
        color: #dbeafe;
    }
    
    .custom-date-picker::-webkit-datetime-edit-month-field:hover,
    .custom-date-picker::-webkit-datetime-edit-day-field:hover,
    .custom-date-picker::-webkit-datetime-edit-year-field:hover {
        background-color: #374151;
    }
}
