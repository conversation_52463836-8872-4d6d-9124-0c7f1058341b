import React, { useState, use<PERSON>emo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Download, Video, Music, Image, VolumeX, Loader2 } from 'lucide-react';
import { useToast } from './ui/use-toast';

export interface DownloadOption {
	type: 'video' | 'audio' | 'thumbnail';
	quality?: string;
	format?: string;
	formatId?: string;
	url?: string;
}

interface VideoFormat {
	format_id: string;
	format_note: string;
	ext: string;
	protocol: string;
	acodec: string;
	vcodec: string;
	url: string;
	width: number | null;
	height: number | null;
	fps: number | null;
	audio_channels: number | null;
	quality: number;
	has_drm: boolean;
	tbr: number;
	filesize_approx: number | null;
	resolution: string;
	aspect_ratio: number | null;
	format: string;
}

interface VideoThumbnail {
	url: string;
	height: number;
	width: number;
	id: string;
	resolution: string;
}

interface VideoDetail {
	id: string;
	title: string;
	formats: VideoFormat[];
	thumbnails: VideoThumbnail[];
}

interface MediaDownloadDialogProps {
	isOpen: boolean;
	onClose: () => void;
	mediaUrl: string;
	videoDetail?: VideoDetail;
	onDownload?: (url: string, option: DownloadOption) => Promise<void>;
	title?: string;
}

const MediaDownloadDialog: React.FC<MediaDownloadDialogProps> = ({
	isOpen,
	onClose,
	mediaUrl,
	videoDetail,
	onDownload,
	title = '选择下载选项'
}) => {
	const [downloadOption, setDownloadOption] = useState<DownloadOption>({
		type: 'video',
		quality: '720p',
		format: 'mp4'
	});
	const [isDownloading, setIsDownloading] = useState(false);
	const { toast } = useToast();

	// 从videoDetail中提取可用的格式选项
	const availableFormats = useMemo(() => {
		if (!videoDetail?.formats) {
			return {
				videoFormats: [],
				audioFormats: [],
				thumbnails: []
			};
		}

		// 过滤视频格式（有视频编码的格式，可以有音频也可以没有音频）
		const videoFormats = videoDetail.formats
			.filter(format => 
				format.vcodec !== 'none' && 
				format.height && 
				format.width &&
				!format.format_note?.includes('storyboard')
			)
			.sort((a, b) => {
				// 首先按分辨率降序排列
				if ((b.height || 0) !== (a.height || 0)) {
					return (b.height || 0) - (a.height || 0);
				}
				// 同一分辨率时，优先mp4格式
				if (a.ext === 'mp4' && b.ext !== 'mp4') return -1;
				if (b.ext === 'mp4' && a.ext !== 'mp4') return 1;
				return 0;
			})
			.reduce((unique, format) => {
				// 去除同一分辨率的重复格式，优先保留mp4
				const existingFormat = unique.find(f => f.height === format.height);
				if (!existingFormat) {
					unique.push(format);
				} else if (format.ext === 'mp4' && existingFormat.ext !== 'mp4') {
					// 如果当前格式是mp4而已存在的不是，则替换
					const index = unique.indexOf(existingFormat);
					unique[index] = format;
				}
				return unique;
			}, [] as typeof videoDetail.formats);

		// 过滤音频格式（无视频编码且有音频编码）
		const audioFormats = videoDetail.formats
			.filter(format => 
				format.vcodec === 'none' && 
				format.acodec !== 'none' &&
				format.tbr > 0
			)
			.sort((a, b) => b.tbr - a.tbr); // 按比特率降序排列

		// 获取缩略图（过滤掉没有width和height的）
		const thumbnails = (videoDetail.thumbnails || [])
			.filter(thumbnail => 
				thumbnail.width && 
				thumbnail.height && 
				thumbnail.width > 0 && 
				thumbnail.height > 0
			);

		return {
			videoFormats,
			audioFormats,
			thumbnails
		};
	}, [videoDetail]);

	// 当对话框打开且有videoDetail时，初始化默认选项
	React.useEffect(() => {
		if (isOpen && videoDetail && availableFormats.videoFormats.length > 0) {
			const defaultFormat = availableFormats.videoFormats[0];
			setDownloadOption({
				type: 'video',
				quality: `${defaultFormat.height}p`,
				format: defaultFormat.ext,
				formatId: defaultFormat.format_id,
				url: defaultFormat.url
			});
		}
		// 重置下载状态
		if (!isOpen) {
			setIsDownloading(false);
		}
	}, [isOpen, videoDetail, availableFormats.videoFormats]);

	const handleConfirmDownload = async () => {
		if (!mediaUrl || isDownloading) return;

		setIsDownloading(true);
		try {
			toast({
				title: '开始下载',
				description: `正在准备${downloadOption.type === 'video' ? '视频' : downloadOption.type === 'audio' ? '音频' : '封面'}下载，请稍候...`,
			});

			if (onDownload) {
				await onDownload(mediaUrl, downloadOption);
			} else {
				// 默认实现：提示用户功能开发中
				toast({
					title: '功能开发中',
					description: `${downloadOption.type === 'video' ? '视频' : downloadOption.type === 'audio' ? '音频' : '封面'}下载功能正在开发中，敬请期待`,
				});
			}

			onClose();
		} catch (error) {
			console.error('Failed to download:', error);
			toast({
				variant: 'destructive',
				title: '下载失败',
				description: '下载失败，请稍后重试',
			});
		} finally {
			setIsDownloading(false);
		}
	};

	const handleTypeChange = (type: 'video' | 'audio' | 'thumbnail') => {
		switch (type) {
			case 'video':
				if (availableFormats.videoFormats.length > 0) {
					const defaultFormat = availableFormats.videoFormats[0];
					setDownloadOption({ 
						type: 'video', 
						quality: `${defaultFormat.height}p`, 
						format: defaultFormat.ext,
						formatId: defaultFormat.format_id,
						url: defaultFormat.url
					});
				} else {
					setDownloadOption({ type: 'video', quality: '720p', format: 'mp4' });
				}
				break;
			case 'audio':
				if (availableFormats.audioFormats.length > 0) {
					const defaultFormat = availableFormats.audioFormats[0];
					setDownloadOption({ 
						type: 'audio', 
						quality: `${Math.round(defaultFormat.tbr)}k`, 
						format: defaultFormat.ext,
						formatId: defaultFormat.format_id,
						url: defaultFormat.url
					});
				} else {
					setDownloadOption({ type: 'audio', quality: '128k', format: 'm4a' });
				}
				break;
			case 'thumbnail':
				if (availableFormats.thumbnails.length > 0) {
					const defaultThumbnail = availableFormats.thumbnails
						.sort((a, b) => (b.width * b.height) - (a.width * a.height))[0]; // 选择最大分辨率
					setDownloadOption({ 
						type: 'thumbnail', 
						format: 'jpg',
						quality: `${defaultThumbnail.width}x${defaultThumbnail.height}`,
						url: defaultThumbnail.url
					});
				} else {
					setDownloadOption({ type: 'thumbnail', format: 'jpg' });
				}
				break;
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
				</DialogHeader>
				<div className="space-y-6 py-4">
					{/* 下载类型选择 */}
					<div className="space-y-3">
						<label className="text-sm font-medium">下载类型</label>
						<div className={`grid gap-3 ${availableFormats.audioFormats.length > 0 ? 'grid-cols-3' : 'grid-cols-2'}`}>
							<Button
								variant={downloadOption.type === 'video' ? 'default' : 'outline'}
								size="sm"
								onClick={() => handleTypeChange('video')}
								className="flex flex-col items-center gap-2 h-16"
							>
								<Video className="w-4 h-4" />
								<span className="text-xs">视频</span>
							</Button>
							{availableFormats.audioFormats.length > 0 && (
								<Button
									variant={downloadOption.type === 'audio' ? 'default' : 'outline'}
									size="sm"
									onClick={() => handleTypeChange('audio')}
									className="flex flex-col items-center gap-2 h-16"
								>
									<Music className="w-4 h-4" />
									<span className="text-xs">音频</span>
								</Button>
							)}
							<Button
								variant={downloadOption.type === 'thumbnail' ? 'default' : 'outline'}
								size="sm"
								onClick={() => handleTypeChange('thumbnail')}
								className="flex flex-col items-center gap-2 h-16"
							>
								<Image className="w-4 h-4" />
								<span className="text-xs">封面</span>
							</Button>
						</div>
					</div>

					{/* 质量/分辨率选择 */}
					<div className="space-y-3">
						<label className="text-sm font-medium">
							{downloadOption.type === 'video' ? '视频分辨率' : downloadOption.type === 'audio' ? '音频质量' : '缩略图尺寸'}
						</label>
						<Select
							value={downloadOption.quality}
							onValueChange={(value) => {
					if (downloadOption.type === 'video') {
						// 由于去重后每个分辨率只有一个格式，直接查找即可
						const selectedFormat = availableFormats.videoFormats.find(f => `${f.height}p` === value);
						if (selectedFormat) {
							setDownloadOption({ 
								...downloadOption, 
								quality: value,
								format: selectedFormat.ext,
								formatId: selectedFormat.format_id,
								url: selectedFormat.url
							});
						}
					} else if (downloadOption.type === 'audio') {
						const selectedFormat = availableFormats.audioFormats.find(f => `${Math.round(f.tbr)}k` === value);
						if (selectedFormat) {
							setDownloadOption({ 
								...downloadOption, 
								quality: value,
								format: selectedFormat.ext,
								formatId: selectedFormat.format_id,
								url: selectedFormat.url
							});
						}
					} else {
						const selectedThumbnail = availableFormats.thumbnails.find(t => `${t.width}x${t.height}` === value);
						if (selectedThumbnail) {
							setDownloadOption({ 
								...downloadOption, 
								quality: value,
								url: selectedThumbnail.url
							});
						}
					}
				}}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{downloadOption.type === 'video' && availableFormats.videoFormats.length > 0 ? (
								availableFormats.videoFormats.map((format) => (
									<SelectItem key={format.format_id} value={`${format.height}p`}>
										<div className="flex items-center justify-between w-full">
											<span>{format.height}p ({format.ext.toUpperCase()}) - {format.format_note || '标准'}</span>
											{format.acodec === 'none' && (
												<div title="无音频">
													<VolumeX className="w-4 h-4 text-red-500 ml-2" />
												</div>
											)}
										</div>
									</SelectItem>
								))
							) : downloadOption.type === 'video' ? (
									<SelectItem value="720p">720p (无可用格式)</SelectItem>
								) : downloadOption.type === 'audio' && availableFormats.audioFormats.length > 0 ? (
									availableFormats.audioFormats.map((format) => (
										<SelectItem key={format.format_id} value={`${Math.round(format.tbr)}k`}>
											{Math.round(format.tbr)}kbps ({format.ext.toUpperCase()}) - {format.format_note || '标准'}
										</SelectItem>
									))
								) : downloadOption.type === 'audio' ? (
									<SelectItem value="128k">128kbps (无可用格式)</SelectItem>
								) : availableFormats.thumbnails.length > 0 ? (
											availableFormats.thumbnails
												.sort((a, b) => (b.width * b.height) - (a.width * a.height))
												.map((thumbnail) => (
													<SelectItem key={thumbnail.id} value={`${thumbnail.width}x${thumbnail.height}`}>
														{thumbnail.width} × {thumbnail.height}
													</SelectItem>
												))
								) : (
									<SelectItem value="default">无可用选项</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					{/* 格式选择 */}
					<div className="space-y-3">
						<label className="text-sm font-medium">文件格式</label>
						<div className="p-3 bg-gray-50 rounded-md">
							<div className="text-sm text-gray-600">
								当前格式: <span className="font-medium">{downloadOption.format?.toUpperCase() || '未选择'}</span>
							</div>
							{downloadOption.formatId && (() => {
								const currentFormat = downloadOption.type === 'video' 
									? availableFormats.videoFormats.find(f => f.format_id === downloadOption.formatId)
									: downloadOption.type === 'audio'
									? availableFormats.audioFormats.find(f => f.format_id === downloadOption.formatId)
									: null;
								const fileSize = currentFormat?.filesize_approx;
								return (
									<div className="text-xs text-gray-500 mt-1">
										文件大小: {fileSize ? `约 ${(fileSize / 1024 / 1024).toFixed(1)} MB` : '未知'}
									</div>
								);
							})()}
							{downloadOption.url && (
								<div className="text-xs text-gray-500 mt-1">
									下载链接已准备就绪
								</div>
							)}
						</div>
					</div>

					{/* 操作按钮 */}
					<div className="flex justify-end gap-3 pt-4">
						<Button
							variant="outline"
							onClick={onClose}
							disabled={isDownloading}
						>
							取消
						</Button>
						<Button onClick={handleConfirmDownload} disabled={isDownloading}>
							{isDownloading ? (
								<Loader2 className="w-4 h-4 mr-2 animate-spin" />
							) : (
								<Download className="w-4 h-4 mr-2" />
							)}
							{isDownloading ? '下载中...' : '开始下载'}
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default MediaDownloadDialog;
export type { MediaDownloadDialogProps };