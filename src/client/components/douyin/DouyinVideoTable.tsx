import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Trash2, Download, FileText, Loader2, AlertCircle, BarChart3 } from 'lucide-react';
import { formatDuration, formatNumber, formatTimestamp } from '../../lib/utils';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';

interface DouyinVideoInfo {
  id: string;
  url: string;
  title?: string;
  author?: {
    nickname?: string;
    unique_id?: string;
    sec_uid?: string;
  };
  statistics?: {
    digg_count?: number;
    comment_count?: number;
    share_count?: number;
    collect_count?: number;
    recommend_count?: number;
    play_count?: number;
    vv_cnt?: number;
    finish_rate?: number;
  };
  video?: {
    duration?: number;
  };
  create_time?: number;
  loading?: boolean;
  error?: string;
  showStarData?: boolean;
  starDataLoading?: boolean;
}

interface DouyinVideoTableProps {
  videos: DouyinVideoInfo[];
  onRemove: (index: number) => void;
  onDownload: (video: DouyinVideoInfo) => void;
  onSubtitleDownload: (video: DouyinVideoInfo) => void;
  onToggleStarData?: (index: number) => void;
  isShowingExampleData?: boolean;
}

export function DouyinVideoTable({ videos, onRemove, onDownload, onSubtitleDownload, onToggleStarData, isShowingExampleData = false }: DouyinVideoTableProps) {
  const formatCount = (count: number | undefined): string => {
    if (!count) return '0';
    return formatNumber(count);
  };

  const truncateText = (text: string | undefined, maxLength: number = 50): string => {
    if (!text) return '-';
    return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">标题</TableHead>
            <TableHead className="w-[120px]">作者</TableHead>
            <TableHead className="w-[80px]">时长</TableHead>
            <TableHead className="w-[70px]">点赞</TableHead>
            <TableHead className="w-[70px]">评论</TableHead>
            <TableHead className="w-[70px]">分享</TableHead>
            <TableHead className="w-[70px]">收藏</TableHead>
            <TableHead className="w-[70px]">推荐</TableHead>
            <TableHead className="w-[70px]">播放量</TableHead>
            <TableHead className="w-[60px]">完播率</TableHead>
            <TableHead className="w-[120px]">发布时间</TableHead>
            <TableHead className="w-[80px]">状态</TableHead>
            <TableHead className="w-[150px]">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {videos.map((video, index) => (
            <TableRow key={video.id || index}>
              <TableCell>
                <div className="space-y-1">
                  <div className="font-medium text-sm" title={video.title}>
                    {isShowingExampleData ? (
                      <span className="text-gray-700">
                        {truncateText(video.title)}
                      </span>
                    ) : video.id ? (
                      <a
                        href={`https://www.douyin.com/video/${video.id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {truncateText(video.title)}
                      </a>
                    ) : (
                      truncateText(video.title)
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground" title={video.url}>
                    {truncateText(video.url, 30)}
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="space-y-1">
                  <div className="text-sm font-medium" title={video.author?.nickname}>
                    {isShowingExampleData ? (
                      <span className="text-gray-700">
                        {truncateText(video.author?.nickname)}
                      </span>
                    ) : video.author?.sec_uid ? (
                      <a
                        href={`https://www.douyin.com/user/${video.author.sec_uid}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {truncateText(video.author?.nickname)}
                      </a>
                    ) : (
                      truncateText(video.author?.nickname)
                    )}
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatDuration(Number(video.video?.duration) / 1000) || '-'}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatCount(video.statistics?.digg_count)}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatCount(video.statistics?.comment_count)}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatCount(video.statistics?.share_count)}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatCount(video.statistics?.collect_count)}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatCount(video.statistics?.recommend_count)}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {video.showStarData ? formatCount(video.statistics?.vv_cnt || 0) : '-'}
                </span>
              </TableCell>
              <TableCell>
                <span className="text-sm">
                  {video.showStarData && video.statistics?.finish_rate 
                    ? `${(video.statistics.finish_rate * 100).toFixed(2)}%`
                    : '-'}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-sm">
                  {formatTimestamp(video.create_time) || '-'}
                </span>
              </TableCell>
              
              <TableCell>
                {video.loading ? (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    加载中
                  </Badge>
                ) : video.error ? (
                  <Badge variant="destructive" className="flex items-center gap-1" title={video.error}>
                    <AlertCircle className="h-3 w-3" />
                    失败
                  </Badge>
                ) : (
                  <Badge variant="default" className="bg-green-500">
                    成功
                  </Badge>
                )}
              </TableCell>
              
              <TableCell>
                <div className="flex gap-2">
                  {/* <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDownload(video)}
                    disabled={video.loading || !!video.error}
                    className="text-xs"
                  >
                    {video.loading ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
                        加载中
                      </>
                    ) : (
                      '视频下载'
                    )}
                  </Button> */}
                  
                  {onToggleStarData && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={isShowingExampleData ? undefined : () => onToggleStarData(index)}
                          disabled={isShowingExampleData || video.starDataLoading}
                          className="text-xs"
                          title={video.showStarData ? "隐藏星图数据" : "查看星图数据"}
                        >
                          {video.starDataLoading ? (
                            <>
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                              加载中
                            </>
                          ) : (
                            <>
                              <BarChart3 className="w-3 h-3 mr-1" />
                              {video.showStarData ? '隐藏星图数据' : '查看星图数据'}
                            </>
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{isShowingExampleData ? '示例数据不支持查看星图数据' : (video.showStarData ? '隐藏星图数据' : '查看星图数据')}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {videos.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          暂无视频数据
        </div>
      )}
    </div>
  );
}