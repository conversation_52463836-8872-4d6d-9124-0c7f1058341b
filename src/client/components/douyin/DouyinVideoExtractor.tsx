import React, { useState, useRef, useEffect } from 'react';
import { Card } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Upload, Download, Search, X, BarChart3 } from 'lucide-react';
import { useDouyinVideoHandler } from './useDouyinVideoHandler';
import * as XLSX from 'xlsx';
import { useToast } from '../ui/use-toast';
import { douyinApi, DouyinVideoDetailResponse, DouyinStarDataResponse } from '../../lib/api';
import { DouyinVideoTable } from './DouyinVideoTable';
import { formatDuration } from '../../lib/utils';
import { Toolt<PERSON>, Toolt<PERSON>Content, Toolt<PERSON><PERSON>rovider, TooltipTrigger } from '../ui/tooltip';

interface DouyinVideoInfo {
  id: string;
  url: string;
  title?: string;
  author?: {
    nickname?: string;
    unique_id?: string;
    sec_uid?: string;
  };
  statistics?: {
    digg_count?: number;
    comment_count?: number;
    share_count?: number;
    collect_count?: number;
    recommend_count?: number;
    play_count?: number;
    vv_cnt?: number;
    finish_rate?: number;
  };
  video?: {
    duration?: number;
  };
  create_time?: number;
  formatted_create_time?: string;
  loading?: boolean;
  error?: string;
  showStarData?: boolean;
  starDataLoading?: boolean;
}

export function DouyinVideoExtractor() {
  const [videoUrls, setVideoUrls] = useState('');
  const [videoInfos, setVideoInfos] = useState<DouyinVideoInfo[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isShowingExampleData, setIsShowingExampleData] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { handleDownload, handleSubtitleDownload } = useDouyinVideoHandler();

  // 加载示例数据
  const loadExampleData = async () => {
    try {
      // 加载详情示例数据
      const response = await fetch('/test/data/douyin/dyDetail.json');
      const data = await response.json();

      // 转换视频数据
      const detail = data.data.aweme_detail;
      const exampleVideoInfo: DouyinVideoInfo = {
        id: detail.aweme_id || 'example_video_id',
        url: 'https://www.douyin.com/video/example',
        title: detail.desc || '示例视频标题',
        author: {
          nickname: detail.author?.nickname || '示例用户',
          sec_uid: detail.author?.sec_uid || 'example_sec_uid',
          unique_id: detail.author?.unique_id || 'example_unique_id'
        },
        statistics: {
          digg_count: detail.statistics?.digg_count || 1234,
          comment_count: detail.statistics?.comment_count || 567,
          share_count: detail.statistics?.share_count || 89,
          collect_count: detail.statistics?.collect_count || 123,
          recommend_count: detail.statistics?.recommend_count || 456
        },
        video: {
          duration: detail.video?.duration || 30000,
        },
        create_time: detail.create_time || Date.now(),
        formatted_create_time: detail.formatted_create_time || new Date().toLocaleString('zh-CN'),
        loading: false,
        showStarData: false,
        starDataLoading: false
      };

      setVideoInfos([exampleVideoInfo]);

      toast({
        title: '示例数据已加载',
        description: '已加载 1 条示例视频数据',
      });
    } catch (error) {
      console.error('Failed to load example data:', error);
      toast({
        variant: 'destructive',
        title: '加载示例数据失败',
        description: '无法加载示例数据，请检查文件是否存在',
      });
    }
  };

  // 组件挂载时加载示例数据
  useEffect(() => {
    loadExampleData();
  }, []);

  // 解析视频URL
  const parseVideoUrls = (text: string): string[] => {
    const lines = text.split('\n').filter(line => line.trim());
    const urls: string[] = [];
    
    for (const line of lines) {
      // 匹配抖音URL
      const douyinUrlRegex = /https?:\/\/(?:v\.douyin\.com\/[A-Za-z0-9_-]+|(?:www\.)?douyin\.com\/[^\s]*|(?:www\.)?iesdouyin\.com\/[^\s]*)/g;
      const matches = line.match(douyinUrlRegex);
      if (matches) {
        urls.push(...matches);
      }
    }
    
    return [...new Set(urls)]; // 去重
  };

  // 获取星图数据
  const fetchStarData = async (url: string, index: number) => {
    try {
      // 设置loading状态
      setVideoInfos(prev => prev.map((info, i) => 
        i === index ? { ...info, starDataLoading: true } : info
      ));

      const response = await douyinApi.getStarData(url) as DouyinStarDataResponse;
      
      if (response.success && response.data?.video_info) {
        const starInfo = response.data.video_info;
        setVideoInfos(prev => prev.map((info, i) => 
          i === index ? {
            ...info,
            statistics: {
              ...info.statistics,
              vv_cnt: starInfo.vv_cnt,
              finish_rate: starInfo.finish_rate
            },
            showStarData: true,
            starDataLoading: false
          } : info
        ));
        
        toast({
          title: '星图数据获取成功',
          description: `已获取视频 "${videoInfos[index]?.title || '未知'}" 的星图数据`
        });
      } else {
        // 获取失败时清除loading状态
        setVideoInfos(prev => prev.map((info, i) => 
          i === index ? { ...info, starDataLoading: false } : info
        ));
      }
    } catch (error) {
      console.error('获取星图数据失败:', error);
      // 获取失败时清除loading状态
      setVideoInfos(prev => prev.map((info, i) => 
        i === index ? { ...info, starDataLoading: false } : info
      ));
      toast({
        title: '星图数据获取失败',
        description: '请稍后重试',
        variant: 'destructive'
      });
    }
  };

  // 获取单个视频信息
  const fetchVideoInfo = async (url: string, index: number) => {
    try {
      setVideoInfos(prev => prev.map((info, i) => 
        i === index ? { ...info, loading: true, error: undefined } : info
      ));

      const response = await douyinApi.getVideoDetail(url) as DouyinVideoDetailResponse;
      
      if (response.success && response.data?.aweme_detail) {
        const detail = response.data.aweme_detail;
        const videoInfo: DouyinVideoInfo = {
          id: detail.aweme_id || `douyin_${Date.now()}_${index}`,
          url,
          title: detail.desc || '无标题',
          author: {
            nickname: detail.author?.nickname,
            sec_uid: detail.author?.sec_uid,
            unique_id: detail.author?.unique_id
          },
          statistics: {
            digg_count: detail.statistics?.digg_count || 0,
            comment_count: detail.statistics?.comment_count || 0,
            share_count: detail.statistics?.share_count || 0,
            collect_count: detail.statistics?.collect_count || 0,
            recommend_count: detail.statistics?.recommend_count || 0
          },
          video: {
            duration: detail.video?.duration,
          },
          create_time: detail.create_time,
          formatted_create_time: detail.formatted_create_time,
          loading: false
        };

        setVideoInfos(prev => prev.map((info, i) => 
          i === index ? videoInfo : info
        ));


      } else {
        throw new Error(response.message || '获取视频信息失败');
      }
    } catch (error) {
      console.error('获取视频信息失败:', error);
      setVideoInfos(prev => prev.map((info, i) => 
        i === index ? { 
          ...info, 
          loading: false, 
          error: error instanceof Error ? error.message : '获取失败'
        } : info
      ));
    }
  };

  // 批量提取视频信息
  const handleExtract = async () => {
    const urls = parseVideoUrls(videoUrls);
    
    if (urls.length === 0) {
      toast({
        title: '提示',
        description: '请输入有效的抖音视频链接',
        variant: 'destructive'
      });
      return;
    }

    if (urls.length > 50) {
      toast({
        title: '提示',
        description: '一次最多处理50个视频链接',
        variant: 'destructive'
      });
      return;
    }

    setIsExtracting(true);
    setIsShowingExampleData(false);

    // 初始化视频信息
    const initialVideoInfos: DouyinVideoInfo[] = urls.map((url, index) => ({
      id: `temp_${index}`,
      url,
      loading: true
    }));
    setVideoInfos(initialVideoInfos);

    // 并发获取视频信息
    const promises = urls.map((url, index) => fetchVideoInfo(url, index));
    
    try {
      await Promise.allSettled(promises);
      
      toast({
        title: '提取完成',
        description: `成功处理 ${urls.length} 个视频链接`
      });
    } catch (error) {
      console.error('批量提取失败:', error);
      toast({
        title: '提取失败',
        description: '部分视频信息获取失败，请检查链接是否有效',
        variant: 'destructive'
      });
    } finally {
      setIsExtracting(false);
    }
  };

  // 清空结果
  const handleClear = () => {
    setVideoInfos([]);
    setVideoUrls('');
  };

  // 移除单个视频
  const handleRemoveVideo = (index: number) => {
    setVideoInfos(prev => prev.filter((_, i) => i !== index));
  };



  // 处理单个视频的星图数据切换
  const handleToggleStarData = async (index: number) => {
    const video = videoInfos[index];
    if (!video || video.loading || video.error || video.starDataLoading) return;
    
    if (!video.showStarData) {
      // 如果当前未显示星图数据，则获取并显示
      await fetchStarData(video.url, index);
    } else {
      // 如果当前显示星图数据，则隐藏
      setVideoInfos(prev => prev.map((info, i) => 
        i === index ? { ...info, showStarData: false } : info
      ));
    }
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];
        
        // 提取URL列（假设在第一列）
        const urls = jsonData
          .slice(1) // 跳过标题行
          .map(row => row[0])
          .filter(url => url && typeof url === 'string' && url.trim())
          .join('\n');
        
        setVideoUrls(prev => prev ? `${prev}\n${urls}` : urls);
        
        toast({
          title: '文件上传成功',
          description: `已导入 ${urls.split('\n').length} 个链接`
        });
      } catch (error) {
        console.error('文件解析失败:', error);
        toast({
          title: '文件解析失败',
          description: '请确保文件格式正确',
          variant: 'destructive'
        });
      }
    };
    reader.readAsArrayBuffer(file);
  };

  // 下载模板
  const downloadTemplate = () => {
    const templateData = [
      ['视频链接'],
      ['https://v.douyin.com/xxxxxx'],
      ['https://www.douyin.com/video/xxxxxx']
    ];
    
    const ws = XLSX.utils.aoa_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '抖音视频链接模板');
    XLSX.writeFile(wb, '抖音视频链接模板.xlsx');
  };

  // 导出结果
  const handleExport = () => {
    if (videoInfos.length === 0) {
      toast({
        title: '提示',
        description: '没有可导出的数据',
        variant: 'destructive'
      });
      return;
    }



    const exportData = videoInfos.map(info => {
      const base = {
        '视频ID': info.id,
        '视频链接': info.url,
        '视频标题': info.title || '',
        '作者昵称': info.author?.nickname || '',
        '作者ID': info.author?.unique_id || '',
        '点赞数': info.statistics?.digg_count || 0,
        '评论数': info.statistics?.comment_count || 0,
        '分享数': info.statistics?.share_count || 0,
        '收藏数': info.statistics?.collect_count || 0,
        '推荐数': info.statistics?.recommend_count || 0,
        '视频时长': formatDuration(Number(info.video?.duration) / 1000) || '',
        '发布时间': info.formatted_create_time || '',
        '状态': info.error ? '失败' : '成功',
        '错误信息': info.error || '',
        '播放量': info.showStarData ? (info.statistics?.vv_cnt || 0) : '-',
        '点赞率': info.showStarData && info.statistics?.digg_count && info.statistics?.vv_cnt 
          ? `${((info.statistics.digg_count / info.statistics.vv_cnt) * 100).toFixed(2)}%`
          : '-',
        '收藏率': info.showStarData && info.statistics?.collect_count && info.statistics?.vv_cnt 
          ? `${((info.statistics.collect_count / info.statistics.vv_cnt) * 100).toFixed(2)}%`
          : '-',
        '完播率': info.showStarData && info.statistics?.finish_rate 
          ? `${(info.statistics.finish_rate * 100).toFixed(2)}%`
          : '-'
      };

      return base;
    });

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    const sheetName = '抖音视频信息';
    const fileName = `抖音视频信息_${new Date().toISOString().slice(0, 10)}.xlsx`;
    
    XLSX.utils.book_append_sheet(wb, ws, sheetName);
    XLSX.writeFile(wb, fileName);

    toast({
      title: '导出成功',
      description: '视频信息已导出到Excel文件'
    });
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
      {/* 输入区域 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="video-urls" className="text-base font-medium">
              视频链接输入
            </Label>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={downloadTemplate} disabled={isExtracting}>
                <Download className="h-4 w-4 mr-2" />
                下载模板
              </Button>
              <Button variant="outline" size="sm" onClick={() => fileInputRef.current?.click()} disabled={isExtracting}>
                <Upload className="h-4 w-4 mr-2" />
                导入Excel
              </Button>
              <input ref={fileInputRef} type="file" accept=".xlsx,.xls" onChange={handleFileUpload} className="hidden" />
            </div>
          </div>

          <Textarea
            id="video-urls"
            placeholder={`请输入抖音视频链接，每行一个...

示例：
https://v.douyin.com/xxxxxx
https://www.douyin.com/video/xxxxxx`}
            value={videoUrls}
            onChange={(e) => setVideoUrls(e.target.value)}
            className="min-h-[120px]"
            disabled={isExtracting}
          />
          <div className="flex justify-end mt-4 gap-2">
            <Button onClick={handleExtract} disabled={isExtracting || !videoUrls.trim()}>
              <Search className="w-4 h-4 mr-2" />
              {isExtracting ? '提取中...' : '提取'}
            </Button>
            <Button variant="outline" onClick={handleClear} disabled={isExtracting || (!videoUrls.trim() && videoInfos.length === 0)}>
              清空
            </Button>
          </div>
        </div>
      </Card>

      {/* 结果区域 */}
      {videoInfos.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">提取结果 ({videoInfos.length})</h3>
              {isShowingExampleData && (
                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                  示例数据
                </span>
              )}
            </div>
            {videoInfos.length > 0 && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    onClick={isShowingExampleData ? undefined : handleExport}
                    disabled={isShowingExampleData}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    导出结果
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isShowingExampleData ? '示例数据不支持导出' : '导出视频数据'}</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          <DouyinVideoTable
            videos={videoInfos}
            onRemove={handleRemoveVideo}
            onDownload={handleDownload}
            onSubtitleDownload={handleSubtitleDownload}
            onToggleStarData={handleToggleStarData}
            isShowingExampleData={isShowingExampleData}
          />
        </Card>
      )}
      
      {/* 显示处理中的视频占位符 */}
      {isExtracting && videoInfos.some(video => video.loading) && (
        <Card className="p-4">
          <div className="text-sm text-gray-600">
            <div className="flex items-center gap-2 mb-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              正在实时获取视频信息...
            </div>
            <div className="text-xs">
              已处理: {videoInfos.filter(v => !v.loading && !v.error).length} / {videoInfos.length}
            </div>
          </div>
        </Card>
      )}
    </div>
    </TooltipProvider>
  );
}