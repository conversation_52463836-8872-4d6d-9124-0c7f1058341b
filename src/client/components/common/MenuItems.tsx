import React from 'react';
import {
  Search,
  MessageSquare,
  User,
  CreditCard,
  Users,
  Shield,
  Video,
  FileText,
  Home,
  ShoppingCart,
  TrendingUp,
  Receipt,
  Youtube,
  Music,
  MessageCircle,
  Zap,
  Camera,
} from 'lucide-react';

export interface MenuItem {
  title: string;
  icon: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  description?: string;
  color?: string;
  link?: string; // 为了兼容 Home.tsx 中的 features
}

// 数据采集工具菜单配置
export const useDataExtractionMenuItems = (): MenuItem[] => [
  {
    title: '红薯取数',
    icon: <Search className="h-5 w-5 text-red-500" />,
    description: '话题搜索笔记、账号笔记提取，获取完整的小红书内容数据和用户信息',
    color: 'text-red-500',
    children: [
      {
        title: '关键词搜索笔记',
        icon: <Search className="h-4 w-4" />,
        path: '/tools/xhs/note-search',
      },
      {
        title: '账号笔记数据提取',
        icon: <User className="h-4 w-4" />,
        path: '/tools/user-notes',
      },
      {
        title: '笔记批量提取数据',
        icon: <MessageSquare className="h-4 w-4" />,
        path: '/tools/xhs/batch-extract',
      },
    ],
  },
  {
    title: 'TikTok取数',
    icon: <Camera className="h-5 w-5 text-black" />,
    description: '账号视频提取、账号粉丝提取、视频批量提取等海外平台数据',
    color: 'text-black',
    children: [
      {
        title: '账号作品数据提取',
        icon: <User className="h-4 w-4" />,
        path: '/tools/tiktok/user-videos',
      },
      {
        title: '账号粉丝提取',
        icon: <Users className="h-4 w-4" />,
        path: '/tools/tiktok/user-fans',
      },
      {
        title: '视频批量提取数据',
        icon: <FileText className="h-4 w-4" />,
        path: '/tools/tiktok/video-extractor',
      },
    ],
  },
  {
    title: '抖音取数',
    icon: <Music className="h-5 w-5 text-purple-500" />,
    description: '账号作品提取、视频批量提取、评论列表提取，全方位抖音数据获取',
    color: 'text-purple-500',
    children: [
      {
        title: '账号作品数据提取',
        icon: <User className="h-4 w-4" />,
        path: '/tools/douyin/user-videos',
      },
      {
        title: '视频批量提取数据',
        icon: <FileText className="h-4 w-4" />,
        path: '/tools/douyin/video-extractor',
      },
      {
        title: '评论列表提取',
        icon: <MessageSquare className="h-4 w-4" />,
        path: '/tools/douyin/comment-extractor',
      },
    ],
  },
  {
    title: 'YouTube取数',
    icon: <Youtube className="h-5 w-5 text-red-600" />,
    description: '账号作品提取、播放列表视频、视频批量提取等全方位数据获取',
    color: 'text-red-600',
    children: [
      {
        title: '账号作品数据提取',
        icon: <User className="h-4 w-4" />,
        path: '/tools/youtube/user-videos',
      },
      {
        title: '播放列表视频数据提取',
        icon: <Video className="h-4 w-4" />,
        path: '/tools/youtube/playlist-videos',
      },
      {
        title: '视频批量提取数据',
        icon: <FileText className="h-4 w-4" />,
        path: '/tools/youtube/video-extractor',
      },
    ],
  },
  {
    title: '快手取数',
    icon: <Zap className="h-5 w-5 text-orange-500" />,
    description: '视频批量提取、评论列表提取，快手平台数据采集工具',
    color: 'text-orange-500',
    children: [
      {
        title: '视频批量提取数据',
        icon: <FileText className="h-4 w-4" />,
        path: '/tools/kuaishou/video-extractor',
      },
      {
        title: '评论列表提取',
        icon: <MessageCircle className="h-4 w-4" />,
        path: '/tools/kuaishou/comments-extractor',
      },
    ],
  },
];

// 侧边栏菜单配置
export const useSidebarMenuItems = (userRole?: string): MenuItem[] => [
  {
    title: '首页',
    icon: <Home className="h-5 w-5" />,
    path: '/dashboard',
  },
  ...useDataExtractionMenuItems(),
  {
    title: '个人中心',
    icon: <User className="h-5 w-5" />,
    children: [
      {
        title: '积分变更记录',
        icon: <TrendingUp className="h-4 w-4" />,
        path: '/profile/points-history',
      },
      {
        title: '付款记录',
        icon: <Receipt className="h-4 w-4" />,
        path: '/profile/payment-history',
      },
      {
        title: '购买积分',
        icon: <ShoppingCart className="h-4 w-4" />,
        path: '/payment/stripe',
      },
    ],
  },
  ...(userRole === 'admin' ? [
    {
      title: '管理员',
      icon: <Shield className="h-5 w-5" />,
      children: [
        {
          title: '用户列表',
          icon: <Users className="h-4 w-4" />,
          path: '/admin/users',
        },
        {
          title: '商品管理',
          icon: <ShoppingCart className="h-4 w-4" />,
          path: '/admin/products',
        },
        {
          title: '积分类型',
          icon: <CreditCard className="h-4 w-4" />,
          path: '/admin/credit-types',
        },
      ],
    },
  ] : []),
];

// 首页功能特性配置
export const useHomeFeatures = (): MenuItem[] => [
  {
    title: "红薯取数",
    icon: <Search className="w-8 h-8 text-red-500" />,
    description: "关键词搜索笔记、账号笔记提取，获取完整的小红书内容数据和用户信息",
    link: "/tools/xhs/note-search"
  },
  {
    title: "抖音取数",
    icon: <Music className="w-8 h-8 text-purple-500" />,
    description: "账号作品提取、视频批量提取、评论列表提取，全方位抖音数据获取",
    link: "/tools/douyin/user-videos"
  },
  {
    title: "TikTok取数",
    icon: <Camera className="w-8 h-8 text-black" />,
    description: "账号视频提取、账号粉丝提取、视频批量提取等海外平台数据",
    link: "/tools/tiktok/user-videos"
  },
  {
    title: "YouTube取数",
    icon: <Youtube className="w-8 h-8 text-red-600" />,
    description: "账号作品提取、播放列表视频、视频批量提取等全方位数据获取",
    link: "/tools/youtube/user-videos"
  },
  {
    title: "快手取数",
    icon: <Zap className="w-8 h-8 text-orange-500" />,
    description: "视频批量提取、评论列表提取，快手平台数据采集工具",
    link: "/tools/kuaishou/video-extractor"
  },
  {
    title: "安全可靠",
    icon: <Shield className="w-8 h-8 text-green-500" />,
    description: "企业级安全保障，数据传输加密，隐私保护完善",
    link: "/dashboard"
  }
];
