import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { useAuth } from '../../contexts/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

interface UserAvatarProps {
  username?: string;
  isAuthenticated: boolean;
}

export function UserAvatar({ username, isAuthenticated }: UserAvatarProps) {
  const navigate = useNavigate();
  const { logout } = useAuth();

  if (!isAuthenticated) {
    return (
      <Button
        variant="ghost"
        onClick={() => navigate('/login')}
        className="h-8 px-4"
      >
        登录
      </Button>
    );
  }

  const firstLetter = username?.[0]?.toUpperCase() || '?';

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-2 cursor-pointer">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            {firstLetter}
          </div>
          <span className="text-sm font-medium">{username}</span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => navigate('/dashboard')}>
          个人中心
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleLogout}>
          退出登录
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}