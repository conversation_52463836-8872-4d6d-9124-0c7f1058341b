import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  ChevronDown,
  ChevronRight,
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useSidebarMenuItems } from '../common/MenuItems';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>(['取数工具']);
  const { user } = useAuth();

  const menuItems = useSidebarMenuItems(user?.role);

  const toggleItem = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title)
        ? prev.filter((item) => item !== title)
        : [...prev, title]
    );
  };

  const isActive = (path?: string) => {
    if (!path) return false;
    return location.pathname === path;
  };

  return (
    <aside className="w-64 border-r bg-card">
      <div className="flex h-16 items-center border-b px-6">
        <Link to="/" className="flex items-center gap-2 text-lg font-semibold hover:text-blue-600 transition-colors">
          <img src="/favicon.png" alt="DataMiner Logo" className="w-6 h-6" />
          DataMiner
        </Link>
      </div>
      <nav className="space-y-1 p-4">
        {menuItems.map((item) => (
          <div key={item.title}>
            {item.children ? (
              <>
                <button
                  onClick={() => toggleItem(item.title)}
                  className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent"
                >
                  <div className="flex items-center gap-3">
                    {item.icon}
                    <span>{item.title}</span>
                  </div>
                  {expandedItems.includes(item.title) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>
                {expandedItems.includes(item.title) && (
                  <div className="ml-6 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.path}
                        to={child.path!}
                        className={`${'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent'} ${isActive(child.path) ? 'bg-accent' : ''}`.trim()}
                      >
                        {child.icon}
                        <span>{child.title}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <Link
                to={item.path!}
                className={`${'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent'} ${isActive(item.path) ? 'bg-accent' : ''}`.trim()}
              >
                {item.icon}
                <span>{item.title}</span>
              </Link>
            )}
          </div>
        ))}
      </nav>
    </aside>
  );
};

export { Sidebar };