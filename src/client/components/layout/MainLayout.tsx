import React from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { UserAvatar } from './UserAvatar';
import { useAuth } from '../../contexts/AuthContext';

export function MainLayout() {
  const { user, isLoading } = useAuth();
  const isAuthenticated = !!user;
  const username = user?.username || '';

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <header className="h-16 border-b px-6 flex items-center justify-end">
          <UserAvatar isAuthenticated={isAuthenticated} username={username} />
        </header>
        <main className="flex-1 overflow-y-auto p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
} 