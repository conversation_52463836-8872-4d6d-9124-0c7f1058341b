import React, { useState } from 'react';
import { youtubeApi } from '../../lib/api';
import { useToast } from '../ui/use-toast';
import { buildProxyUrl } from '../../lib/buildPorxy';
import { DownloadOption } from '../MediaDownloadDialog';
import { YouTubeVideoDetail } from './YouTubeVideoTable';
import { YouTubeVideo } from '../../lib/api';

interface UseYouTubeVideoHandlerProps {
	videos: YouTubeVideo[];
	videoDetails: Map<string, YouTubeVideoDetail>;
	setVideoDetails: React.Dispatch<React.SetStateAction<Map<string, YouTubeVideoDetail>>>;
	loadingVideoDetails: Set<string>;
	setLoadingVideoDetails: React.Dispatch<React.SetStateAction<Set<string>>>;
	toast: any;
}

export const useYouTubeVideoHandler = ({
	videos,
	videoDetails,
	setVideoDetails,
	loadingVideoDetails,
	setLoadingVideoDetails,
	toast,
}: UseYouTubeVideoHandlerProps) => {
	// 内部状态管理
	const [selectedVideoUrl, setSelectedVideoUrl] = useState('');
	const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
	const [selectedSubtitleVideoUrl, setSelectedSubtitleVideoUrl] = useState('');
	const [isSubtitleDialogOpen, setIsSubtitleDialogOpen] = useState(false);

	const handleDownloadVideo = async (videoUrl: string) => {
		if (!videoUrl) return;

		// 查找对应的视频对象
		const video = videos.find((v) => v.url === videoUrl);
		if (!video || !video.id) return;

		// 检查是否已有视频详情
		if (!videoDetails.has(video.id)) {
			// 检查是否正在加载中
			if (loadingVideoDetails.has(video.id)) {
				toast({
					title: '正在加载',
					description: '视频详情正在加载中，请稍候...',
				});
				return;
			}

			try {
				// 添加到加载状态
				setLoadingVideoDetails((prev) => new Set(prev).add(video.id!));

				toast({
					title: '获取视频详情',
					description: '正在获取视频详情，请稍候...',
				});

				// 获取视频详情
				const detailResponse = await youtubeApi.getVideoInfo(videoUrl);
				const videoDetail = detailResponse.data;

				if (videoDetail) {
					// 更新视频详情状态
					setVideoDetails((prevDetails) => {
						const newDetails = new Map(prevDetails);
						newDetails.set(video.id!, videoDetail as YouTubeVideoDetail);
						return newDetails;
					});

					// 获取成功后直接打开下载对话框
					setSelectedVideoUrl(videoUrl);
					setIsDownloadDialogOpen(true);
				}
			} catch (error) {
				console.error('Failed to fetch video details:', error);
				toast({
					variant: 'destructive',
					title: '获取详情失败',
					description: error instanceof Error ? error.message : '获取视频详情失败，下载失败',
				});
			} finally {
				// 从加载状态中移除
				setLoadingVideoDetails((prev) => {
					const newSet = new Set(prev);
					newSet.delete(video.id!);
					return newSet;
				});
			}
		} else {
			// 已有详情，直接打开下载对话框
			setSelectedVideoUrl(videoUrl);
			setIsDownloadDialogOpen(true);
		}
	};

	const handleCustomDownload = async (url: string, option: DownloadOption) => {
		try {
			if (!option.url) {
				throw new Error('请选择下载的资源');
			}

			// 查找当前视频信息
			const currentVideo = videos.find((v) => v.url === url);
			const videoTitle = currentVideo?.id || 'download';

			// 清理文件名，移除特殊字符
			const cleanFilename = videoTitle
				.replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
				.replace(/[\s]+/g, '_') // 将空格替换为下划线
				.substring(0, 100); // 限制长度

			// 构建后端代理下载链接
			const downloadUrl = await buildProxyUrl(`${option.url}`, 60 * 60 * 24, 'youtube', '', cleanFilename + "." + option.format);

			try {
				// 尝试直接下载
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 10000);
				const response = await fetch(downloadUrl, {
					method: 'HEAD',
					signal: controller.signal,
				});

				clearTimeout(timeoutId);

				if (response.ok) {
					// 如果响应正常，创建隐藏的下载链接并触发下载
					const link = document.createElement('a');
					link.href = downloadUrl;
					link.style.display = 'none';
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);

					toast({
						title: '下载开始',
						description: `${option.type === 'video' ? '视频' : option.type === 'audio' ? '音频' : '封面'}下载已开始`,
					});
				} else {
					throw new Error('下载链接无效');
				}
			} catch (downloadError) {
				// 直接下载失败，提供备用方案
				console.warn('直接下载失败，使用备用方案:', downloadError);
				toast({
					title: '下载链接已打开',
					description: '已在新标签页打开下载链接，请在新页面右键选择"另存为"进行下载',
					variant: 'default',
					duration: 3000,
				});
				// 在新标签页打开下载链接
				window.open(option.url, '_blank');
			}
		} catch (error) {
			console.error('下载失败:', error);
			toast({
				title: '下载失败',
				description: error instanceof Error ? error.message : '下载过程中发生错误，请稍后重试',
				variant: 'destructive',
			});
		}
	};

	const handleDownloadSubtitles = async (videoUrl: string) => {
		if (!videoUrl) return;

		// 查找对应的视频对象
		const video = videos.find((v) => v.url === videoUrl);
		if (!video || !video.id) return;

		// 检查是否已有视频详情
		if (!videoDetails.has(video.id)) {
			// 检查是否正在加载中
			if (loadingVideoDetails.has(video.id)) {
				toast({
					title: '正在加载',
					description: '视频详情正在加载中，请稍候...',
				});
				return;
			}

			try {
				// 添加到加载状态
				setLoadingVideoDetails((prev) => new Set(prev).add(video.id!));

				toast({
					title: '获取视频详情',
					description: '正在获取视频详情，请稍候...',
				});

				// 获取视频详情
				const detailResponse = await youtubeApi.getVideoInfo(videoUrl);
				const videoDetail = detailResponse.data as YouTubeVideoDetail;

				if (videoDetail) {
					// 更新视频详情状态
					setVideoDetails((prevDetails) => {
						const newDetails = new Map(prevDetails);
						newDetails.set(video.id!, videoDetail as YouTubeVideoDetail);
						return newDetails;
					});

					// 检查字幕是否存在
					if (!videoDetail || !videoDetail.subtitles || Object.keys(videoDetail.subtitles).length === 0) {
						toast({
							variant: 'destructive',
							title: '未找到字幕',
							description: '该视频没有可用的字幕信息',
						});
						return;
					}

					// 获取成功后直接打开字幕下载对话框
					setSelectedSubtitleVideoUrl(videoUrl);
					setIsSubtitleDialogOpen(true);
				}
			} catch (error) {
				console.error('Failed to fetch video details:', error);
				toast({
					variant: 'destructive',
					title: '获取详情失败',
					description: error instanceof Error ? error.message : '获取视频详情失败，无法下载字幕',
				});
			} finally {
				// 从加载状态中移除
				setLoadingVideoDetails((prev) => {
					const newSet = new Set(prev);
					newSet.delete(video.id!);
					return newSet;
				});
			}
		} else {
			// 已有详情，检查字幕是否存在
			const videoDetail = videoDetails.get(video.id!);
			if (!videoDetail || !videoDetail.subtitles || Object.keys(videoDetail.subtitles).length === 0) {
				toast({
					variant: 'destructive',
					title: '未找到字幕',
					description: '该视频没有可用的字幕信息',
				});
				return;
			}

			// 直接打开字幕下载对话框
			setSelectedSubtitleVideoUrl(videoUrl);
			setIsSubtitleDialogOpen(true);
		}
	};

	return {
			handleDownloadVideo,
			handleCustomDownload,
			handleDownloadSubtitles,
			downloadDialogProps: {
				isOpen: isDownloadDialogOpen,
				onOpenChange: setIsDownloadDialogOpen,
				videoUrl: selectedVideoUrl,
				videoDetails: (() => {
					const video = videos.find(v => v.url === selectedVideoUrl);
					return video?.id ? videoDetails.get(video.id) || null : null;
				})(),
				onCustomDownload: handleCustomDownload,
			},
			subtitleDialogProps: {
				isOpen: isSubtitleDialogOpen,
				onOpenChange: setIsSubtitleDialogOpen,
				videoUrl: selectedSubtitleVideoUrl,
				videoDetails: (() => {
					const video = videos.find(v => v.url === selectedSubtitleVideoUrl);
					return video?.id ? videoDetails.get(video.id) || null : null;
				})(),
			},
		};
};