import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { Subtitles, Download, Eye } from 'lucide-react';
import { formatDuration, formatTimestamp, formatNumber } from '../../lib/utils';
import { YouTubeVideo, YouTubeUserInfo } from '../../lib/api';
import MediaDownloadDialog from '../MediaDownloadDialog';
import SubtitleDownloadDialog from '../SubtitleDownloadDialog';
import YoutubeExportDialog from './YoutubeExportDialog';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { ExampleImageSVG } from '../ui/example-image';

interface YouTubeVideoDetail {
	id: string;
	title: string;
	uploader: string;
	formats: Array<{
		format_id: string;
		format_note: string;
		ext: string;
		protocol: string;
		acodec: string;
		vcodec: string;
		url: string;
		width: number | null;
		height: number | null;
		fps: number | null;
		audio_channels: number | null;
		quality: number;
		has_drm: boolean;
		tbr: number;
		filesize_approx: number | null;
		resolution: string;
		aspect_ratio: number | null;
		format: string;
	}>;
	thumbnails: Array<{
		url: string;
		height: number;
		width: number;
		id: string;
		resolution: string;
	}>;
	description: string;
	channel_id: string;
	channel_url: string;
	duration: number;
	view_count: number;
	tags: string[];
	timestamp: number;
	comment_count: number;
	like_count: number;
	channel_follower_count: number;
	upload_date: string;
	thumbnail: string;
	fulltitle: string;
	duration_string: string;
	subtitles?: Record<string, any[]>;
}

interface YouTubeVideoTableProps {
	videos: YouTubeVideo[];
	videoDetails: Map<string, YouTubeVideoDetail>;
	includeDetails: boolean;
	loadingVideoDetails: Set<string>;
	contentType?: string;
	userInfo?: YouTubeUserInfo | null;
	hasMoreData?: boolean;
	isLoadingMore?: boolean;
	isShowingExampleData?: boolean;
	selectedVideos?: Set<string>;
	onVideoSelect?: (videoId: string, selected: boolean) => void;
	onSelectAll?: (selected: boolean) => void;
	onViewDetails?: () => void;
	isLoadingDetails?: boolean;
	onDownloadVideo: (videoUrl: string) => void;
	onDownloadSubtitles: (videoUrl: string) => void;
	onExport?: () => void;
	onLoadMore?: () => void;
	downloadDialogProps?: {
		isOpen: boolean;
		onOpenChange: (open: boolean) => void;
		videoUrl: string;
		videoDetails: YouTubeVideoDetail | null;
		onCustomDownload: (videoUrl: string, option: any) => Promise<void>;
	};
	subtitleDialogProps?: {
		isOpen: boolean;
		onOpenChange: (open: boolean) => void;
		videoUrl: string;
		videoDetails: YouTubeVideoDetail | null;
	};
	exportDialogProps?: {
		isOpen: boolean;
		onOpenChange: (open: boolean) => void;
		exportProgress: { current: number; total: number; isLoading: boolean };
		onExportConfirm: (config: any) => void;
	};
}

const YouTubeVideoTable: React.FC<YouTubeVideoTableProps> = ({
	videos,
	videoDetails,
	includeDetails,
	loadingVideoDetails,
	contentType,
	userInfo,
	hasMoreData,
	isLoadingMore,
	isShowingExampleData = false,
	selectedVideos = new Set(),
	onVideoSelect,
	onSelectAll,
	onViewDetails,
	isLoadingDetails = false,
	onDownloadVideo,
	onDownloadSubtitles,
	onExport,
	onLoadMore,
	downloadDialogProps,
	subtitleDialogProps,
	exportDialogProps,
}) => {
	const formatUploadDate = (uploadDate: string): string => {
		if (!uploadDate || uploadDate.length !== 8) return '-';
		const year = uploadDate.substring(0, 4);
		const month = uploadDate.substring(4, 6);
		const day = uploadDate.substring(6, 8);
		return `${year}-${month}-${day}`;
	};

	return (
		<TooltipProvider>
			<div className="space-y-4">
			{/* 操作按钮 */}
			{videos.length > 0 && (
				<div className="flex justify-end gap-2">
					{onViewDetails && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									onClick={isShowingExampleData ? undefined : onViewDetails}
									variant="outline"
									className="flex items-center gap-2"
									disabled={isShowingExampleData || selectedVideos.size === 0 || isLoadingDetails}
								>
									<Eye className="h-4 w-4" />
									{isLoadingDetails ? '获取详情中...' : `查看详情${selectedVideos.size > 0 ? ` (${selectedVideos.size})` : ''}`}
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									{isShowingExampleData
										? '示例数据不支持查看详情'
										: isLoadingDetails
										? '正在获取视频详情...'
										: selectedVideos.size === 0
										? '请先选择视频'
										: `查看 ${selectedVideos.size} 个视频的详情`
									}
								</p>
							</TooltipContent>
						</Tooltip>
					)}
					{onExport && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									onClick={isShowingExampleData ? undefined : onExport}
									variant="outline"
									className="flex items-center gap-2"
									disabled={isShowingExampleData}
								>
									<Download className="h-4 w-4" />
									导出数据
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>{isShowingExampleData ? '示例数据不支持导出' : '导出视频数据'}</p>
							</TooltipContent>
						</Tooltip>
					)}
				</div>
			)}

			<div className="border rounded-md">
				<Table>
				<TableHeader>
					<TableRow>
						<TableHead className="w-[50px]">
							{onSelectAll && (
								<Checkbox
									checked={videos.length > 0 && selectedVideos.size === videos.length}
									onCheckedChange={onSelectAll}
									aria-label="全选"
								/>
							)}
						</TableHead>
						<TableHead className="w-[120px]">缩略图</TableHead>
						<TableHead>标题</TableHead>
						<TableHead className="w-[100px]">播放量</TableHead>
						<TableHead className="w-[80px]">时长</TableHead>
						{includeDetails && [
							<TableHead key="likes" className="w-[80px]">点赞数</TableHead>,
							<TableHead key="comments" className="w-[80px]">评论数</TableHead>,
							<TableHead key="upload-date" className="w-[160px]">发布时间</TableHead>,
							<TableHead key="tags" className="w-[200px]">标签</TableHead>
						]}
						<TableHead className="w-[120px]">操作</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{videos.map((video) => {
						const detail = videoDetails.get(video.id!);
						return (
							<TableRow key={video.id}>
								<TableCell>
									{onVideoSelect && (
										<Checkbox
											checked={selectedVideos.has(video.id!)}
											onCheckedChange={(checked) => onVideoSelect(video.id!, checked as boolean)}
											aria-label={`选择视频: ${video.title}`}
										/>
									)}
								</TableCell>
								<TableCell>
									{isShowingExampleData ? (
										<ExampleImageSVG width={96} height={64} type="video" />
									) : (
										<img
											src={video.thumbnails?.[0]?.url || ''}
											alt={video.title || ''}
											className="w-24 h-16 object-cover rounded"
											loading="lazy"
											referrerPolicy="no-referrer"
										/>
									)}
								</TableCell>
								<TableCell className="max-w-[400px]">
									{isShowingExampleData ? (
										<div className="text-gray-700 text-sm font-medium">
											<div className="line-clamp-2">{video.title}</div>
										</div>
									) : (
										<a
											href={video.url}
											target="_blank"
											rel="noopener noreferrer"
											className="text-blue-600 hover:underline text-sm font-medium"
										>
											<div className="line-clamp-2">{video.title}</div>
										</a>
									)}
									<p className="text-xs text-gray-500 mt-1 line-clamp-2">{video.description}</p>
								</TableCell>
								<TableCell>{formatNumber(video.view_count)}</TableCell>
								<TableCell>{detail ? formatDuration(detail.duration) : formatDuration(Number(video.duration))}</TableCell>
								{includeDetails && [
									<TableCell key="likes">{detail ? formatNumber(detail.like_count) : '-'}</TableCell>,
									<TableCell key="comments">{detail ? formatNumber(detail.comment_count) : '-'}</TableCell>,
									<TableCell key="upload-date" className="whitespace-pre-line w-[160px]">
										{formatTimestamp(detail?.timestamp) || (detail?.upload_date ? formatUploadDate(detail.upload_date) : '-')}
									</TableCell>,
									<TableCell key="tags" className="max-w-[200px]">
										<div className="text-xs text-gray-600 line-clamp-2">{detail?.tags?.slice(0, 5).join(', ') || '-'}</div>
									</TableCell>
								]}
								<TableCell>
									<div className="flex gap-2">
										{(() => {
											const isLoadingDetails = Boolean(video.id && loadingVideoDetails.has(video.id));
											return (
												<Tooltip>
													<TooltipTrigger asChild>
														<Button
															variant="outline"
															size="sm"
															onClick={isShowingExampleData ? undefined : () => onDownloadVideo(video.url || '')}
															disabled={isShowingExampleData || !video.url || isLoadingDetails}
															className="text-xs"
														>
															{isLoadingDetails ? (
																<>
																	<div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
																	加载中
																</>
															) : (
																'视频下载'
															)}
														</Button>
													</TooltipTrigger>
													<TooltipContent>
														<p>{isShowingExampleData ? '示例数据不支持下载' : '下载视频'}</p>
													</TooltipContent>
												</Tooltip>
											);
										})()}
										{(() => {
											const isLoadingDetails = Boolean(video.id && loadingVideoDetails.has(video.id));

											return (
												<Tooltip>
													<TooltipTrigger asChild>
														<Button
															variant="outline"
															size="sm"
															onClick={isShowingExampleData ? undefined : () => onDownloadSubtitles(video.url || '')}
															disabled={isShowingExampleData || !video.url || isLoadingDetails}
															className="text-xs"
															title="下载字幕"
														>
															{isLoadingDetails ? (
																<>
																	<div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
																	加载中
																</>
															) : (
																<>
																	<Subtitles className="w-3 h-3 mr-1" />
																	字幕
																</>
															)}
														</Button>
													</TooltipTrigger>
													<TooltipContent>
														<p>{isShowingExampleData ? '示例数据不支持下载字幕' : '下载视频字幕'}</p>
													</TooltipContent>
												</Tooltip>
											);
										})()}
									</div>
								</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
				</Table>
			</div>

			{/* 加载更多按钮 */}
			{hasMoreData && onLoadMore && (
				<div className="flex justify-center py-4">
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								onClick={isShowingExampleData ? undefined : onLoadMore}
								disabled={isShowingExampleData || isLoadingMore}
								variant="outline"
							>
								{isLoadingMore ? (
									<>
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
										加载中...
									</>
								) : (
									'加载更多'
								)}
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多视频数据'}</p>
						</TooltipContent>
					</Tooltip>
				</div>
			)}

			{/* 对话框组件 */}
			{downloadDialogProps && (
				<MediaDownloadDialog
					isOpen={downloadDialogProps.isOpen}
					onClose={() => downloadDialogProps.onOpenChange(false)}
					mediaUrl={downloadDialogProps.videoUrl}
					videoDetail={(() => {
						const video = videos.find(v => v.url === downloadDialogProps.videoUrl);
						const detail = video?.id ? videoDetails.get(video.id) : null;
						return detail ? {
							id: detail.id,
							title: detail.title,
							formats: detail.formats,
							thumbnails: detail.thumbnails
						} : undefined;
					})()}
					onDownload={downloadDialogProps.onCustomDownload}
				/>
			)}

			{subtitleDialogProps && (
				<SubtitleDownloadDialog
					isOpen={subtitleDialogProps.isOpen}
					onClose={() => subtitleDialogProps.onOpenChange(false)}
					videoDetail={(() => {
						const video = videos.find(v => v.url === subtitleDialogProps.videoUrl);
						const detail = video?.id ? videoDetails.get(video.id) : null;
						return detail ? {
							id: detail.id,
							title: detail.title,
							subtitles: detail.subtitles
						} : undefined;
					})()}
				/>
			)}

			{exportDialogProps && (
				<YoutubeExportDialog
				isOpen={exportDialogProps.isOpen}
				onOpenChange={exportDialogProps.onOpenChange}
				exportProgress={exportDialogProps.exportProgress}
				onExportConfirm={exportDialogProps.onExportConfirm}
				/>
			)}
		</div>
	</TooltipProvider>
);
};

export default YouTubeVideoTable;
export type { YouTubeVideoDetail };