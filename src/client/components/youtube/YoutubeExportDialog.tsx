import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '../ui/dialog';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card } from '../ui/card';
import { Progress } from '../ui/progress';
import { Info, Loader2 } from 'lucide-react';

export interface ExportConfig {
  includeDetails: boolean;
  maxCount: number;
}

export interface ExportProgress {
  current: number;
  total: number;
  isLoading: boolean;
}

interface YoutubeExportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onExportConfirm: (config: ExportConfig) => void;
  totalCount?: number;
  exportProgress?: ExportProgress;
}

const YoutubeExportDialog: React.FC<YoutubeExportDialogProps> = ({
  isOpen,
  onOpenChange,
  onExportConfirm,
  exportProgress = { current: 0, total: 0, isLoading: false },
}) => {
  const [includeDetails, setIncludeDetails] = useState(false);
  const [maxCount, setMaxCount] = useState(100);

  const handleConfirm = () => {
    onExportConfirm({
      includeDetails,
      maxCount: maxCount
    });
    // 不立即关闭弹窗，等待导出完成
  };

  const handleClose = (open: boolean) => {
    // 防止在加载过程中关闭对话框
    if (exportProgress.isLoading && !open) {
      return;
    }
    onOpenChange(open);
  };
  
  // 计算进度百分比
  const progressPercentage = exportProgress.total > 0 ? Math.round((exportProgress.current / exportProgress.total) * 100) : 0;

  const handleMaxCountChange = (value: string) => {
    const num = parseInt(value) || 0;
    setMaxCount(Math.min(Math.max(1, num), 1000));
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md" onPointerDownOutside={(e) => {
        if (exportProgress.isLoading) {
          e.preventDefault();
        }
      }} onEscapeKeyDown={(e) => {
        if (exportProgress.isLoading) {
          e.preventDefault();
        }
      }}>
        <DialogHeader>
          <DialogTitle>
            {exportProgress.isLoading ? '正在导出...' : '导出配置'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="exportIncludeDetails" 
                checked={includeDetails} 
                onCheckedChange={(checked) => setIncludeDetails(checked as boolean)} 
                disabled={exportProgress.isLoading}
              />
              <Label htmlFor="exportIncludeDetails" className="text-sm font-medium">
                包含视频详情（点赞数、评论数、字幕等）
              </Label>
            </div>
            
            {includeDetails && (
              <Card className="p-3 bg-blue-50 border-blue-200">
                <div className="space-y-2 text-sm text-blue-700">
                  <div className="flex items-center space-x-2">
                    <Info className="h-4 w-4" />
                    <span>包含详情将获取以下额外信息：</span>
                  </div>
                  <ul className="ml-6 space-y-1 text-xs">
                    <li>• 点赞数、评论数、发布时间、视频标签</li>
                    <li>• 字幕链接（优先英文字幕的下载链接）</li>
                  </ul>
                  <div className="text-xs text-blue-600 mt-2">
                    注意：获取详情需要更多时间，请耐心等待
                  </div>
                </div>
              </Card>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="maxCount" className="text-sm font-medium">
              导出条数（最大1000条）
            </Label>
            <Input
              id="maxCount"
              type="number"
              min="1"
              max="1000"
              value={maxCount}
              onChange={(e) => handleMaxCountChange(e.target.value)}
              className="w-full"
              disabled={exportProgress.isLoading}
            />
          </div>
        </div>
        
        {/* 进度条显示 */}
        {exportProgress.isLoading && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm font-medium">正在导出数据...</span>
            </div>
            <Progress 
              value={progressPercentage} 
              className="w-full" 
            />
            <p className="text-xs text-gray-500 text-center">
              {exportProgress.current} / {exportProgress.total} ({progressPercentage}%)
            </p>
          </div>
        )}
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => handleClose(false)}
            disabled={exportProgress.isLoading}
          >
            {exportProgress.isLoading ? '导出中...' : '取消'}
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={exportProgress.isLoading}
          >
            {exportProgress.isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                导出中...
              </>
            ) : (
              '确认导出'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default YoutubeExportDialog;