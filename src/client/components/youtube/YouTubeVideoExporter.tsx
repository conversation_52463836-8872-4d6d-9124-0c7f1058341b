import { useState } from 'react';
import { youtubeApi, YouTubeVideo, YouTubeUserInfo } from '../../lib/api';
import { exportArrayToCSV, formatTimestamp } from '../../lib/utils';
import { ExportConfig } from './YoutubeExportDialog';
import { YouTubeVideoDetail } from './YouTubeVideoTable';

interface UseYouTubeVideoExporterProps {
	videos: YouTubeVideo[];
	videoDetails: Map<string, YouTubeVideoDetail>;
	setVideoDetails: React.Dispatch<React.SetStateAction<Map<string, YouTubeVideoDetail>>>;
	setVideos: React.Dispatch<React.SetStateAction<YouTubeVideo[]>>;
	currentPage: number;
	setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
	hasMoreData: boolean;
	setHasMoreData: React.Dispatch<React.SetStateAction<boolean>>;
	pageSize: number;
	toast: any;
	userInfo?: YouTubeUserInfo | null;
	contentType?: string;
	userUrl?: string;
	youtubeApi?: any;
}

export const useYouTubeVideoExporter = ({
	videos,
	videoDetails,
	setVideoDetails,
	setVideos,
	currentPage,
	setCurrentPage,
	hasMoreData,
	setHasMoreData,
	pageSize,
	toast,
	userInfo,
	contentType = 'videos',
	userUrl = '',
	youtubeApi,
}: UseYouTubeVideoExporterProps) => {
	const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
	const [exportProgress, setExportProgress] = useState({ current: 0, total: 0, isLoading: false });

	const handleExport = () => {
		setIsExportDialogOpen(true);
	};

	const formatUploadDate = (uploadDate: string): string => {
		if (!uploadDate || uploadDate.length !== 8) return '-';
		const year = uploadDate.substring(0, 4);
		const month = uploadDate.substring(4, 6);
		const day = uploadDate.substring(6, 8);
		return `${year}-${month}-${day}`;
	};

	const handleExportConfirm = async (config: ExportConfig) => {
		const { includeDetails: exportIncludeDetails, maxCount } = config;

		try {
			// 初始化导出进度
			setExportProgress({ current: 0, total: maxCount, isLoading: true });

			// 确保导出的数据量不超过用户指定的条数
			let exportVideos = videos.slice(0, Math.min(maxCount, videos.length));
			if (maxCount > videos.length && hasMoreData && userUrl.trim()) {
				try {
					toast({
						title: '加载数据中',
						description: `正在加载更多数据以满足导出需求...`,
					});

					// 计算需要加载的页数
					const currentCount = videos.length;
					const needCount = maxCount - currentCount;
					const needPages = Math.ceil(needCount / pageSize);

					const cleanUrl = userUrl.trim().replace(/\/(videos|shorts|playlists|community|channels|about)\/?$/, '');
					// 使用本地变量跟踪累积的视频数据
					let accumulatedVideos = [...videos];

					for (let page = 1; page <= needPages; page++) {
						const nextPage = currentPage + page;
						const startIndex = nextPage * pageSize + 1;
						const endIndex = (nextPage + 1) * pageSize;

						let videosResponse;
						if (contentType === 'playlist') {
							videosResponse = await youtubeApi.getUserVideos(cleanUrl, startIndex, endIndex, 'playlists');
						} else {
							videosResponse = await youtubeApi.getUserVideos(
								cleanUrl + '/' + contentType,
								startIndex,
								endIndex,
								contentType as 'videos' | 'shorts'
							);
						}

						const newVideoList = videosResponse.data as YouTubeVideo[];
						if (newVideoList.length === 0) {
							setHasMoreData(false);
							break;
						}

						// 更新本地累积数据（仅用于导出，不影响页面显示）
						accumulatedVideos = [...accumulatedVideos, ...newVideoList];
						// 重新获取导出数据，确保不超过maxCount
						exportVideos = accumulatedVideos.slice(0, Math.min(maxCount, accumulatedVideos.length));

						// 检查是否还有更多数据
						if (newVideoList.length < pageSize) {
							setHasMoreData(false);
						}

						// 更新导出进度
						setExportProgress((prev) => ({ ...prev, current: exportVideos.length }));

						toast({
							title: '加载进度',
							description: `已加载 ${exportVideos.length}/${maxCount} 条数据`,
						});

						if (exportVideos.length >= maxCount) break;
					}
				} catch (error) {
					console.error('Failed to load more data for export:', error);
					toast({
						variant: 'destructive',
						title: '加载数据失败',
						description: '无法加载足够的数据，将导出当前已有数据',
					});
					// 继续使用当前已有数据进行导出
				}
			}

			// 确保不超过实际数据量
			exportVideos = exportVideos.slice(0, Math.min(maxCount, exportVideos.length));

			const headers = ['视频ID', '标题', '描述', '播放量', '时长(秒)', '视频链接', '缩略图链接', '频道名称'];

			if (exportIncludeDetails) {
				headers.push('点赞数', '评论数', '发布时间', '标签', '字幕语言', '字幕链接');
			}

			// 创建一个本地的详情映射，用于导出时使用
			let exportVideoDetails = new Map(videoDetails);

			// 如果需要包含详情但还没有获取，则先获取详情
			if (exportIncludeDetails) {
				const videosNeedingDetails = exportVideos.filter((video) => video.id && video.url && !exportVideoDetails.has(video.id));

				if (videosNeedingDetails.length > 0) {
					// 保持总进度数为用户指定的导出数量
					setExportProgress((prev) => ({
						...prev,
						total: maxCount,
					}));

					for (let i = 0; i < videosNeedingDetails.length; i++) {
						const video = videosNeedingDetails[i];
						try {
							// 更新获取详情的进度，按比例计算
							const detailProgress = Math.floor((i / videosNeedingDetails.length) * maxCount);
							setExportProgress((prev) => ({
								...prev,
								current: detailProgress,
							}));

							toast({
								title: '获取详情中',
								description: `正在获取第 ${i + 1}/${videosNeedingDetails.length} 个视频详情...`,
							});

							const detailResponse = await youtubeApi.getVideoInfo(video.url!);
							const videoDetail = detailResponse.data;

							if (videoDetail) {
								// 同时更新本地映射和全局状态
								exportVideoDetails.set(video.id!, videoDetail as YouTubeVideoDetail);
								setVideoDetails((prevDetails) => {
									const newDetails = new Map(prevDetails);
									newDetails.set(video.id!, videoDetail as YouTubeVideoDetail);
									return newDetails;
								});
							}
						} catch (error) {
							console.error(`Failed to fetch details for video ${video.id}:`, error);
						}
					}
					// 获取详情完成后，设置进度为完成状态
					setExportProgress((prev) => ({
						...prev,
						current: maxCount,
					}));
				}
			}

			// 创建数据行
			const rows = [headers];

			for (let i = 0; i < exportVideos.length; i++) {
				const video = exportVideos[i];
				const detail = exportVideoDetails.get(video.id!);

				const baseRow = [
					video.id || '',
					video.title || '',
					video.description || '',
					video.view_count?.toLocaleString() || '0',
					detail ? detail.duration : (video.duration || '-'),
					video.url || '',
					video.thumbnails?.[0]?.url || '',
					userInfo?.name || '',
				];

				if (exportIncludeDetails && detail) {
					const subtitleLanguages = detail.subtitles ? Object.keys(detail.subtitles).join(', ') : '';

					// 获取字幕链接
					let subtitleLinks = '';
					if (detail.subtitles) {
						// 优先获取英文字幕，如果没有则获取第一个可用的字幕
						const preferredLanguages = ['en', 'en-US', 'en-GB'];
						let selectedLanguage = '';
						let selectedSubtitle: any = null;

						// 查找首选语言
						for (const lang of preferredLanguages) {
							if (detail.subtitles[lang] && detail.subtitles[lang].length > 0) {
								selectedLanguage = lang;
								selectedSubtitle = detail.subtitles[lang].find((sub: any) => sub.ext === 'srt') || detail.subtitles[lang][0];
								break;
							}
						}

						// 如果没有找到首选语言，使用第一个可用的语言
						if (!selectedLanguage) {
							const availableLanguages = Object.keys(detail.subtitles);
							if (availableLanguages.length > 0) {
								selectedLanguage = availableLanguages[0];
								const languageSubtitles = detail.subtitles[selectedLanguage];
								selectedSubtitle = languageSubtitles.find((sub: any) => sub.ext === 'srt') || languageSubtitles[0];
							}
						}

						if (selectedLanguage && selectedSubtitle && selectedSubtitle.url) {
							subtitleLinks = `${selectedLanguage}: ${selectedSubtitle.url}`;
						}
					}

					baseRow.push(
						detail.like_count?.toLocaleString() || '0',
						detail.comment_count?.toLocaleString() || '0',
						formatUploadDate(detail.upload_date || ''),
						detail.tags?.join(', ') || '',
						subtitleLanguages,
						subtitleLinks
					);
				} else if (exportIncludeDetails) {
					baseRow.push('', '', '', '', '', '');
				}

				// 将数字转换为字符串后再添加到rows中
				rows.push(baseRow.map((item) => item.toString()));
			}

			const getFileNameSuffix = () => {
				if (contentType === 'playlist') {
					return '播放列表视频';
				} else if (contentType === 'videos') {
					return '用户视频';
				} else if (contentType === 'shorts') {
					return '用户Shorts';
				}
				return '视频';
			};

			const fileName = `YouTube${getFileNameSuffix()}_${userInfo?.name || 'unknown'}_${new Date().toLocaleString()}.csv`;
			exportArrayToCSV(rows as (string | number)[][], fileName);

			toast({
				title: '导出成功',
				description: `已导出 ${exportVideos.length} 条${getFileNameSuffix()}数据${exportIncludeDetails ? '（包含详情和字幕链接）' : ''}`,
			});
		} catch (error) {
			console.error('Export failed:', error);
			toast({
				variant: 'destructive',
				title: '导出失败',
				description: '导出过程中发生错误，请稍后重试',
			});
		} finally {
			// 重置导出进度状态并关闭弹窗
			setExportProgress({ current: 0, total: 0, isLoading: false });
			setIsExportDialogOpen(false);
		}
	};

	return {
		handleExport,
		handleExportConfirm,
		exportDialogProps: {
			isOpen: isExportDialogOpen,
			onOpenChange: setIsExportDialogOpen,
			exportProgress,
			onExportConfirm: handleExportConfirm,
		},
	};
};
