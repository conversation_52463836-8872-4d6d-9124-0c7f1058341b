import { useState, useEffect } from 'react';
import { YouTubeVideo } from '../../lib/api';
import { YouTubeVideoDetail } from './YouTubeVideoTable';
import { youtubeApi } from '../../lib/api';
import { useToast } from '../ui/use-toast';

interface UseYouTubeVideoDetailsProps {
	videos: YouTubeVideo[];
	videoDetails: Map<string, YouTubeVideoDetail>;
	setVideoDetails: React.Dispatch<React.SetStateAction<Map<string, YouTubeVideoDetail>>>;
	includeDetails: boolean;
}

export const useYouTubeVideoDetails = ({
	videos,
	videoDetails,
	setVideoDetails,
	includeDetails,
}: UseYouTubeVideoDetailsProps) => {
	const { toast } = useToast();
	const [isFetchingDetails, setIsFetchingDetails] = useState(false);

	// 获取视频详情的通用方法
	const fetchVideoDetails = async (videoList: YouTubeVideo[], showProgress = true) => {
		if (!videoList.length) return;

		// 并发获取所有视频详情
		let completedCount = 0;
		const totalCount = videoList.filter(video => video.url).length;

		const fetchPromises = videoList.map(async (video, index) => {
			if (!video.url) {
				return;
			}
			try {
				const detailResponse = await youtubeApi.getVideoInfo(video.url);
				const videoDetail = detailResponse.data;

				if (videoDetail) {
					// 立即更新状态，每获取到一个详情就渲染
					setVideoDetails((prevDetails) => {
						const newDetails = new Map(prevDetails);
						newDetails.set(video.id!, videoDetail as YouTubeVideoDetail);
						return newDetails;
					});
				}

				// 更新进度
				completedCount++;
				if (showProgress && (completedCount % 5 === 0 || completedCount === totalCount)) {
					toast({
						title: '获取详情中',
						description: `已获取 ${completedCount}/${totalCount} 个视频详情`,
					});
				}
			} catch (error) {
				console.error(`Failed to fetch details for video ${video.id}:`, error);
				completedCount++;
			}
		});

		// 等待所有请求完成
		await Promise.allSettled(fetchPromises);
	};

	// 监听includeDetails变化，自动获取当前视频的详情
	useEffect(() => {
		const fetchDetailsForCurrentVideos = async () => {
			if (!includeDetails || videos.length === 0 || isFetchingDetails) return;

			// 找出还没有获取详情的视频
			const videosNeedingDetails = videos.filter((video) => video.id && video.url && !videoDetails.has(video.id));

			if (videosNeedingDetails.length === 0) return;

			setIsFetchingDetails(true);

			toast({
				title: '开始获取详情',
				description: `正在为 ${videosNeedingDetails.length} 个视频获取详情...`,
			});

			try {
				await fetchVideoDetails(videosNeedingDetails, true);

				toast({
					title: '详情获取完成',
					description: `已为 ${videosNeedingDetails.length} 个视频获取详情`,
				});
			} catch (error) {
				console.error('Failed to fetch details for current videos:', error);
			} finally {
				setIsFetchingDetails(false);
			}
		};

		fetchDetailsForCurrentVideos();
	}, [includeDetails, videos, toast, isFetchingDetails, videoDetails]);

	return {
		isFetchingDetails,
		fetchVideoDetails,
	};
};