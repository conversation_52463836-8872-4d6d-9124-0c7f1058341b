import React, { useState, useEffect, useRef } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Loader2, CreditCard } from 'lucide-react';
import { useToast } from './ui/use-toast';
import stripeApi, { CreatePaymentIntentRequest } from '../lib/stripeApi';
import { configApi } from '../lib/api';

interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  currency: string;
  credits: number;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  onSuccess: () => void;
}

interface CheckoutFormProps {
  product: Product;
  onSuccess: () => void;
  onClose: () => void;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({ product, onSuccess, onClose }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment/stripe?success=true`,
        },
        redirect: 'if_required',
      });

      if (error) {
        // 处理各种错误情况
        if (error.type === 'card_error' || error.type === 'validation_error') {
          toast({
            title: '支付失败',
            description: error.message,
            variant: 'destructive',
          });
        } else {
          toast({
            title: '支付失败',
            description: '发生了意外错误，请重试。',
            variant: 'destructive',
          });
        }
      } else if (paymentIntent) {
        // 检查支付意图的状态
        console.log('支付意图状态:', paymentIntent.status);
        
        if (paymentIntent.status === 'succeeded') {
          // 支付成功
          console.log('支付成功');
          onSuccess();
          onClose();
        } else if (paymentIntent.status === 'processing') {
          // 支付处理中（例如银行转账等异步支付方式）
          toast({
            title: '支付处理中',
            description: '您的支付正在处理中，请稍后查看支付结果。',
            variant: 'default',
          });
          onClose();
        } else if (paymentIntent.status === 'requires_action') {
          // 需要进一步操作（例如3D验证）
          toast({
            title: '需要验证',
            description: '请完成额外的验证步骤。',
            variant: 'default',
          });
        } else {
          // 其他状态（canceled, requires_payment_method等）
          console.log('支付未完成，状态:', paymentIntent.status);
          toast({
            title: '支付未完成',
            description: '支付未完成，请重试。',
            variant: 'destructive',
          });
        }
      } else {
        // 没有error也没有paymentIntent，异常情况
        toast({
          title: '支付状态未知',
          description: '无法确定支付状态，请联系客服。',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      console.error('支付处理失败:', error);
      toast({
        title: '支付失败',
        description: '支付处理过程中发生错误，请重试。',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">价格:</span>
          <span className="font-bold text-xl">
            {product.currency === 'CNY' ? '¥' : '$'}{product.price.toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between items-center mt-2">
          <span className="text-gray-600">获得积分:</span>
          <span className="font-semibold text-blue-600">{product.credits} 积分</span>
        </div>
      </div>

      <div className="space-y-4">
        <PaymentElement 
          options={{
            layout: 'tabs',
          }}
        />
      </div>

      <div className="flex gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isLoading}
          className="flex-1"
        >
          取消
        </Button>
        <Button
          type="submit"
          disabled={!stripe || isLoading}
          className="flex-1"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              处理中...
            </>
          ) : (
            <>
              <CreditCard className="mr-2 h-4 w-4" />
              确认支付
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

const PaymentModal: React.FC<PaymentModalProps> = ({ isOpen, onClose, product, onSuccess }) => {
  const [clientSecret, setClientSecret] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [stripePromise, setStripePromise] = useState<Promise<any> | null>(null);
  const [initError, setInitError] = useState<string>('');
  const isCancelledRef = useRef(false);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && product) {
      // 重置取消标志和所有状态
      isCancelledRef.current = false;
      setClientSecret('');
      setStripePromise(null);
      setInitError('');
      setIsLoading(false);
      // 初始化支付
      initializeStripeAndPayment(isCancelledRef);
    } else if (!isOpen) {
      // 弹窗关闭时立即设置取消标志并重置所有状态
      isCancelledRef.current = true;
      setClientSecret('');
      setStripePromise(null);
      setInitError('');
      setIsLoading(false);
    }
    
    return () => {
      isCancelledRef.current = true;
    };
  }, [isOpen, product]);

  const initializeStripeAndPayment = async (isCancelledRef: React.MutableRefObject<boolean>) => {
    try {
      setIsLoading(true);
      setInitError('');
      
      // 从配置接口获取Stripe公钥
      console.log('正在获取Stripe配置...');
      const configResponse = await configApi.getPublicConfig();
      console.log('配置响应:', configResponse);
      
      // 检查是否已取消
      if (isCancelledRef.current) {
        return;
      }
      
      if (!configResponse.success) {
        throw new Error(`配置接口调用失败: ${configResponse.message || '未知错误'}`);
      }
      
      if (!configResponse.data?.stripe?.publishKey) {
        console.error('配置数据:', configResponse.data);
        throw new Error('Stripe配置不完整，缺少publishKey');
      }
      
      const stripePublishableKey = configResponse.data.stripe.publishKey;
      console.log('获取到Stripe公钥:', stripePublishableKey.substring(0, 20) + '...');
      
      const stripe = loadStripe(stripePublishableKey);
      
      // 再次检查是否已取消
      if (isCancelledRef.current) {
        return;
      }
      
      setStripePromise(stripe);
      
      // 创建支付意图
      await createPaymentIntent(isCancelledRef);
    } catch (error: any) {
      console.error('初始化Stripe失败:', error);
      
      // 检查是否已取消，如果已取消则不设置错误状态
      if (isCancelledRef.current) {
        return;
      }
      
      const errorMessage = error.message || '无法初始化支付系统，请重试。';
      setInitError(errorMessage);
      toast({
        title: '初始化失败',
        description: errorMessage,
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  const createPaymentIntent = async (isCancelledRef: React.MutableRefObject<boolean>) => {
    if (!product) return;

    try {
      console.log('创建支付意图，产品:', product);
      
      const response = await stripeApi.createPaymentIntent({
        productId: product.id,
        quantity: 1,
      });

      console.log('支付意图响应:', response);

      // 检查是否已取消
      if (isCancelledRef.current) {
        return;
      }

      if (response.success && response.data) {
        console.log('设置clientSecret:', response.data.clientSecret);
        setClientSecret(response.data.clientSecret);
        setIsLoading(false);
      } else {
        throw new Error(response.message || '创建支付意图失败');
      }
    } catch (error: any) {
      console.error('创建支付意图失败:', error);
      
      // 检查是否已取消，如果已取消则不设置错误状态
      if (isCancelledRef.current) {
        return;
      }
      
      const errorMessage = error.message || '无法创建支付会话，请重试。';
      setInitError(errorMessage);
      toast({
        title: '创建支付失败',
        description: errorMessage,
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#3b82f6',
      colorBackground: '#ffffff',
      colorText: '#1f2937',
      colorDanger: '#ef4444',
      fontFamily: 'system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '6px',
    },
  };

  const options = {
    clientSecret,
    appearance,
  };

  // 调试信息
  console.log('PaymentModal渲染状态:', {
    stripePromise: !!stripePromise,
    clientSecret,
    isLoading,
    product
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            安全支付
          </DialogTitle>
        </DialogHeader>

        {initError ? (
          <div className="text-center py-8 space-y-4">
            <p className="text-red-500">支付系统初始化失败</p>
            <p className="text-gray-600 text-sm">请联系客服获取帮助</p>
          </div>
        ) : !stripePromise || isLoading ? (
           <div className="flex items-center justify-center py-8">
             <Loader2 className="h-8 w-8 animate-spin" />
           </div>
        ) : clientSecret && product ? (
          <Elements options={options} stripe={stripePromise}>
            <CheckoutForm 
              product={product} 
              onSuccess={onSuccess} 
              onClose={onClose}
            />
          </Elements>
        ) : null}

        <div className="text-center text-xs text-gray-500 mt-4">
          <div className="flex items-center justify-center gap-1">
            <span>🔒</span>
            <span>支付信息由 Stripe 安全处理</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal;