import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from './ui/dialog';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Download, Subtitles } from 'lucide-react';
import { useToast } from './ui/use-toast';
import { buildProxyUrl } from '../lib/buildPorxy';

interface SubtitleFormat {
  ext: string;
  url: string;
  name: string;
}

interface VideoDetail {
  id: string;
  title: string;
  subtitles?: Record<string, any[]>;
}

interface SubtitleDownloadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  videoDetail?: VideoDetail;
  videoTitle?: string;
}

const SubtitleDownloadDialog: React.FC<SubtitleDownloadDialogProps> = ({
  isOpen,
  onClose,
  videoDetail,
  videoTitle
}) => {
  const { toast } = useToast();
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [selectedFormat, setSelectedFormat] = useState<string>('srt');
  const [isDownloading, setIsDownloading] = useState(false);

  // 当对话框打开时，自动设置默认语言
  useEffect(() => {
    if (isOpen && videoDetail?.subtitles) {
      const availableLanguages = Object.keys(videoDetail.subtitles);
      if (availableLanguages.length > 0) {
        // 查找英文语言（可能的英文标识）
        const englishLang = availableLanguages.find(lang => 
          lang.toLowerCase().includes('en') || 
          lang.toLowerCase().includes('english') ||
          lang === 'en' || lang === 'en-US' || lang === 'en-GB'
        );
        
        if (englishLang) {
          setSelectedLanguage(englishLang);
        } else {
          // 没有英文，选择第一个可用语言
          setSelectedLanguage(availableLanguages[0]);
        }
      }
    } else {
      // 重置选择状态
      setSelectedLanguage('');
      setSelectedFormat('srt');
    }
  }, [isOpen, videoDetail]);

  const handleDownload = async () => {
    if (!selectedLanguage || !selectedFormat || 
        ['no-video', 'loading', 'no-subtitles', 'no-language', 'no-format'].includes(selectedLanguage) ||
        ['no-video', 'loading', 'no-subtitles', 'no-language', 'no-format'].includes(selectedFormat)) {
      toast({
        variant: 'destructive',
        title: '参数错误',
        description: '请选择字幕语言和格式',
      });
      return;
    }

    if (!videoDetail?.subtitles) {
      toast({
        variant: 'destructive',
        title: '字幕不可用',
        description: '该视频没有可用的字幕信息',
      });
      return;
    }

    setIsDownloading(true);
    try {
      // 查找指定语言的字幕
      const languageSubtitles = videoDetail.subtitles[selectedLanguage];
      if (!languageSubtitles || languageSubtitles.length === 0) {
        toast({
          variant: 'destructive',
          title: '语言不支持',
          description: `该视频不支持 ${selectedLanguage} 语言的字幕`,
        });
        return;
      }

      // 查找指定格式的字幕
      const subtitleFormat = languageSubtitles.find((sub: any) => sub.ext === selectedFormat);
      if (!subtitleFormat) {
        toast({
          variant: 'destructive',
          title: '格式不支持',
          description: `该视频不支持 ${selectedFormat} 格式的字幕`,
        });
        return;
      }

      toast({
        title: '字幕下载中',
        description: `正在下载 ${subtitleFormat.name} 的 ${selectedFormat} 格式字幕...`,
      });

      // 清理文件名，移除特殊字符
      const cleanFilename = (videoTitle || videoDetail.title || 'subtitle')
        .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
        .replace(/[\s]+/g, '_') // 将空格替换为下划线
        .substring(0, 100); // 限制长度

      try {
        // 尝试通过代理下载
        const proxyUrl = await buildProxyUrl(subtitleFormat.url, 60*60, '', '', `${cleanFilename}.${selectedFormat}`);
        
        // 验证代理URL是否有效
        if (!proxyUrl || proxyUrl === subtitleFormat.url) {
          throw new Error('代理URL构建失败');
        }
        
        // 尝试通过fetch验证链接可访问性
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);
          
          const response = await fetch(proxyUrl, {
            method: 'HEAD',
            signal: controller.signal,
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          // 创建下载链接并提交到浏览器下载管理器
          const link = document.createElement('a');
          link.href = proxyUrl;
          link.download = `${cleanFilename}.${selectedFormat}`;
          link.style.display = 'none';
          link.target = '_blank'; // 确保在新窗口打开
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          toast({
            title: '字幕下载成功',
            description: '字幕文件下载已开始，请检查浏览器下载管理器',
          });
          
        } catch (fetchError) {
          // 如果HEAD请求失败，尝试直接下载
          console.warn('HEAD请求失败，尝试直接下载:', fetchError);
          
          const link = document.createElement('a');
          link.href = proxyUrl;
          link.download = `${cleanFilename}.${selectedFormat}`;
          link.style.display = 'none';
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          // 等待一段时间后检查下载是否成功
          setTimeout(() => {
            toast({
              title: '字幕下载已触发',
              description: '如果下载未开始，请检查浏览器设置或使用备用下载方式',
              variant: 'default',
              duration: 3000,
            });
          }, 1000);
          
          throw fetchError; // 重新抛出错误以触发备用方案
        }
        
      } catch (downloadError) {
        // 下载失败，使用备用方案：在新标签页打开字幕地址
        console.warn('字幕下载失败，使用备用方案:', downloadError);
        
        toast({
          title: '字幕下载链接已打开',
          description: '已在新标签页打开字幕链接，请在新页面右键选择"另存为"进行下载',
          variant: 'default',
          duration: 5000,
        });
        
        // 在新标签页打开原始字幕地址
        window.open(subtitleFormat.url, '_blank');
      }

      // 下载成功后关闭对话框
      onClose();
    } catch (error) {
      console.error('Failed to download subtitles:', error);
      toast({
        variant: 'destructive',
        title: '字幕下载失败',
        description: '获取字幕失败，请稍后重试',
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleClose = () => {
    if (!isDownloading) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Subtitles className="w-5 h-5" />
            字幕下载
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="language" className="text-sm font-medium">
              字幕语言
            </label>
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="请选择字幕语言" />
              </SelectTrigger>
              <SelectContent>
                {(() => {
                  if (!videoDetail) return <SelectItem value="no-video" disabled>请先选择视频</SelectItem>;
                  
                  if (!videoDetail.subtitles || Object.keys(videoDetail.subtitles).length === 0) {
                    return <SelectItem value="no-subtitles" disabled>该视频无可用字幕</SelectItem>;
                  }
                  
                  return Object.keys(videoDetail.subtitles).map(lang => {
                    const subtitleList = videoDetail.subtitles![lang];
                    const displayName = subtitleList[0]?.name || lang;
                    return (
                      <SelectItem key={lang} value={lang}>
                        {displayName} ({lang})
                      </SelectItem>
                    );
                  });
                })()}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <label htmlFor="format" className="text-sm font-medium">
              字幕格式
            </label>
            <Select value={selectedFormat} onValueChange={setSelectedFormat}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {(() => {
                  if (!videoDetail) return <SelectItem value="no-video" disabled>请先选择视频</SelectItem>;
                  if (!selectedLanguage) return <SelectItem value="no-language" disabled>请先选择语言</SelectItem>;
                  
                  if (!videoDetail.subtitles?.[selectedLanguage]) return <SelectItem value="no-format" disabled>该语言无可用格式</SelectItem>;
                  
                  return videoDetail.subtitles[selectedLanguage].map((subtitle: any) => (
                    <SelectItem key={subtitle.ext} value={subtitle.ext}>
                      {subtitle.ext.toUpperCase()} (.{subtitle.ext})
                    </SelectItem>
                  ));
                })()}
              </SelectContent>
            </Select>
          </div>
          <div className="text-sm text-gray-500">
            <p>• SRT: 最常用的字幕格式，兼容性最好</p>
            <p>• VTT: 网页视频字幕格式，支持样式</p>
            <p>• TTML: 时间文本标记语言格式</p>
            <p>• JSON: 结构化数据格式</p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isDownloading}>
            取消
          </Button>
          <Button 
            onClick={handleDownload} 
            disabled={isDownloading || !selectedLanguage || !selectedFormat || 
              ['no-video', 'loading', 'no-subtitles', 'no-language', 'no-format'].includes(selectedLanguage) ||
              ['no-video', 'loading', 'no-subtitles', 'no-language', 'no-format'].includes(selectedFormat)}
          >
            {isDownloading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                下载中...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                下载字幕
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SubtitleDownloadDialog;
export type { SubtitleFormat, VideoDetail };