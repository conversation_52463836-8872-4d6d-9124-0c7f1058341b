import React from 'react';
import { Badge } from '../ui/badge';
import { Heart, MessageCircle, User } from 'lucide-react';
import { formatNumber } from '../../lib/utils';
import { ExampleImageSVG } from '../ui/example-image';

interface KuaishouComment {
  id: string;
  content: string;
  author: {
    nickname: string;
    userId: string;
    avatar?: string;
  };
  likeCount: number;
  replyCount: number;
  createTime: number;
  formattedCreateTime: string;
  loading?: boolean;
  error?: string;
}

interface KuaishouCommentsTableProps {
  comments: KuaishouComment[];
  isShowingExampleData?: boolean;
}

export function KuaishouCommentsTable({ comments, isShowingExampleData = false }: KuaishouCommentsTableProps) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b bg-gray-50">
            <th className="text-left p-3 font-medium">作者</th>
            <th className="text-left p-3 font-medium">评论内容</th>
            <th className="text-left p-3 font-medium">点赞数</th>
            <th className="text-left p-3 font-medium">回复数</th>
            <th className="text-left p-3 font-medium">发布时间</th>
          </tr>
        </thead>
        <tbody>
          {comments.map((comment, index) => (
            <tr key={comment.id} className="border-b hover:bg-gray-50">
              {/* 作者 */}
              <td className="p-3">
                {comment.loading ? (
                  <div className="animate-pulse flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                    <div className="space-y-1">
                      <div className="h-4 bg-gray-200 rounded w-20"></div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                  </div>
                ) : comment.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                      {isShowingExampleData ? (
                        <ExampleImageSVG width={32} height={32} type="avatar" />
                      ) : comment.author.avatar ? (
                        <img
                          src={comment.author.avatar}
                          alt={comment.author.nickname}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : (
                        <User className="w-4 h-4 text-gray-500" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{comment.author.nickname}</p>
                      <p className="text-xs text-gray-500">ID: {comment.author.userId}</p>
                    </div>
                  </div>
                )}
              </td>
              
              {/* 评论内容 */}
              <td className="p-3">
                {comment.loading ? (
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                ) : comment.error ? (
                  <div className="space-y-1">
                    <Badge variant="destructive">获取失败</Badge>
                    <p className="text-xs text-red-600">{comment.error}</p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    <p className="text-sm leading-relaxed max-w-md break-words">
                      {comment.content || '无内容'}
                    </p>
                    <p className="text-xs text-gray-500">ID: {comment.id}</p>
                  </div>
                )}
              </td>
              
              {/* 点赞数 */}
              <td className="p-3">
                {comment.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : comment.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <Heart className="h-4 w-4 text-red-500" />
                    <span>{formatNumber(comment.likeCount)}</span>
                  </div>
                )}
              </td>
              
              {/* 回复数 */}
              <td className="p-3">
                {comment.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : comment.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <MessageCircle className="h-4 w-4 text-blue-500" />
                    <span>{formatNumber(comment.replyCount)}</span>
                  </div>
                )}
              </td>
              
              {/* 发布时间 */}
              <td className="p-3">
                {comment.loading ? (
                  <div className="animate-pulse">
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                  </div>
                ) : comment.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <span className="text-xs text-gray-600">
                    {comment.formattedCreateTime || '未知'}
                  </span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
