import React from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { X, ExternalLink, User, Heart, MessageCircle, Share, Eye, Forward } from 'lucide-react';
import { formatDuration, formatNumber } from '../../lib/utils';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';

interface KuaishouVideoInfo {
  id: string;
  url: string;
  title?: string;
  author?: {
    nickname?: string;
    userId?: number;
    userEid?: string;
  };
  statistics?: {
    likeCount?: number;
    commentCount?: number;
    shareCount?: number;
    viewCount?: number;
    forwardCount?: number;
  };
  video?: {
    duration?: number;
  };
  create_time?: number;
  formatted_create_time?: string;
  loading?: boolean;
  error?: string;
}

interface KuaishouVideoTableProps {
  videos: KuaishouVideoInfo[];
  onRemove: (index: number) => void;
  isShowingExampleData?: boolean;
}

export function KuaishouVideoTable({ videos, onRemove, isShowingExampleData = false }: KuaishouVideoTableProps) {
  const openUrl = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b bg-gray-50">
            <th className="text-left p-3 font-medium">视频信息</th>
            <th className="text-left p-3 font-medium">作者</th>
            <th className="text-left p-3 font-medium">时长</th>
            <th className="text-left p-3 font-medium">播放数</th>
            <th className="text-left p-3 font-medium">点赞数</th>
            <th className="text-left p-3 font-medium">评论数</th>
            <th className="text-left p-3 font-medium">分享数</th>
            <th className="text-left p-3 font-medium">转发数</th>
            <th className="text-left p-3 font-medium">发布时间</th>
            <th className="text-left p-3 font-medium">操作</th>
          </tr>
        </thead>
        <tbody>
          {videos.map((video, index) => (
            <tr key={video.id} className="border-b hover:bg-gray-50">
              <td className="p-3">
                <div className="space-y-2">
                  {video.loading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-sm text-gray-500">加载中...</span>
                    </div>
                  ) : video.error ? (
                    <div className="space-y-1">
                      <Badge variant="destructive">获取失败</Badge>
                      <p className="text-xs text-red-600">{video.error}</p>
                      <p className="text-xs text-gray-500 break-all">{video.url}</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {isShowingExampleData ? (
                        <span className="font-medium text-sm line-clamp-2 text-gray-700" title={video.title}>
                          {video.title}
                        </span>
                      ) : (
                        <a
                          href={video.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="font-medium text-sm line-clamp-2 text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                          title={video.title}
                        >
                          {video.title}
                        </a>
                      )}
                      {isShowingExampleData ? (
                        <span className="text-xs text-gray-500">
                          ID: {video.id}
                        </span>
                      ) : (
                        <a
                          href={video.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-gray-500 hover:text-blue-600 hover:underline cursor-pointer"
                        >
                          ID: {video.id}
                        </a>
                      )}
                    </div>
                  )}
                </div>
              </td>
              
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-20 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="space-y-1">
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3 text-gray-400" />
                      {isShowingExampleData ? (
                        <span className="text-sm font-medium text-gray-700">{video.author?.nickname || '未知'}</span>
                      ) : video.author?.userEid ? (
                        <a
                          href={`https://www.kuaishou.com/profile/${video.author.userEid}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline"
                        >
                          {video.author?.nickname || '未知'}
                        </a>
                      ) : (
                        <span className="text-sm font-medium">{video.author?.nickname || '未知'}</span>
                      )}
                    </div>
                    {video.author?.userId && (
                      <p className="text-xs text-gray-500">ID: {video.author.userId}</p>
                    )}
                  </div>
                )}
              </td>

              {/* 时长 */}
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <span className="text-sm text-gray-600">
                    {video.video?.duration ? formatDuration(video.video.duration / 1000) : '-'}
                  </span>
                )}
              </td>

              {/* 播放数 */}
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <Eye className="h-4 w-4 text-blue-500" />
                    <span>{formatNumber(video.statistics?.viewCount || 0)}</span>
                  </div>
                )}
              </td>

              {/* 点赞数 */}
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <Heart className="h-4 w-4 text-red-500" />
                    <span>{formatNumber(video.statistics?.likeCount || 0)}</span>
                  </div>
                )}
              </td>

              {/* 评论数 */}
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <MessageCircle className="h-4 w-4 text-green-500" />
                    <span>{formatNumber(video.statistics?.commentCount || 0)}</span>
                  </div>
                )}
              </td>

              {/* 分享数 */}
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <Share className="h-4 w-4 text-purple-500" />
                    <span>{formatNumber(video.statistics?.shareCount || 0)}</span>
                  </div>
                )}
              </td>

              {/* 转发数 */}
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <div className="flex items-center gap-1 text-sm">
                    <Forward className="h-4 w-4 text-orange-500" />
                    <span>{formatNumber(video.statistics?.forwardCount || 0)}</span>
                  </div>
                )}
              </td>
              
              <td className="p-3">
                {video.loading ? (
                  <div className="animate-pulse">
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                  </div>
                ) : video.error ? (
                  <span className="text-gray-400">-</span>
                ) : (
                  <span className="text-xs text-gray-600">
                    {video.formatted_create_time || '未知'}
                  </span>
                )}
              </td>
              
              <td className="p-3">
                <div className="flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={isShowingExampleData ? undefined : () => openUrl(video.url)}
                        className="h-8 px-2"
                        disabled={isShowingExampleData}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isShowingExampleData ? '示例数据不支持打开链接' : '打开视频链接'}</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={isShowingExampleData ? undefined : () => onRemove(index)}
                        className="h-8 px-2 text-red-600 hover:text-red-700"
                        disabled={isShowingExampleData}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isShowingExampleData ? '示例数据不支持删除' : '删除此视频'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
