import { useState, useEffect } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Search, Download, AlertTriangle, Calendar } from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Progress } from '../ui/progress';
import { kuaishouApi } from '../../lib/api';
import { KuaishouCommentsTable } from './KuaishouCommentsTable.tsx';
import { exportArrayToCSV } from '../../lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import '../../styles/date-picker.css';

interface KuaishouComment {
  id: string;
  content: string;
  author: {
    nickname: string;
    userId: string;
    avatar?: string;
  };
  likeCount: number;
  replyCount: number;
  createTime: number;
  formattedCreateTime: string;
  loading?: boolean;
  error?: string;
}

export function KuaishouCommentsExtractor() {
  const [videoUrl, setVideoUrl] = useState('');
  const [videoInfo, setVideoInfo] = useState<{photoId: string, authorId: string} | null>(null);
  const [comments, setComments] = useState<KuaishouComment[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [pcursor, setPcursor] = useState<string>('');
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [isShowingExampleData, setIsShowingExampleData] = useState(true);

  // 导出弹窗状态
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [exportStartDate, setExportStartDate] = useState('');
  const [exportEndDate, setExportEndDate] = useState('');
  const [includeReplies, setIncludeReplies] = useState(false);
  const [exportCount, setExportCount] = useState('');
  const [exportKeywords, setExportKeywords] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const { toast } = useToast();

  // 加载示例数据
  const loadExampleData = async () => {
    try {
      // 加载评论列表示例数据
      const commentsResponse = await fetch('/test/data/kuaishou/commentList.json');
      const commentsData = await commentsResponse.json();

      // 加载详情示例数据
      const detailResponse = await fetch('/test/data/kuaishou/ksDetail.json');
      const detailData = await detailResponse.json();

      // 转换评论数据
      const convertedComments: KuaishouComment[] = commentsData.data.data.visionCommentList.rootComments.map((comment: any, index: number) => ({
        id: comment.commentId || `comment_${Date.now()}_${index}`,
        content: comment.content || '',
        author: {
          nickname: comment.authorName || '未知用户',
          userId: comment.authorId || '',
          avatar: comment.headurl
        },
        likeCount: parseInt(comment.likedCount) || comment.realLikedCount || 0,
        replyCount: comment.subCommentCount || 0,
        createTime: comment.timestamp || Date.now(),
        formattedCreateTime: comment.timestamp ? new Date(comment.timestamp).toLocaleString('zh-CN') : '',
        loading: false
      }));

      // 设置示例视频信息（从详情数据中解析）
      const exampleVideoInfo = {
        photoId: 'example_photo_id',
        authorId: 'example_author_id'
      };

      setComments(convertedComments);
      setVideoInfo(exampleVideoInfo);
      setHasMore(!!commentsData.data.data.visionCommentList.pcursor);
      setPcursor(commentsData.data.data.visionCommentList.pcursor || '');
      setTotal(commentsData.data.data.visionCommentList.commentCount || 0);

      toast({
        title: '示例数据已加载',
        description: `已加载 ${convertedComments.length} 条示例评论数据`,
      });
    } catch (error) {
      console.error('Failed to load example data:', error);
      toast({
        variant: 'destructive',
        title: '加载示例数据失败',
        description: '无法加载示例数据，请检查文件是否存在',
      });
    }
  };

  // 组件挂载时加载示例数据
  useEffect(() => {
    loadExampleData();
  }, []);

  // 从URL中直接解析photoId和authorId
  const parseVideoUrl = (url: string) => {
    try {
      const urlObj = new URL(url);

      // 从路径中提取photoId (short-video/photoId)
      const pathMatch = urlObj.pathname.match(/\/short-video\/([^\/]+)/);
      const photoId = pathMatch ? pathMatch[1] : '';

      // 从查询参数中提取authorId
      const authorId = urlObj.searchParams.get('authorId') || '';

      return {
        photoId,
        authorId,
        hasRequiredParams: !!(photoId && authorId)
      };
    } catch (error) {
      return {
        photoId: '',
        authorId: '',
        hasRequiredParams: false
      };
    }
  };

  // 解析share_info获取photoId和authorId
  const parseShareInfo = (shareInfo: string) => {
    const params = new URLSearchParams(shareInfo);
    return {
      photoId: params.get('photoId') || '',
      authorId: params.get('userId') || ''
    };
  };

  // 提取评论
  const handleExtract = async (loadMore = false) => {
    if (!videoUrl.trim()) {
      toast({
        title: '提示',
        description: '请输入快手视频链接',
        variant: 'destructive'
      });
      return;
    }

    setIsExtracting(true);
    setIsShowingExampleData(false);

    try {
      let currentVideoInfo = videoInfo;

      // 如果是首次提取或没有视频信息，先尝试从URL解析
      if (!loadMore || !currentVideoInfo) {
        // 首先尝试从URL直接解析
        const urlParsed = parseVideoUrl(videoUrl.trim());

        if (urlParsed.hasRequiredParams) {
          // URL中包含完整参数，直接使用
          currentVideoInfo = {
            photoId: urlParsed.photoId,
            authorId: urlParsed.authorId
          };
          setVideoInfo(currentVideoInfo);
        } else {
          // URL中参数不完整，调用详情接口
          const detailResponse = await kuaishouApi.getVideoDetail(videoUrl.trim()) as any;

          if (!detailResponse.success || !detailResponse.data?.photo?.share_info) {
            throw new Error('获取视频详情失败，无法解析视频信息');
          }

          const parsedInfo = parseShareInfo(detailResponse.data.photo.share_info);
          if (!parsedInfo.photoId || !parsedInfo.authorId) {
            throw new Error('无法从视频详情中解析出photoId和authorId');
          }

          currentVideoInfo = parsedInfo;
          setVideoInfo(currentVideoInfo);
        }
      }

      const cursor = loadMore ? pcursor : undefined;
      const response = await kuaishouApi.getComments(currentVideoInfo.photoId, currentVideoInfo.authorId, cursor);
      
      if (response.success && response.data?.data?.visionCommentList?.rootComments) {
        const commentList = response.data.data.visionCommentList;
        const newComments: KuaishouComment[] = commentList.rootComments.map((comment: any, index: number) => ({
          id: comment.commentId || `comment_${Date.now()}_${index}`,
          content: comment.content || '',
          author: {
            nickname: comment.authorName || '未知用户',
            userId: comment.authorId || '',
            avatar: comment.headurl
          },
          likeCount: parseInt(comment.likedCount) || comment.realLikedCount || 0,
          replyCount: comment.subCommentCount || 0,
          createTime: comment.timestamp || Date.now(),
          formattedCreateTime: comment.timestamp ? new Date(comment.timestamp).toLocaleString('zh-CN') : '',
          loading: false
        }));

        if (loadMore) {
          setComments(prev => [...prev, ...newComments]);
        } else {
          setComments(newComments);
        }

        // 更新翻页信息和总数
        setPcursor(commentList.pcursor || '');
        setHasMore(commentList.pcursor && commentList.pcursor !== 'no_more');

        // 设置总评论数（只在首次加载时设置）
        if (!loadMore) {
          setTotal(commentList.commentCount || 0);
        }

        toast({
          title: '提取成功',
          description: loadMore
            ? `加载了 ${newComments.length} 条评论，共 ${comments.length + newComments.length} 条`
            : `获取到 ${newComments.length} 条评论，总计 ${total.toLocaleString()} 条`
        });
      } else {
        throw new Error(response.message || '获取评论失败');
      }
    } catch (error) {
      console.error('获取评论失败:', error);
      toast({
        title: '提取失败',
        description: error instanceof Error ? error.message : '获取评论失败',
        variant: 'destructive'
      });
    } finally {
      setIsExtracting(false);
    }
  };

  // 加载更多评论
  const handleLoadMore = () => {
    if (hasMore && !isExtracting) {
      handleExtract(true);
    }
  };

  // 清空结果
  const handleClear = () => {
    setComments([]);
    setPcursor('');
    setHasMore(false);
    setVideoInfo(null);
    setTotal(0);
  };

  // 导出按钮点击处理
  const handleExportClick = () => {
    if (comments.length === 0) {
      toast({
        title: '无数据可导出',
        description: '请先提取评论数据',
        variant: 'destructive',
      });
      return;
    }
    setIsExportDialogOpen(true);
  };

  // 自动加载更多评论数据
  const loadMoreCommentsForExport = async (targetCount: number): Promise<KuaishouComment[]> => {
    let allComments = [...comments];
    let currentPcursor = pcursor;
    let currentHasMore = hasMore;

    // 先去重现有评论
    allComments = allComments.filter((comment, index, self) =>
      index === self.findIndex(c => c.id === comment.id)
    );

    while (allComments.length < targetCount && currentHasMore && currentPcursor && videoInfo) {
      try {
        const response = await kuaishouApi.getComments(videoInfo.photoId, videoInfo.authorId, currentPcursor) as any;

        if (response.success && response.data?.data?.visionCommentList?.rootComments) {
          const commentList = response.data.data.visionCommentList;
          const newComments: KuaishouComment[] = commentList.rootComments.map((comment: any, index: number) => ({
            id: comment.commentId || `comment_${Date.now()}_${index}`,
            content: comment.content || '',
            author: {
              nickname: comment.authorName || '未知用户',
              userId: comment.authorId || '',
              avatar: comment.headurl
            },
            likeCount: parseInt(comment.likedCount) || comment.realLikedCount || 0,
            replyCount: comment.subCommentCount || 0,
            createTime: comment.timestamp || Date.now(),
            formattedCreateTime: comment.timestamp ? new Date(comment.timestamp).toLocaleString('zh-CN') : '',
            loading: false
          }));

          // 合并新评论
          const combinedComments = [...allComments, ...newComments];
          // 去重处理
          allComments = combinedComments.filter((comment, index, self) =>
            index === self.findIndex(c => c.id === comment.id)
          );

          currentPcursor = commentList.pcursor || '';
          currentHasMore = commentList.pcursor && commentList.pcursor !== 'no_more';

          // 去重后检查是否已达到目标数量
          if (allComments.length >= targetCount) {
            break;
          }
        } else {
          break;
        }
      } catch (error) {
        console.error('Failed to load more comments:', error);
        toast({
          title: '评论内容加载失败',
          description: error instanceof Error ? error.message : '获取评论失败',
          variant: 'destructive',
        });
        break;
      }
    }

    return allComments;
  };

  // 获取评论回复数据
  const getCommentReplies = async (photoId: string, authorId: string, commentId: string): Promise<any[]> => {
    let allReplies: any[] = [];
    let currentPcursor = '';
    let hasMoreReplies = true;

    while (hasMoreReplies) {
      try {
        const response = await kuaishouApi.getReplies(photoId, authorId, commentId, currentPcursor) as any;

        if (response.success && response.data?.data?.visionSubCommentList?.subComments) {
          const replyList = response.data.data.visionSubCommentList;
          allReplies = [...allReplies, ...replyList.subComments];
          currentPcursor = replyList.pcursor || '';
          hasMoreReplies = !!(replyList.pcursor && replyList.pcursor !== 'no_more');
        } else {
          toast({
            title: '回复内容加载失败',
            description: response.message || '获取评论回复失败',
            variant: 'destructive',
          });
          break;
        }
      } catch (error) {
        console.error('Failed to get comment replies:', error);
        toast({
          title: '回复内容加载失败',
          description: error instanceof Error ? error.message : '获取评论回复失败',
          variant: 'destructive',
        });
        break;
      }
    }

    return allReplies;
  };

  // 主导出处理函数
  const handleExport = async () => {
    // 验证导出数量
    const count = parseInt(exportCount);
    if (!exportCount || isNaN(count) || count <= 0) {
      toast({
        title: '参数错误',
        description: '请输入有效的导出条数',
        variant: 'destructive',
      });
      return;
    }

    if (count > total && total > 0) {
      toast({
        title: '参数错误',
        description: `导出条数不能超过总评论数 ${total.toLocaleString()}`,
        variant: 'destructive',
      });
      return;
    }

    if (!videoInfo) {
      toast({
        title: '参数错误',
        description: '缺少视频信息，请重新提取评论',
        variant: 'destructive',
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // 先去重现有评论数据
      let allComments = [...comments];
      allComments = allComments.filter((comment, index, self) =>
        index === self.findIndex(c => c.id === comment.id)
      );

      // 检查是否需要加载更多评论数据
      if (allComments.length < count && hasMore) {
        setExportProgress(10);
        toast({
          title: '正在加载更多数据',
          description: `当前已有 ${allComments.length} 条评论（去重后），需要总计 ${count} 行数据（含回复），正在自动加载...`,
        });

        // 预估需要加载的评论数量（考虑到回复会增加总行数）
        const estimatedCommentsNeeded = includeReplies ? Math.ceil(count * 0.7) : count;
        allComments = await loadMoreCommentsForExport(estimatedCommentsNeeded);
        setExportProgress(30);
      } else {
        setExportProgress(30);
      }

      // 时间筛选
      let filteredComments = [...allComments];
      if (exportStartDate || exportEndDate) {
        const startTime = exportStartDate ? new Date(exportStartDate).getTime() : 0;
        const endTime = exportEndDate ? new Date(exportEndDate + ' 23:59:59').getTime() : Infinity;

        filteredComments = filteredComments.filter(comment => {
          return comment.createTime >= startTime && comment.createTime <= endTime;
        });
      }

      // 关键词筛选
      if (exportKeywords.trim()) {
        const keywords = exportKeywords.trim().split(/\s+/).filter(Boolean);
        filteredComments = filteredComments.filter(comment => {
          const commentText = (comment.content || '').toLowerCase();
          return keywords.some(keyword => commentText.includes(keyword.toLowerCase()));
        });
      }

      // 筛选后再次去重，确保数据唯一性
      filteredComments = filteredComments.filter((comment, index, self) =>
        index === self.findIndex(c => c.id === comment.id)
      );

      setExportProgress(50);

      // 如果需要包含回复，获取回复数据
      let repliesData: { [commentId: string]: any[] } = {};
      if (includeReplies) {
        toast({
          title: '正在获取回复数据',
          description: `正在获取 ${filteredComments.length} 条评论的回复...`,
        });

        for (let i = 0; i < filteredComments.length; i++) {
          const comment = filteredComments[i];
          if (comment.replyCount > 0) {
            try {
              const replies = await getCommentReplies(videoInfo.photoId, videoInfo.authorId, comment.id);
              // 对回复数据进行去重
              const uniqueReplies = replies.filter((reply, index, self) =>
                index === self.findIndex(r => r.commentId === reply.commentId)
              );
              repliesData[comment.id] = uniqueReplies;
            } catch (error) {
              console.error(`Failed to get replies for comment ${comment.id}:`, error);
            }
          }

          // 更新进度
          const progress = 50 + (i / filteredComments.length) * 30;
          setExportProgress(Math.round(progress));
        }
      }

      setExportProgress(80);

      // 构建导出数据
      const headers = [
        '评论ID',
        '主评论ID',
        '是否是回复',
        '用户昵称',
        '用户ID',
        '评论内容',
        '点赞数',
        '回复数',
        '发布时间',
        '回复对象',
      ];

      const rows: any[][] = [headers];

      // 筛选条件
      const startTime = exportStartDate ? new Date(exportStartDate).getTime() : 0;
      const endTime = exportEndDate ? new Date(exportEndDate + ' 23:59:59').getTime() : Infinity;
      const hasTimeFilter = exportStartDate || exportEndDate;
      const keywords = exportKeywords.trim() ? exportKeywords.trim().split(/\s+/).filter(Boolean) : [];
      const hasKeywordFilter = keywords.length > 0;

      // 处理主评论和回复，控制总行数
      let currentRowCount = 0; // 当前已添加的行数（不包括表头）

      for (const comment of filteredComments) {
        // 检查是否已达到导出条数限制
        if (currentRowCount >= count) {
          break;
        }

        // 添加主评论行
        const mainCommentRow = [
          comment.id,
          '', // 主评论ID为空
          '否', // 不是回复
          comment.author.nickname,
          comment.author.userId,
          comment.content,
          comment.likeCount,
          comment.replyCount,
          comment.formattedCreateTime,
          '', // 回复对象为空
        ];
        rows.push(mainCommentRow);
        currentRowCount++;

        // 如果包含回复，添加回复行
        if (includeReplies && repliesData[comment.id] && currentRowCount < count) {
          const replies = repliesData[comment.id] || [];
          for (const reply of replies) {
            // 检查是否已达到导出条数限制
            if (currentRowCount >= count) {
              break;
            }

            // 对回复进行时间筛选
            const replyTime = reply.timestamp || 0;
            if (hasTimeFilter && (replyTime < startTime || replyTime > endTime)) {
              continue; // 跳过不满足时间条件的回复
            }

            // 对回复进行关键词筛选
            if (hasKeywordFilter) {
              const replyText = (reply.content || '').toLowerCase();
              const matchesKeyword = keywords.some(keyword => replyText.includes(keyword.toLowerCase()));
              if (!matchesKeyword) {
                continue; // 跳过不包含关键词的回复
              }
            }

            const replyRow = [
              reply.commentId || `${comment.id}_reply_${Math.random()}`, // 回复ID
              comment.id, // 主评论ID
              '是', // 是回复
              reply.authorName || '未知用户',
              reply.authorId || '-',
              reply.content || '',
              parseInt(reply.likedCount) || reply.realLikedCount || 0,
              0, // 回复的回复数
              reply.timestamp ? new Date(reply.timestamp).toLocaleString('zh-CN') : '',
              reply.replyToUserName || '', // 回复对象
            ];
            rows.push(replyRow);
            currentRowCount++;
          }
        }
      }

      setExportProgress(90);

      exportArrayToCSV(rows, `快手评论提取结果_${new Date().toLocaleString()}.csv`);

      setExportProgress(100);

      // 计算总行数（不包括表头）
      const totalRows = rows.length - 1;
      // 计算主评论和回复的数量
      let mainCommentCount = 0;
      let replyCount = 0;
      for (let i = 1; i < rows.length; i++) { // 跳过表头
        if (rows[i][2] === '否') { // 是否是回复列
          mainCommentCount++;
        } else {
          replyCount++;
        }
      }

      // 构建筛选提示信息
      const filterInfo: string[] = [];
      if (hasTimeFilter) filterInfo.push('时间筛选');
      if (hasKeywordFilter) filterInfo.push('关键词筛选');
      const filterText = filterInfo.length > 0 ? `（已应用${filterInfo.join('、')}）` : '';

      toast({
        title: '导出成功',
        description: `已导出 ${mainCommentCount} 条主评论${includeReplies ? `，${replyCount} 条回复，共 ${totalRows} 行数据` : ''}${filterText}`,
      });

      // 重置状态
      setTimeout(() => {
        setIsExportDialogOpen(false);
        setExportProgress(0);
        setExportStartDate('');
        setExportEndDate('');
        setIncludeReplies(false);
        setExportCount('');
        setExportKeywords('');
      }, 1000);

    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: '导出失败',
        description: error instanceof Error ? error.message : "导出过程中发生错误，请重试",
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
      {/* 输入区域 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="video-url">快手视频链接</Label>
            <Input
              id="video-url"
              placeholder="请输入快手视频链接，如：https://www.kuaishou.com/short-video/3xrz553rvj5m7tc?authorId=3xttpxaqthggea2"
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              disabled={isExtracting}
            />
            {videoInfo && (
              <div className="text-xs text-gray-500 mt-2">
                视频ID: {videoInfo.photoId} | 作者ID: {videoInfo.authorId}
                {parseVideoUrl(videoUrl).hasRequiredParams && (
                  <span className="text-green-600 ml-2">✓ 从URL直接解析</span>
                )}
              </div>
            )}
          </div>
          
          <div className="flex justify-end gap-2">
            <Button onClick={() => handleExtract(false)} disabled={isExtracting || !videoUrl.trim()}>
              <Search className="w-4 h-4 mr-2" />
              {isExtracting ? '提取中...' : '提取评论'}
            </Button>
            <Button variant="outline" onClick={handleClear} disabled={isExtracting || (comments.length === 0 && !videoInfo)}>
              清空
            </Button>
          </div>
        </div>
      </Card>

      {/* 结果区域 */}
      {comments.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">
                评论列表 - 已加载 {comments.length.toLocaleString()} 条
                {total > 0 && ` / 总计 ${total.toLocaleString()} 条`}
              </h3>
              {isShowingExampleData && (
                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                  示例数据
                </span>
              )}
            </div>
            <div className="flex gap-2">
              {hasMore && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={isShowingExampleData ? undefined : handleLoadMore}
                      disabled={isShowingExampleData || isExtracting}
                    >
                      {isExtracting ? '加载中...' : '加载更多'}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isShowingExampleData ? '示例数据不支持加载更多' : '加载更多评论数据'}</p>
                  </TooltipContent>
                </Tooltip>
              )}
              <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
                <DialogTrigger asChild>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        onClick={isShowingExampleData ? undefined : handleExportClick}
                        disabled={isShowingExampleData}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        导出 CSV
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isShowingExampleData ? '示例数据不支持导出' : '导出评论数据'}</p>
                    </TooltipContent>
                  </Tooltip>
                </DialogTrigger>
                <DialogContent className="max-w-lg max-h-[90vh] overflow-hidden flex flex-col">
                  <DialogHeader className="flex-shrink-0">
                    <DialogTitle>导出评论数据</DialogTitle>
                  </DialogHeader>

                  <div className="flex-1 overflow-y-auto pr-1 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                    {/* 重要提示 */}
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-yellow-800">
                          <p className="font-medium mb-1">重要提示：</p>
                          <ul className="space-y-1 text-xs">
                            <li>• 由于接口返回数据存在乱序，筛选时间或关键词时可能导致积分消耗与导出内容不完全对应。</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* 导出条数 */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <Label htmlFor="export-count" className="text-sm font-medium text-gray-700">
                          导出条数
                        </Label>
                        <span className="text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full font-medium">必填</span>
                      </div>
                      <div className="relative">
                        <Input
                          id="export-count"
                          type="number"
                          placeholder={`最多 ${total > 0 ? total.toLocaleString() : '未知'} 行`}
                          value={exportCount}
                          onChange={(e) => setExportCount(e.target.value)}
                          disabled={isExporting}
                          min="1"
                          max={total > 0 ? total : undefined}
                          className="text-sm border border-gray-200 focus:border-orange-400 focus:ring-orange-400/20 transition-colors pl-3 pr-16 rounded-md"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500 font-medium">
                          行数据
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-xs text-gray-600 bg-gray-50 border border-gray-200 rounded-lg p-2">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-3 h-3 rounded-full bg-gray-200 flex items-center justify-center">
                              <div className="w-1.5 h-1.5 rounded-full bg-gray-500"></div>
                            </div>
                            <span className="font-medium text-gray-700">数据统计</span>
                          </div>
                          <p>
                            当前已加载 <span className="font-medium text-blue-600">{comments.length.toLocaleString()}</span> 条评论
                            {total > 0 && (
                              <span> / 总计 <span className="font-medium text-green-600">{total.toLocaleString()}</span> 条</span>
                            )}
                          </p>
                        </div>
                        <div className="text-xs text-orange-700 bg-orange-50 border border-orange-200 rounded-lg p-2">
                          <div className="flex items-start gap-2">
                            <div className="w-3 h-3 rounded-full bg-orange-100 flex items-center justify-center mt-0.5">
                              <div className="w-1.5 h-1.5 rounded-full bg-orange-500"></div>
                            </div>
                            <div>
                              <p className="font-medium text-orange-800 mb-1">重要提醒</p>
                              <p>导出条数是 <span className="font-medium">评论+回复</span> 的总行数，不仅仅是主评论数量</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 时间筛选 */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <Label className="text-sm font-medium text-gray-700">时间筛选</Label>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">可选</span>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-1.5">
                          <Label htmlFor="start-date" className="text-xs font-medium text-gray-600 flex items-center gap-1">
                            <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                            开始时间
                          </Label>
                          <div className="relative group">
                            <Input
                              id="start-date"
                              type="date"
                              value={exportStartDate}
                              onChange={(e) => setExportStartDate(e.target.value)}
                              disabled={isExporting}
                              className="text-sm border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 transition-all duration-200 pl-10 pr-10 py-2.5 rounded-lg bg-white hover:bg-gray-50 focus:bg-white custom-date-picker"
                              style={{
                                colorScheme: 'light',
                              }}
                            />
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-blue-500 transition-colors duration-200">
                              <Calendar className="w-4 h-4" />
                            </div>
                            {exportStartDate && (
                              <button
                                type="button"
                                onClick={() => setExportStartDate('')}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200 z-10"
                                disabled={isExporting}
                              >
                                <div className="w-4 h-4 rounded-full bg-gray-200 hover:bg-red-100 flex items-center justify-center">
                                  <span className="text-xs">×</span>
                                </div>
                              </button>
                            )}
                          </div>
                        </div>
                        <div className="space-y-1.5">
                          <Label htmlFor="end-date" className="text-xs font-medium text-gray-600 flex items-center gap-1">
                            <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                            结束时间
                          </Label>
                          <div className="relative group">
                            <Input
                              id="end-date"
                              type="date"
                              value={exportEndDate}
                              onChange={(e) => setExportEndDate(e.target.value)}
                              disabled={isExporting}
                              className="text-sm border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 transition-all duration-200 pl-10 pr-10 py-2.5 rounded-lg bg-white hover:bg-gray-50 focus:bg-white custom-date-picker"
                              style={{
                                colorScheme: 'light',
                              }}
                            />
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-blue-500 transition-colors duration-200">
                              <Calendar className="w-4 h-4" />
                            </div>
                            {exportEndDate && (
                              <button
                                type="button"
                                onClick={() => setExportEndDate('')}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200 z-10"
                                disabled={isExporting}
                              >
                                <div className="w-4 h-4 rounded-full bg-gray-200 hover:bg-red-100 flex items-center justify-center">
                                  <span className="text-xs">×</span>
                                </div>
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 关键词筛选 */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <Label htmlFor="export-keywords" className="text-sm font-medium text-gray-700">关键词筛选</Label>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">可选</span>
                      </div>
                      <div className="relative">
                        <Input
                          id="export-keywords"
                          type="text"
                          placeholder="输入关键词，多个关键词用空格分隔"
                          value={exportKeywords}
                          onChange={(e) => setExportKeywords(e.target.value)}
                          disabled={isExporting}
                          className="text-sm border-gray-200 focus:border-purple-400 focus:ring-purple-400/20 transition-colors pl-3 pr-10"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <div className="w-4 h-4 text-gray-400">
                            <svg viewBox="0 0 16 16" fill="currentColor">
                              <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 bg-purple-50 border border-purple-200 rounded-lg p-2">
                        <div className="flex items-start gap-2">
                          <div className="w-3 h-3 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                            <div className="w-1.5 h-1.5 rounded-full bg-purple-500"></div>
                          </div>
                          <div>
                            <p className="font-medium text-purple-800 mb-1">关键词筛选说明</p>
                            <ul className="space-y-0.5 text-purple-700">
                              <li>• 支持多个关键词，用空格分隔</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 是否包含回复 */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <Label className="text-sm font-medium text-gray-700">回复选项</Label>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">可选</span>
                      </div>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-start space-x-3">
                          <Checkbox
                            id="include-replies"
                            checked={includeReplies}
                            onCheckedChange={(checked) => setIncludeReplies(checked as boolean)}
                            disabled={isExporting}
                            className="mt-0.5"
                          />
                          <div className="flex-1">
                            <Label htmlFor="include-replies" className="text-sm font-medium text-green-800 cursor-pointer">
                              包含评论回复内容
                            </Label>
                            <p className="text-xs text-green-700 mt-1">
                              勾选后将获取每条评论的回复数据，会增加积分消耗和导出时间
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮 - 固定在底部 */}
                  <div className="flex-shrink-0 border-t border-gray-200 pt-4 mt-4">
                    <div className="flex items-center justify-between">
                      {/* 进度条 - 左侧 */}
                      <div className="flex-1 mr-4">
                        {isExporting && (
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs text-gray-600">
                              <span>导出进度</span>
                              <span>{exportProgress}%</span>
                            </div>
                            <Progress value={exportProgress} className="w-full h-2" />
                          </div>
                        )}
                      </div>

                      {/* 按钮 - 右侧 */}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={() => setIsExportDialogOpen(false)}
                          disabled={isExporting}
                        >
                          取消
                        </Button>
                        <Button
                          onClick={handleExport}
                          disabled={isExporting || !exportCount}
                        >
                          {isExporting ? '导出中...' : '开始导出'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          <KuaishouCommentsTable comments={comments} isShowingExampleData={isShowingExampleData} />
        </Card>
      )}
    </div>
    </TooltipProvider>
  );
}
