import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Loader2, Zap, MessageSquare, User, FileText, Video, Play, Youtube, Gift } from 'lucide-react';
import { useToast } from './ui/use-toast';
import { creditTypeApi, type CreditType } from '../lib/creditTypeApi';

interface PopularFeaturesProps {
  title?: string;
  description?: string;
  showHeader?: boolean;
  className?: string;
}

const PopularFeatures: React.FC<PopularFeaturesProps> = ({
  title = "热门功能",
  description = "使用积分解锁强大的数据挖掘功能",
  showHeader = true,
  className = ""
}) => {
  const [creditTypes, setCreditTypes] = useState<CreditType[]>([]);
  const [isCreditTypesLoading, setIsCreditTypesLoading] = useState(false);
  const { toast } = useToast();

  // 获取积分类型数据
  const fetchCreditTypes = useCallback(async () => {
    setIsCreditTypesLoading(true);
    try {
      const response = await creditTypeApi.getCreditTypes();
      if (response.success && response.data) {
        // 过滤出活跃的扣费类型功能，排除充值相关类型
        const deductCreditTypes = Array.isArray(response.data) ? response.data.filter(
          (creditType: CreditType) => 
            creditType.operation_type === 'deduct' &&
            creditType.type_key !== 'stripe_payment_credit' &&
            creditType.type_key !== 'system_add_credit'
        ) : [];
        setCreditTypes(deductCreditTypes);
      }
    } catch (error) {
      console.error('获取功能列表失败:', error);
      toast({
        title: '获取功能列表失败',
        description: '无法加载功能列表，请刷新页面重试。',
        variant: 'destructive',
      });
    } finally {
      setIsCreditTypesLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchCreditTypes();
  }, [fetchCreditTypes]);

  // 根据积分类型键值获取对应的图标
  const getIconForCreditType = (typeKey: string) => {
    const iconMap: { [key: string]: JSX.Element } = {
      'xhs_topic_deduct': <MessageSquare className="h-6 w-6" />,
      'xhs_user_note_deduct': <User className="h-6 w-6" />,
      'xhs_topic_note_deduct': <FileText className="h-6 w-6" />,
      'xhs_note_detail_deduct': <FileText className="h-6 w-6" />,
      'dy_xt_video_deduct': <Video className="h-6 w-6" />,
      'dy_video_detail_deduct': <Play className="h-6 w-6" />,
      'dy_user_videos_deduct': <User className="h-6 w-6" />,
      'tiktok_user_info_deduct': <User className="h-6 w-6" />,
      'tiktok_user_posts_deduct': <Play className="h-6 w-6" />,
      'youtube_user_info_deduct': <Youtube className="h-6 w-6" />,
      'youtube_user_videos_deduct': <Youtube className="h-6 w-6" />,
      'youtube_user_shorts_deduct': <Video className="h-6 w-6" />,
      'youtube_user_playlists_deduct': <Video className="h-6 w-6" />,
      'youtube_video_info_deduct': <Youtube className="h-6 w-6" />,
    };
    return iconMap[typeKey] || <Zap className="h-6 w-6" />;
  };

  // 根据积分类型键值获取分类
  const getCategoryForCreditType = (typeKey: string) => {
    if (typeKey.startsWith('xhs_')) return '红薯取数';
    if (typeKey.startsWith('dy_')) return '抖音取数';
    if (typeKey.startsWith('tiktok_')) return 'TikTok取数';
    if (typeKey.startsWith('youtube_')) return 'YouTube取数';
    return '数据服务';
  };

  return (
    <div className={className}>
      {showHeader && (
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {title}
          </h2>
        </div>
      )}

      {isCreditTypesLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载功能列表中...</span>
        </div>
      ) : creditTypes.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Zap className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">暂无可用功能</h3>
              <p className="text-muted-foreground">目前没有可用的功能</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {creditTypes.map((creditType) => (
            <div key={creditType.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  {getIconForCreditType(creditType.type_key)}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm mb-1 line-clamp-2">
                    {creditType.type_name.replace('扣除', '')}
                  </h3>
                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                    {creditType.description || '暂无描述'}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {creditType.credit_amount} credits起
                    </span>
                    <span className="text-xs text-blue-600">
                      {getCategoryForCreditType(creditType.type_key)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PopularFeatures;