import React from 'react';

interface ExampleImageSVGProps {
  width?: number;
  height?: number;
  type?: 'video' | 'avatar';
  className?: string;
}

export const ExampleImageSVG: React.FC<ExampleImageSVGProps> = ({ 
  width = 80, 
  height = 80, 
  type = 'video',
  className = ''
}) => (
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 80 80" 
    className={`bg-gray-100 rounded ${className}`}
  >
    <rect width="80" height="80" fill="#f3f4f6"/>
    <rect x="10" y="10" width="60" height="60" fill="#e5e7eb" rx="4"/>
    {type === 'video' ? (
      <>
        <circle cx="40" cy="35" r="8" fill="#9ca3af"/>
        <polygon points="36,31 36,39 44,35" fill="white"/>
        <rect x="15" y="50" width="50" height="4" fill="#d1d5db" rx="2"/>
        <rect x="15" y="58" width="35" height="3" fill="#d1d5db" rx="1"/>
      </>
    ) : (
      <>
        <circle cx="40" cy="30" r="8" fill="#9ca3af"/>
        <path d="M25 55 Q25 45 40 45 Q55 45 55 55 L55 65 L25 65 Z" fill="#9ca3af"/>
      </>
    )}
    <text x="40" y="75" textAnchor="middle" fontSize="8" fill="#6b7280">示例</text>
  </svg>
);
