import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { MainLayout } from './components/layout/MainLayout';
import NotFound from './pages/NotFound';
import Login from './pages/Login';
import GoogleCallback from './pages/GoogleCallback';
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import TopicSearch from './pages/topic/TopicSearch';
import UserNotes from './pages/user/UserNotes';
import TikTokUserVideos from './pages/tiktok/TikTokUserVideos';
import TikTokUserFans from './pages/tiktok/TikTokUserFans';
import YouTubeUserVideos from './pages/youtube/YouTubeUserVideos';
import YouTubePlaylistVideos from './pages/youtube/YouTubePlaylistVideos';
import YouTubeVideoExtractor from './pages/youtube/YouTubeVideoExtractor';
import DouyinVideoExtractor from './pages/douyin/DouyinVideoExtractor';
import DouyinUserVideos from './pages/douyin/DouyinUserVideos';
import DouyinCommentExtractor from './pages/douyin/DouyinCommentExtractor';
import UserList from './pages/admin/UserList';
import ProductList from './pages/admin/ProductList';
import CreditTypeList from './pages/admin/CreditTypeList';
import PointsHistory from './pages/profile/PointsHistory';
import PaymentHistory from './pages/profile/PaymentHistory';
import StripePay from './pages/payment/StripePay';
import TranscriptList from './pages/transcript/TranscriptList';
import VideoSubtitle from './pages/ai/VideoSubtitle';
import VideoToArticle from './pages/ai/VideoToArticle';
import BrowserRenderer from './pages/BrowserRenderer';
import { eventBus } from './lib/eventBus';
import { Toaster } from './components/ui/toaster';
import TikTokVideoExtractor from './pages/tiktok/TikTokVideoExtractor';
import KuaishouVideoExtractor from './pages/kuaishou/KuaishouVideoExtractor';
import KuaishouCommentsExtractor from './pages/kuaishou/KuaishouCommentsExtractor';
import XhsBatchExtract from './pages/xhs/XhsNoteInfo';
import ApiDocs from './pages/docs/ApiDocs';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredRole }) => {
  const { user, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requiredRole && user.role !== requiredRole) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

function AppRoutes() {
  const navigate = useNavigate();

  useEffect(() => {
    const handleUnauthorized = () => {
      navigate('/login');
    };

    eventBus.subscribe('unauthorized', handleUnauthorized);

    return () => {
      eventBus.unsubscribe('unauthorized', handleUnauthorized);
    };
  }, [navigate]);

  return (
    <Routes>
      <Route index element={<Home />} />
      <Route path="/login" element={<Login />} />
      <Route path="/auth/google/callback" element={<GoogleCallback />} />
      <Route path="/docs" element={<ApiDocs />} />
      {/* 将 MainLayout 包裹的路由范围缩小，不再包含 * 通配符 */}
      <Route path="/" element={<MainLayout />}>
        <Route path="dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="tools/topic-search" element={
            <TopicSearch />
        } />
        <Route path="tools/user-notes" element={
            <UserNotes />
        } />
        <Route path="tools/xhs/batch-extract" element={
            <XhsBatchExtract />
        } />
        <Route path="tools/tiktok/user-videos" element={
            <TikTokUserVideos />
        } />
        <Route path="tools/tiktok/user-fans" element={
            <TikTokUserFans />
        } />
        <Route path="tools/tiktok/video-extractor" element={
            <TikTokVideoExtractor />
        } />
        <Route path="tools/kuaishou/video-extractor" element={
            <KuaishouVideoExtractor />
        } />
        <Route path="tools/kuaishou/comments-extractor" element={
            <KuaishouCommentsExtractor />
        } />
        <Route path="tools/youtube/user-videos" element={
            <YouTubeUserVideos />
        } />
        <Route path="tools/youtube/playlist-videos" element={
            <YouTubePlaylistVideos />
        } />
        <Route path="tools/youtube/video-extractor" element={
            <YouTubeVideoExtractor />
        } />
        <Route path="tools/douyin/user-videos" element={
            <DouyinUserVideos />
        } />
        <Route path="tools/douyin/video-extractor" element={
            <DouyinVideoExtractor />
        } />
        <Route path="tools/douyin/comment-extractor" element={
            <DouyinCommentExtractor />
        } />
        <Route path="tools/browser-renderer" element={
          <ProtectedRoute>
            <BrowserRenderer />
          </ProtectedRoute>
        } />
        {/* <Route path="tools/transcript" element={<TranscriptList />} /> */}
        {/* <Route path="tools/ai/video-subtitle" element={<VideoSubtitle />} />
        <Route path="tools/ai/video-to-article" element={<VideoToArticle />} /> */}
        <Route path="profile/points-history" element={
          <ProtectedRoute>
            <PointsHistory />
          </ProtectedRoute>
        } />
        <Route path="profile/payment-history" element={
          <ProtectedRoute>
            <PaymentHistory />
          </ProtectedRoute>
        } />
        <Route path="payment/stripe" element={
            <StripePay />
        } />
        <Route path="admin">
          <Route path="users" element={
            <ProtectedRoute requiredRole="admin">
              <UserList />
            </ProtectedRoute>
          } />
          <Route path="products" element={
            <ProtectedRoute requiredRole="admin">
              <ProductList />
            </ProtectedRoute>
          } />
          <Route path="credit-types" element={
            <ProtectedRoute requiredRole="admin">
              <CreditTypeList />
            </ProtectedRoute>
          } />
        </Route>
        {/* 移除原有的 path="*" 子路由 */}
      </Route>
      {/* 将 404 路由移到 MainLayout 外层，独立渲染 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export function App() {
  return (
    <Router>
      <AuthProvider>
        <AppRoutes />
        <Toaster />
      </AuthProvider>
    </Router>
  );
}