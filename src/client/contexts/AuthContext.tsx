import React, { createContext, useContext, useState, useEffect } from 'react';
import { authApi } from '../lib/api';

interface User {
  id: number;
  username: string;
  role: string;
  api_key: string;
  credits: number;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  login: (loginData: {
    loginType: 'password' | 'google' | 'api_key' | 'email';
    username?: string;
    password?: string;
    api_key?: string;
    google_token?: string;
    email?: string;
    code?: string;
  }) => Promise<void>;
  logout: () => void;
  setUser: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      if (token) {
        try {
          const response = await authApi.getCurrentUser();
          setUser(response.data?.user);
        } catch (error) {
          console.error('Failed to get current user:', error);
          logout();
        }
      }
      setIsLoading(false);
    };

    initAuth();
  }, [token]);

  const login = async (loginData: {
    loginType: 'password' | 'google' | 'api_key' | 'email';
    username?: string;
    password?: string;
    api_key?: string;
    google_token?: string;
    email_verified?: string;
  }) => {
    try {
      const response = await authApi.login(loginData);
      const { token: newToken, user: newUser } = response.data ?? {};
      
      localStorage.setItem('token', newToken ?? '');
      setToken(newToken ?? null);
      setUser(newUser ?? null);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, token, isLoading, login, logout, setUser }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}