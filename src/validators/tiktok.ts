import { z } from 'zod';

/**
 * 从TikTok URL中提取用户名或用户ID
 */
function extractTikTokIdentifier(url: string): string {
	// 移除所有空格和换行符
	const cleanUrl = url.replace(/\s+/g, '');
	
	// 匹配各种TikTok URL格式
	const patterns = [
		// @username 格式
		/@([a-zA-Z0-9_.]+)/,
		// tiktok.com/@username 格式
		/tiktok\.com\/@([a-zA-Z0-9_.]+)/,
		// vm.tiktok.com 短链
		/vm\.tiktok\.com\/([A-Za-z0-9]+)/,
		// www.tiktok.com/@username
		/www\.tiktok\.com\/@([a-zA-Z0-9_.]+)/,
		// 直接的用户名（没有@符号）
		/^([a-zA-Z0-9_.]+)$/
	];
	
	for (const pattern of patterns) {
		const match = cleanUrl.match(pattern);
		if (match && match[1]) {
			return match[1];
		}
	}
	
	// 如果没有匹配到，直接返回清理后的URL
	return cleanUrl;
}

/**
 * 获取TikTok用户信息的schema
 */
export const getTikTokUserInfoSchema = z.object({
	url: z.string()
		.min(1, '请输入TikTok用户链接或用户名')
		.transform((val) => {
			const identifier = extractTikTokIdentifier(val);
			if (!identifier) {
				throw new Error('无效的TikTok用户链接或用户名');
			}
			return identifier;
		})
		.refine((val) => {
			// 验证提取出的标识符是否有效
			return val && val.length > 0 && /^[a-zA-Z0-9_.]+$/.test(val);
		}, {
			message: '请输入有效的TikTok用户名或链接'
		})
});

/**
 * 获取TikTok用户作品的schema
 */
export const getTikTokUserPostsSchema = z.object({
	secUserId: z.string()
		.min(1, '请输入TikTok用户secUserId')
		.refine((val) => {
			// 验证secUserId格式
			return val && val.length > 0;
		}, {
			message: '请输入有效的TikTok用户secUserId'
		}),
	cursor: z.string().optional(), // 用于分页
	count: z.number().min(15).max(15).default(15).optional() // 每页数量，默认20，最大50
});

/**
 * 获取TikTok用户粉丝的schema
 */
export const getTikTokUserFansSchema = z.object({
	secUid: z.string()
		.min(1, '请输入TikTok用户secUid')
		.refine((val) => {
			// 验证secUid格式
			return val && val.length > 0;
		}, {
			message: '请输入有效的TikTok用户secUid'
		}),
	minCursor: z.string().optional(), // 用于分页
});

export const getTikTokVideoDetailSchema = z.object({
	url: z.string()
		.min(1, '请输入TikTok视频链接或ID')
		.refine((val) => {
			// 验证videoId格式
			return val && val.length > 0;
		}, {
			message: '请输入TikTok视频链接或ID'
		})
});

export type GetTikTokUserInfoSchema = z.infer<typeof getTikTokUserInfoSchema>;
export type GetTikTokUserPostsSchema = z.infer<typeof getTikTokUserPostsSchema>;
export type GetTikTokUserFansSchema = z.infer<typeof getTikTokUserFansSchema>;
export type GetTikTokVideoDetailSchema = z.infer<typeof getTikTokVideoDetailSchema>;