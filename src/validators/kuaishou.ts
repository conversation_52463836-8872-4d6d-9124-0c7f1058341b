import { z } from 'zod';

// 获取快手视频详情的schema
export const getKuaishouVideoDetailSchema = z.object({
	url: z.string()
		.min(1, '请输入快手视频链接')
		.refine((val) => {
			// 验证是否为有效的快手视频链接
			const patterns = [
				/kuaishou\.com\/[^\s]*/,
				/kwai\.com\/[^\s]*/,
				/kw\.ai\/[^\s]*/,
				/v\.kuaishou\.com\/[^\s]*/,
				/ks\.app\/[^\s]*/
			];
			return patterns.some(pattern => pattern.test(val));
		}, {
			message: '请输入有效的快手视频链接'
		})
});

// 获取快手评论列表的schema
export const getKuaishouCommentsSchema = z.object({
	photoId: z.string().min(1, '请输入视频ID'),
	authorId: z.string().min(1, '请输入作者ID'),
	pcursor: z.string().optional()
});

// 获取快手回复列表的schema
export const getKuaishouRepliesSchema = z.object({
	photoId: z.string().min(1, '请输入视频ID'),
	authorId: z.string().min(1, '请输入作者ID'),
	rootCommentId: z.string().min(1, '请输入根评论ID'),
	pcursor: z.string().optional()
});

export type GetKuaishouVideoDetailSchema = z.infer<typeof getKuaishouVideoDetailSchema>;
export type GetKuaishouCommentsSchema = z.infer<typeof getKuaishouCommentsSchema>;
export type GetKuaishouRepliesSchema = z.infer<typeof getKuaishouRepliesSchema>;
