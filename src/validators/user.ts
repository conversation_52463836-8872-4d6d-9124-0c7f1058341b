import { z } from 'zod';

// User creation validation schema
export const createUserSchema = z.object({
  username: z.string().min(2, '用户名至少需要2个字符'),
  password: z.string().min(6, '密码至少需要6个字符'),
  role: z.enum(['admin', 'user']).optional()
});

export type CreateUserSchema = z.infer<typeof createUserSchema>;

// User update validation schema
export const updateUserSchema = createUserSchema.partial();

export type UpdateUserSchema = z.infer<typeof updateUserSchema>; 


export const getUserCreditRecordSchema = z.object({
  page: z.string().default('1'),
  pageSize: z.string().default('20'),
  username: z.string().optional(),
  changeType: z.string().optional(),
});

export type GetUserCreditRecordSchema = z.infer<typeof getUserCreditRecordSchema>;