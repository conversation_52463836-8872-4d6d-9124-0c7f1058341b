import { z } from 'zod';

// 网页渲染请求验证器
export const renderRequestSchema = z.object({
	url: z.string()
		.min(1, 'URL不能为空')
		.refine((val) => {
			try {
				new URL(val);
				return true;
			} catch {
				return false;
			}
		}, {
			message: '请输入有效的URL格式'
		}),
	renderType: z.enum(['pdf', 'html', 'markdown', 'screenshot']).default('pdf'),
	options: z.object({
		// PDF选项
		format: z.enum(['A4', 'A3', 'A5', 'Letter', 'Legal', 'Tabloid']).optional(),
		landscape: z.boolean().optional(),
		printBackground: z.boolean().optional(),
		margin: z.object({
			top: z.string().optional(),
			bottom: z.string().optional(),
			left: z.string().optional(),
			right: z.string().optional()
		}).optional(),
		// HTML选项
		includeStyles: z.boolean().optional(),
		cleanContent: z.boolean().optional(),
		// Markdown选项
		headingStyle: z.enum(['atx', 'setext']).optional(),
		codeBlockStyle: z.enum(['fenced', 'indented']).optional(),
		// 截图选项
		viewportWidth: z.number().min(320).max(1920).optional(),
		viewportHeight: z.number().min(240).max(1080).optional(),
		fullPage: z.boolean().optional(),
		omitBackground: z.boolean().optional(),
		// 通用选项
		waitUntil: z.enum(['load', 'domcontentloaded', 'networkidle0', 'networkidle2']).optional(),
		timeout: z.number().min(1000).max(60000).optional(),
		headers: z.record(z.string()).optional()
	}).default({})
});

// 渲染类型查询验证器
export const renderTypesQuerySchema = z.object({
	detailed: z.boolean().optional().default(false)
});

// 导出类型
export type RenderRequestSchema = z.infer<typeof renderRequestSchema>;
export type RenderTypesQuerySchema = z.infer<typeof renderTypesQuerySchema>;
