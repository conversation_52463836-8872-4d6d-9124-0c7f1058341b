

import { z } from 'zod';

// User creation validation schema
export const getUserListSchema = z.object({
  page: z.number().max(1000).min(1).default(1),
  pageSize: z.number().max(20).min(1).default(20),
  username: z.string().optional(),
  status: z.number().optional(),
});

export type GetUserListSchema = z.infer<typeof getUserListSchema>;



export const updateUserStatusSchema = z.object({
  id: z.coerce.number(),
  status: z.number(),
});

export type UpdateUserStatusSchema = z.infer<typeof updateUserStatusSchema>;

export const updateUserCreditSchema = z.object({
  id: z.coerce.number(),
  changeType: z.enum(['register_send_credit', 'system_add_credit', 'xhs_topic_deduct', 'xhs_user_note_deduct', 'xhs_topic_note_deduct']),
  changeCredit: z.number(),
  changeReason: z.string(),
});

export type UpdateUserCreditSchema = z.infer<typeof updateUserCreditSchema>;

export const updateUserRemarkSchema = z.object({
  id: z.coerce.number(),
  remark: z.string().optional(),
});

export type UpdateUserRemarkSchema = z.infer<typeof updateUserRemarkSchema>;