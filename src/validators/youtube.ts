import { z } from 'zod';

/**
 * 验证并补全YouTube频道URL格式
 */
function validateAndCompleteYouTubeChannelUrl(url: string): string {
	// 移除所有空格和换行符
	const cleanUrl = url.replace(/\s+/g, '');
	
	// 如果已经是完整的YouTube URL，直接返回
	if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
		return cleanUrl;
	}
	
	// 如果是@username格式，补全为完整URL
	if (cleanUrl.startsWith('@')) {
		return `https://www.youtube.com/${cleanUrl}`;
	}
	
	// 如果是直接的频道ID或用户名，判断格式并补全
	if (/^[a-zA-Z0-9_.]+$/.test(cleanUrl)) {
		// 如果以UC开头，很可能是频道ID
		if (cleanUrl.startsWith('UC')) {
			return `https://www.youtube.com/channel/${cleanUrl}`;
		}
		// 否则作为用户名处理，添加@符号
		else {
			return `https://www.youtube.com/@${cleanUrl}`;
		}
	}
	
	// 如果都不匹配，返回原始URL
	return cleanUrl;
}

/**
 * 验证并补全YouTube URL格式（根据类型）
 */
function validateAndCompleteYouTubeUrl(url: string, type: 'videos' | 'shorts' | 'playlists'): string {
	// 移除所有空格和换行符
	const cleanUrl = url.replace(/\s+/g, '');
	
	if (type === 'playlists') {
		// 如果已经是完整的playlist URL，直接返回
		if (cleanUrl.includes('youtube.com/playlist')) {
			return cleanUrl;
		}
		
		// 如果是直接的playlist ID，补全为完整URL
		if (/^(PL|UU)[a-zA-Z0-9_-]+$/.test(cleanUrl) || /^[a-zA-Z0-9_-]{34}$/.test(cleanUrl)) {
			return `https://www.youtube.com/playlist?list=${cleanUrl}`;
		}
		
		// 其他情况直接返回
		return cleanUrl;
	}
	
	// 对于videos和shorts，使用原有的频道URL处理逻辑
	return validateAndCompleteYouTubeChannelUrl(cleanUrl);
}

/**
 * 验证YouTube频道URL格式
 */
function validateYouTubeChannelUrl(url: string): boolean {
	// 移除所有空格和换行符
	const cleanUrl = url.replace(/\s+/g, '');
	
	// 匹配各种YouTube频道URL格式
	const patterns = [
		// @username 格式
		/@([a-zA-Z0-9_.]+)/,
		// youtube.com/@username 格式
		/youtube\.com\/@([a-zA-Z0-9_.]+)/,
		// youtube.com/channel/UCxxxxx 格式
		/youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
		// youtube.com/c/channelname 格式
		/youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
		// youtube.com/user/username 格式
		/youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
		// 直接的频道ID或用户名（没有@符号）
		/^([a-zA-Z0-9_.]+)$/
	];
	
	return patterns.some(pattern => pattern.test(cleanUrl));
}

/**
 * 验证YouTube URL格式（根据类型）
 */
function validateYouTubeUrl(url: string, type: 'videos' | 'shorts' | 'playlists'): boolean {
	// 移除所有空格和换行符
	const cleanUrl = url.replace(/\s+/g, '');
	
	if (type === 'playlists') {
		// 验证playlist链接格式
		const playlistPatterns = [
			// youtube.com/playlist?list=PLxxxxx 格式
			/youtube\.com\/playlist\?list=([a-zA-Z0-9_-]+)/,
			// 包含其他参数的playlist链接
			/youtube\.com\/playlist\?.*list=([a-zA-Z0-9_-]+)/,
			// 直接的playlist ID（PL开头）
			/^PL[a-zA-Z0-9_-]+$/,
			// 其他格式的playlist ID
			/^[a-zA-Z0-9_-]{34}$/,
			// UU开头的playlist ID
			/^UU[a-zA-Z0-9_-]+$/
		];
		return playlistPatterns.some(pattern => pattern.test(cleanUrl));
	}
	
	// 对于videos和shorts，使用原有的频道URL验证逻辑
	return validateYouTubeChannelUrl(cleanUrl);
}

// 获取YouTube用户信息的schema
export const getYouTubeUserInfoSchema = z.object({
	url: z.string()
		.min(1, '请输入YouTube频道链接或频道ID')
		.transform(validateAndCompleteYouTubeChannelUrl)
		.refine(validateYouTubeChannelUrl, {
			message: '无效的YouTube频道链接或频道ID'
		})
});

// 获取YouTube用户视频列表的schema
export const getYouTubeUserVideosSchema = z.object({
	url: z.string().min(1, '请输入YouTube链接'),
	start: z.number().int().min(0).default(0),
	end: z.number().int().min(1).default(20),
	type: z.enum(['videos', 'shorts', 'playlists']).default('videos'),
}).transform((data) => {
	// 根据type类型进行URL转换和验证
	const transformedUrl = validateAndCompleteYouTubeUrl(data.url, data.type);
	return {
		...data,
		url: transformedUrl
	};
}).refine((data) => {
	// 验证转换后的URL是否有效
	return validateYouTubeUrl(data.url, data.type);
}, {
	message: '请输入有效的YouTube链接',
	path: ['url']
});

// 获取YouTube视频详情的schema
export const getYouTubeVideoInfoSchema = z.object({
	url: z.string()
		.min(1, '请输入YouTube视频链接')
		.transform((val) => {
			// 如果是YouTube链接且包含list参数，则去除list参数
			try {
				const url = new URL(val);
				if (url.hostname.includes('youtube.com') && url.searchParams.has('list')) {
					// 保留v参数和其他非list参数
					url.searchParams.delete('list');
					return url.toString();
				}
			} catch {
				// 如果URL解析失败，返回原值
			}
			return val;
		})
		.refine((val) => {
			// 验证是否为有效的YouTube视频链接
			const patterns = [
				/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/,
				/youtu\.be\/([a-zA-Z0-9_-]+)/,
				/youtube\.com\/shorts\/([a-zA-Z0-9_-]+)/,
				/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/
			];
			return patterns.some(pattern => pattern.test(val));
		}, {
			message: '请输入有效的YouTube视频链接'
		})
});

// 获取YouTube视频元数据的schema
export const getYouTubeVideoSchema = z.object({
	id: z.string().min(1, '请输入YouTube视频ID或URL')
});

// 获取YouTube频道信息的schema
export const getYouTubeChannelSchema = z.object({
	channel_id: z.string().min(1, '请输入YouTube频道ID或URL')
});

// 获取YouTube频道视频列表的schema
export const getYouTubeChannelVideosSchema = z.object({
	channel_id: z.string().min(1, '请输入YouTube频道ID或URL'),
	limit: z.number().int().min(1).max(100).optional(),
	type: z.enum(['all', 'video', 'short', 'live']).optional()
});

// 获取YouTube播放列表视频的schema
export const getYouTubePlaylistVideosSchema = z.object({
	playlist_id: z.string().min(1, '请输入YouTube播放列表ID或URL'),
	limit: z.number().int().min(1).max(100).optional()
});

// 获取YouTube视频转录的schema
export const getYouTubeTranscriptSchema = z.object({
	video_id: z.string().min(1, '请输入YouTube视频ID或URL'),
	lang: z.string().optional(),
	text: z.boolean().optional(),
	chunkSize: z.number().int().min(1).optional()
});

// 获取YouTube视频翻译的schema
export const getYouTubeTranslationSchema = z.object({
	video_id: z.string().min(1, '请输入YouTube视频ID或URL'),
	lang: z.string().min(1, '请输入目标语言代码'),
	text: z.boolean().optional(),
	chunkSize: z.number().int().min(1).optional()
});

// 导出类型
export type GetYouTubeUserInfoSchema = z.infer<typeof getYouTubeUserInfoSchema>;
export type GetYouTubeUserVideosSchema = z.infer<typeof getYouTubeUserVideosSchema>;
export type GetYouTubeVideoInfoSchema = z.infer<typeof getYouTubeVideoInfoSchema>;
export type GetYouTubeVideoSchema = z.infer<typeof getYouTubeVideoSchema>;
export type GetYouTubeChannelSchema = z.infer<typeof getYouTubeChannelSchema>;
export type GetYouTubeChannelVideosSchema = z.infer<typeof getYouTubeChannelVideosSchema>;
export type GetYouTubePlaylistVideosSchema = z.infer<typeof getYouTubePlaylistVideosSchema>;
export type GetYouTubeTranscriptSchema = z.infer<typeof getYouTubeTranscriptSchema>;
export type GetYouTubeTranslationSchema = z.infer<typeof getYouTubeTranslationSchema>;