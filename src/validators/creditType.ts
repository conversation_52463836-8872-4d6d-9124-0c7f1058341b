import { z } from 'zod';

// 创建积分类型的验证模式
export const createCreditTypeSchema = z.object({
	type_key: z.string().min(1, '积分类型键值不能为空').max(50, '积分类型键值不能超过50个字符'),
	type_name: z.string().min(1, '积分类型名称不能为空').max(100, '积分类型名称不能超过100个字符'),
	credit_amount: z.number().int().min(0, '积分数量不能小于0'),
	operation_type: z.enum(['add', 'deduct'], {
		errorMap: () => ({ message: '操作类型必须是 add 或 deduct' })
	}),
	description: z.string().max(500, '描述不能超过500个字符').optional(),
});

// 更新积分类型的验证模式
export const updateCreditTypeSchema = createCreditTypeSchema.partial();

// 积分类型ID验证模式
export const creditTypeIdSchema = z.object({
	id: z.string().transform((val) => {
		const num = parseInt(val, 10);
		if (isNaN(num) || num <= 0) {
			throw new Error('无效的积分类型ID');
		}
		return num;
	}),
});

// 导出类型
export type CreateCreditTypeSchema = z.infer<typeof createCreditTypeSchema>;
export type UpdateCreditTypeSchema = z.infer<typeof updateCreditTypeSchema>;
export type CreditTypeIdSchema = z.infer<typeof creditTypeIdSchema>;