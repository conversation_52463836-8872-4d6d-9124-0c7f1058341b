import { z } from 'zod';

export const createProductSchema = z.object({
	name: z.string().min(1, '商品名称不能为空'),
	description: z.string().optional(),
	currency: z.string().min(1, '货币不能为空'),
	price: z.number().min(0, '价格不能为负数'),
	credits: z.number().int().min(0, '积分不能为负数').default(0),
	is_active: z.number().int().min(0).max(1).default(1),
});

export const updateProductSchema = z.object({
	id: z.preprocess(
		(val) => typeof val === 'string' ? Number(val) : val,
		z.number().int().min(1, '商品ID不能为空')
	),
	name: z.string().min(1, '商品名称不能为空').optional(),
	currency: z.string().min(1, '货币不能为空').optional(),
	description: z.string().optional(),
	price: z.number().min(0, '价格不能为负数').optional(),
	credits: z.number().int().min(0, '积分不能为负数').optional(),
	is_active: z.number().int().min(0).max(1).optional(),
});

export const getProductListSchema = z.object({
	name: z.string().optional(),
	currency: z.string().optional(),
	page: z.number().int().min(1).default(1),
	pageSize: z.number().int().min(1).max(100).default(10),
	credits: z.number().int().min(0, '积分不能为负数').optional(),
	is_active: z.number().int().min(0).max(1).optional(),
});

export const deleteProductSchema = z.object({
	id: z.preprocess(
		(val) => typeof val === 'string' ? Number(val) : val,
		z.number().int().min(1, '商品ID不能为空')
	),
});

export const getProductDetailSchema = z.object({
	id: z.preprocess(
		(val) => typeof val === 'string' ? Number(val) : val,
		z.number().int().min(1, '商品ID不能为空')
	),
});

export type CreateProductSchema = z.infer<typeof createProductSchema>;
export type UpdateProductSchema = z.infer<typeof updateProductSchema>;
export type GetProductListSchema = z.infer<typeof getProductListSchema>;
export type DeleteProductSchema = z.infer<typeof deleteProductSchema>;
export type GetProductDetailSchema = z.infer<typeof getProductDetailSchema>;