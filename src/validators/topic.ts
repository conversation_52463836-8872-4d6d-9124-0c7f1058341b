import { z } from 'zod';

export const topicSearchSchema = z.object({
  keyword: z.string().min(1, '搜索关键词不能为空')
});

export const topicNotesSchema = z.object({
  pageId: z.string().min(1, '话题ID不能为空'),
  cursor: z.string().optional(),
  sort: z.enum(['hot', 'time', 'trend']).default('hot'),
  lastNoteId: z.string().optional(),
  lastNoteCt: z.string().optional()
}); 


export const creatorCookieSchema = z.object({
  cookie: z.string().min(1, 'cookie不能为空'),
});