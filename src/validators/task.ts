import { z } from 'zod';

// Login validation schema
export const taskCreateSchema = z.object({
  media_url: z.string().url(),
  proxy_media_url: z.string().url(),
  task_type: z.string().default("media_transcription"),
  language: z.string(),
  model: z.string().default("whisper-large-v3-turbo"),
  country_code: z.string().optional(),
  message: z.string().optional(),
	callback_url: z.string().default(""),
  extra: z.record(z.any()).optional()
});

export type TaskCreateSchema = z.infer<typeof taskCreateSchema>;


export const taskUpdateSchema = z.object({
    task_id: z.string(),
    status: z.enum(['pending', 'processing', 'completed', 'failed']),
    sub_status: z.string().optional(),
    percent: z.number(),
    object_key: z.string().optional(),
    result: z.string().optional(),
    error: z.string().optional()
})

export type TaskUpdateSchema = z.infer<typeof taskUpdateSchema>;

export const taskInfoSchema = z.object({
    task_id: z.string(),
})

export type TaskInfoSchema = z.infer<typeof taskInfoSchema>;


export const taskQuerySchema = z.object({
  task_ids: z.array(z.string()).optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']).optional(),
})

export type TaskQuerySchema = z.infer<typeof taskQuerySchema>;
