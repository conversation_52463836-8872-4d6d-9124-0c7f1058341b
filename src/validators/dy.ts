import { z } from 'zod';

/**
 * 从分享文本中提取抖音URL
 */
function extractDouyinUrl(text: string): string {
	// 移除所有空格和换行符
	const cleanText = text.replace(/\s+/g, '');

	// 匹配各种抖音URL格式
	const urlPatterns = [
		// v.douyin.com 短链
		/https?:\/\/v\.douyin\.com\/[A-Za-z0-9_-]+/g,
		// www.douyin.com 完整链接
		/https?:\/\/(?:www\.)?douyin\.com\/[^\s]*/g,
		// iesdouyin.com 链接
		/https?:\/\/(?:www\.)?iesdouyin\.com\/[^\s]*/g,
		// 处理没有协议的链接
		/v\.douyin\.com\/[A-Za-z0-9_-]+/g,
		/(?:www\.)?douyin\.com\/[^\s]*/g,
	];

	for (const pattern of urlPatterns) {
		const matches = cleanText.match(pattern);
		if (matches && matches.length > 0) {
			let url = matches[0];
			// 如果没有协议，添加https
			if (!url.startsWith('http')) {
				url = 'https://' + url;
			}
			return url;
		}
	}

	// 如果没有找到URL，返回原始文本（可能本身就是URL）
	return text.trim();
}

export const getXtVideoInfo = z.object({
	url: z
		.string()
		.min(1, 'url不能为空')
		.transform((val) => {
			// 提取并清理URL
			const extractedUrl = extractDouyinUrl(val);

			// 验证提取的URL是否为有效的抖音链接
			const douyinDomains = ['v.douyin.com', 'www.douyin.com', 'douyin.com', 'iesdouyin.com', 'www.iesdouyin.com'];

			const isValidDouyinUrl = douyinDomains.some((domain) => extractedUrl.includes(domain));

			if (!isValidDouyinUrl) {
				throw new Error('请提供有效的抖音链接');
			}

			return extractedUrl;
		}),
});

export type GetXtVideoInfoSchema = z.infer<typeof getXtVideoInfo>;

export const getDouyinUserVideos = z.object({
	url: z
		.string()
		.min(1, 'url不能为空')
		.transform((val) => {
			// 提取并清理URL
			const extractedUrl = extractDouyinUrl(val);

			// 验证提取的URL是否为有效的抖音链接
			const douyinDomains = ['v.douyin.com', 'www.douyin.com', 'douyin.com', 'iesdouyin.com', 'www.iesdouyin.com'];

			const isValidDouyinUrl = douyinDomains.some((domain) => extractedUrl.includes(domain));

			if (!isValidDouyinUrl) {
				throw new Error('请提供有效的抖音用户链接');
			}

			return extractedUrl;
		}),
	maxCursor: z.number().optional(),
});

export type GetDouyinUserVideosSchema = z.infer<typeof getDouyinUserVideos>;

export const getDouyinVideoComments = z.object({
	url: z
		.string()
		.min(1, 'url不能为空')
		.transform((val) => {
			// 提取并清理URL
			const extractedUrl = extractDouyinUrl(val);

			// 验证提取的URL是否为有效的抖音链接
			const douyinDomains = ['v.douyin.com', 'www.douyin.com', 'douyin.com', 'iesdouyin.com', 'www.iesdouyin.com'];

			const isValidDouyinUrl = douyinDomains.some((domain) => extractedUrl.includes(domain));

			if (!isValidDouyinUrl) {
				throw new Error('请提供有效的抖音视频链接');
			}

			return extractedUrl;
		}),
	video_id: z.string().optional(), // 可选的视频ID，如果提供则优先使用
	cursor: z
		.union([z.string(), z.number()])
		.optional()
		.default(0)
		.transform((val) => {
			if (val === undefined || val === null || val === '') {
				return undefined;
			}
			const numVal = Number(val);
			if (isNaN(numVal) || !Number.isInteger(numVal) || numVal < 0) {
				throw new Error('cursor必须是非负整数');
			}
			return numVal;
		}),
});

export type GetDouyinVideoCommentsSchema = z.infer<typeof getDouyinVideoComments>;

export const getDouyinCommentReplies = z.object({
	aweme_id: z.string().min(1, 'aweme_id不能为空'),
	comment_id: z.string().min(1, 'comment_id不能为空'),
	cursor: z
		.union([z.string(), z.number()])
		.optional()
		.default(0)
		.transform((val) => {
			if (val === undefined || val === null || val === '') {
				return undefined;
			}
			const numVal = Number(val);
			if (isNaN(numVal) || !Number.isInteger(numVal) || numVal < 0) {
				throw new Error('cursor必须是非负整数');
			}
			return numVal;
		}),
});

export type GetDouyinCommentRepliesSchema = z.infer<typeof getDouyinCommentReplies>;
