import { z } from 'zod';

// 同步商品到 Stripe
export const syncProductToStripeSchema = z.object({
  productId: z.number().int().positive(),
});

// 获取会话状态
export const getPaymentStatusSchema = z.object({
  paymentIntent: z.string().min(1),
});

// 创建 Payment Intent
export const createPaymentIntentSchema = z.object({
  productId: z.number().int().positive(),
  quantity: z.number().int().positive().default(1),
});

// Webhook 事件处理
export const webhookEventSchema = z.object({
  type: z.string(),
  data: z.object({
    object: z.any(),
  }),
  id: z.string(),
  created: z.number(),
  livemode: z.boolean(),
  pending_webhooks: z.number(),
  request: z.object({
    id: z.string().nullable(),
    idempotency_key: z.string().nullable(),
  }).nullable(),
});

export type SyncProductToStripeSchema = z.infer<typeof syncProductToStripeSchema>;
export type GetPaymentStatusSchema = z.infer<typeof getPaymentStatusSchema>;
export type CreatePaymentIntentSchema = z.infer<typeof createPaymentIntentSchema>;
export type WebhookEventSchema = z.infer<typeof webhookEventSchema>;