import { z } from 'zod';

// 登录类型枚举
export const LoginType = z.enum(['password', 'google', 'api_key', 'email']);

// 基础登录 schema
const baseLoginSchema = z.object({
  loginType: LoginType,
});

// 用户名密码登录
const passwordLoginSchema = baseLoginSchema.extend({
  loginType: z.literal('password'),
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
});

// Google 登录
const googleLoginSchema = baseLoginSchema.extend({
  loginType: z.literal('google'),
  google_token: z.string().min(1, 'Google Token 不能为空'),
});

// API Key 登录
const apiKeyLoginSchema = baseLoginSchema.extend({
  loginType: z.literal('api_key'),
  api_key: z.string().min(1, 'API Key 不能为空'),
});

// 邮箱验证码登录
const emailLoginSchema = baseLoginSchema.extend({
  loginType: z.literal('email'),
  email: z.string().email('请输入有效的邮箱地址'),
  code: z.string().regex(/^\d{6}$/, '验证码必须是6位数字'),
});

// 联合登录 schema
export const loginSchema = z.discriminatedUnion('loginType', [
  passwordLoginSchema,
  googleLoginSchema,
  apiKeyLoginSchema,
  emailLoginSchema,
]);

// 导出类型
export type LoginRequest = z.infer<typeof loginSchema>;
export type LoginTypeEnum = z.infer<typeof LoginType>;
