import { z } from 'zod';

export const getUserNotesSchema = z.object({
	userId: z.string().min(1, '用户ID不能为空'),
	lastCursor: z.string().optional(),
});

export type GetUserNotesSchema = z.infer<typeof getUserNotesSchema>;


export const getNoteDetailSchema = z.object({
	url: z.string().optional(),
	noteId: z.string().optional(),
}).refine(
	(data) => data.url || data.noteId,
	{
		message: 'url 和 noteId 不能同时为空，至少需要提供其中一个',
		path: ['url', 'noteId'],
	}
);

export type GetNoteDetailSchema = z.infer<typeof getNoteDetailSchema>;

export const getNoteInfoSchema = z.object({
	url: z.string().min(1, '笔记URL不能为空'),
});

export type GetNoteInfoSchema = z.infer<typeof getNoteInfoSchema>;