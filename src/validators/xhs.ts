import { z } from 'zod';

export const getUserNotesSchema = z.object({
	userId: z.string().min(1, '用户ID不能为空'),
	lastCursor: z.string().optional(),
});

export type GetUserNotesSchema = z.infer<typeof getUserNotesSchema>;


export const getNoteDetailSchema = z.object({
	url: z.string().optional(),
	noteId: z.string().optional(),
}).refine(
	(data) => data.url || data.noteId,
	{
		message: 'url 和 noteId 不能同时为空，至少需要提供其中一个',
		path: ['url', 'noteId'],
	}
);

export type GetNoteDetailSchema = z.infer<typeof getNoteDetailSchema>;

export const getNoteInfoSchema = z.object({
	url: z.string().min(1, '笔记URL不能为空'),
});

export type GetNoteInfoSchema = z.infer<typeof getNoteInfoSchema>;

export const noteSearchSchema = z.object({
	keyword: z.string().min(1, '关键词不能为空'),
	page: z.number().int().positive('页码必须是正整数').default(1),
	sort: z.enum(['general', 'popularity_descending', 'time_descending', 'comment_descending', 'collect_descending']).default('general'),
	noteType: z.enum(['_0', '_1', '_2', '_3']).default('_0'),
	noteTime: z.enum(['一天内', '一周内', '半年内']).optional(),
});

export type NoteSearchSchema = z.infer<typeof noteSearchSchema>;