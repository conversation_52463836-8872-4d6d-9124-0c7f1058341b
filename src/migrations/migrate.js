const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const migrationsDir = 'src/migrations';

if (!fs.existsSync(migrationsDir)) {
  console.log('Migrations directory not found');
  process.exit(1);
}

const sqlFiles = fs.readdirSync(migrationsDir)
  .filter(file => file.endsWith('.sql'))
  .sort(); // 确保按文件名顺序执行

if (sqlFiles.length === 0) {
  console.log('No SQL migration files found');
  process.exit(0);
}

console.log(`Found ${sqlFiles.length} migration file(s)`);

sqlFiles.forEach((file, index) => {
  const filePath = path.join(migrationsDir, file);
  console.log(`[${index + 1}/${sqlFiles.length}] Executing migration: ${filePath}`);
  
  try {
    execSync(`npx wrangler d1 execute datatool_d1 --local --file="${filePath}"`, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    console.log(`✓ Migration ${file} completed successfully`);
  } catch (error) {
    console.error(`✗ Migration ${file} failed:`, error.message);
    process.exit(1);
  }
});

console.log('All migrations completed successfully!');