-- 创建积分类型表
CREATE TABLE IF NOT EXISTS t_credit_type (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type_key TEXT NOT NULL UNIQUE,
    type_name TEXT NOT NULL,
    credit_amount INTEGER NOT NULL DEFAULT 0,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('add', 'deduct')),
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_credit_type_key ON t_credit_type(type_key);
CREATE INDEX IF NOT EXISTS idx_credit_type_operation ON t_credit_type(operation_type);

-- 插入默认积分类型数据
INSERT OR IGNORE INTO t_credit_type (type_key, type_name, credit_amount, operation_type, description) VALUES
('xhs_note_business_data', '小红书笔记商业数据', 10, 'deduct', '小红书笔记商业数据'),
('register_send_credit', '注册赠送', 20, 'add', '新用户注册获得的积分奖励'),
('system_add_credit', '系统添加', 10, 'add', '系统添加积分'),
('tiktok_video_detail_deduct', 'TikTok视频详情扣除', 10, 'deduct', 'TikTok视频详情获取消耗的积分'),
('stripe_payment_credit', 'Stripe支付获得', 1, 'add', '通过Stripe支付获得的积分'),
('xhs_topic_deduct', '小红书话题扣除', 2, 'deduct', '小红书话题搜索消耗的积分'),
('xhs_user_note_deduct', '小红书用户笔记扣除', 10, 'deduct', '小红书用户笔记获取消耗的积分'),
('xhs_topic_note_deduct', '小红书话题笔记扣除', 10, 'deduct', '小红书话题笔记获取消耗的积分'),
('xhs_note_detail_deduct', '小红书笔记详情扣除', 3, 'deduct', '小红书笔记详情获取消耗的积分'),
('dy_xt_video_deduct', '抖音星图视频扣除', 3, 'deduct', '抖音星图视频数据获取消耗的积分'),
('dy_video_detail_deduct', '抖音视频详情扣除', 2, 'deduct', '抖音视频详情获取消耗的积分'),
('dy_user_videos_deduct', '抖音用户视频扣除', 10, 'deduct', '抖音用户视频获取消耗的积分'),
('dy_video_comments_deduct', '抖音视频评论扣除', 5, 'deduct', '抖音视频评论获取消耗的积分'),
('dy_comment_replies_deduct', '抖音评论回复扣除', 3, 'deduct', '抖音评论回复获取消耗的积分'),
('tiktok_user_info_deduct', 'TikTok用户信息扣除', 1, 'deduct', 'TikTok用户信息获取消耗的积分'),
('tiktok_user_posts_deduct', 'TikTok用户作品扣除', 2, 'deduct', 'TikTok用户作品获取消耗的积分'),
('tiktok_user_fans_deduct', 'TikTok用户粉丝扣除', 2, 'deduct', 'TikTok用户粉丝获取消耗的积分'),
('youtube_user_info_deduct', 'YouTube用户信息扣除', 1, 'deduct', 'YouTube用户信息获取消耗的积分'),
('youtube_user_videos_deduct', 'YouTube用户视频扣除', 2, 'deduct', 'YouTube用户视频获取消耗的积分'),
('youtube_user_shorts_deduct', 'YouTube用户Shorts扣除', 2, 'deduct', 'YouTube用户Shorts获取消耗的积分'),
('youtube_user_playlists_deduct', 'YouTube用户播放列表扣除', 2, 'deduct', 'YouTube用户播放列表获取消耗的积分'),
('youtube_video_info_deduct', 'YouTube视频详情扣除', 1, 'deduct', 'YouTube视频详情获取消耗的积分'),
('kuaishou_video_detail_deduct', '快手视频详情扣除', 2, 'deduct', '快手视频详情获取消耗的积分'),
('kuaishou_comments_deduct', '快手评论列表扣除', 3, 'deduct', '快手评论列表获取消耗的积分'),
('kuaishou_replies_deduct', '快手回复列表扣除', 2, 'deduct', '快手回复列表获取消耗的积分');