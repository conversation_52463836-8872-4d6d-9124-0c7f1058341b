CREATE TABLE IF NOT EXISTS t_user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE,
    password TEXT,
    api_key TEXT UNIQUE,
    role TEXT,
    status INTEGER DEFAULT 1,
    remark TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    last_login_at TEXT,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- dataminer@2025.
INSERT
    OR IGNORE INTO t_user (
        username,
        password,
        role,
        status
    )
VALUES (
        'admin',
        '544b719505b70aee80902f6afc3596c6',
        'admin',
        1
    );

CREATE TABLE IF NOT EXISTS t_user_credit (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE,
    credit INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

INSERT
    OR IGNORE INTO t_user_credit (id, user_id, credit)
VALUES (1, 1, 0);

CREATE TABLE IF NOT EXISTS t_user_credit_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    change_before INTEGER,
    change_after INTEGER,
    change_credit INTEGER,
    change_type TEXT,
    change_reason TEXT,
    act_user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS t_product (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stripe_product_id TEXT UNIQUE,
    stripe_price_id TEXT UNIQUE,
    is_active INTEGER NOT NULL DEFAULT 1,
    credits INTEGER NOT NULL DEFAULT 0,
    price REAL NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'CNY',
    description TEXT,
    name TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS t_task (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE,
    task_type TEXT,
    status TEXT,
    sub_status TEXT,
    country_code TEXT,
    language TEXT,
    percent INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    error TEXT,
    result TEXT,
    media_url TEXT,
    proxy_media_url TEXT,
    object_key TEXT,
    model TEXT,
    extra TEXT
);

CREATE TABLE IF NOT EXISTS t_stripe_checkout (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    status TEXT NOT NULL,
    payment_status TEXT NOT NULL,
    amount int NOT NULL,
    payment_intent TEXT NOT NULL DEFAULT '', -- COMMENT 'stripe paymentIntent Id',
    currency TEXT NOT NULL, --COMMENT '币种',
    expires_at int NOT NULL, --COMMENT '过期时间',
    payment_method_types TEXT NOT NULL, --COMMENT '支付方式',
    payment_method_details TEXT, --COMMENT '实际支付方式详情',
    session_data TEXT NOT NULL, --COMMENT 'session完整数据',
    meta_data TEXT NOT NULL, --COMMENT 'meta_data',
    ip_address TEXT NOT NULL, --COMMENT '客户端IP地址',
    remark TEXT,
    user_id INTEGER NOT NULL DEFAULT '0', --COMMENT '用户ID',
    is_handled tinyint(1) NOT NULL DEFAULT '0', --COMMENT '是否成功处理事件',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
--  添加索引
CREATE INDEX IF NOT EXISTS idx_pi_id ON t_stripe_checkout (payment_intent);
CREATE INDEX IF NOT EXISTS idx_uid ON t_stripe_checkout (user_id);