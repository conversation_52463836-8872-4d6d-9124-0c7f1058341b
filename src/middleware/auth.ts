import { Context, Next } from 'hono';
import { createErrorResponse, ResponseCode } from '../utils/response';
import { AuthService } from '../services/auth';

// Define user type
export interface User {
  id: number;
  username: string;
  role: string;
  api_key: string;
  credits: number;
}

// JWT authentication middleware
export const authMiddleware = async (c: Context, next: Next) => {
  const authHeader = c.req.header('Authorization');
  const apiKey = c.req.header('x-api-key');
  const hasBearerToken = authHeader?.startsWith('Bearer ');
  const hasApiKey = !!apiKey;
  if (!hasBearerToken && !hasApiKey) {
    return c.json(
      createErrorResponse('Unauthorized', ResponseCode.UNAUTHORIZED),
      ResponseCode.UNAUTHORIZED
    );
  }

  const authService = new AuthService(c);
  if (apiKey) {
    //apikey 查询用户
    const user = await authService.getUserByApiKey(apiKey);
    if (user) {
      const userCredit = await authService.getUserCredit(user.id);
      c.set('user', {
        id: user.id,
        username: user.username,
        role: user.role,
        api_key: user.api_key,
        credits: userCredit,
      });
      await next();
    } else {
      return c.json(
        createErrorResponse('Unauthorized', ResponseCode.UNAUTHORIZED),
        ResponseCode.UNAUTHORIZED
      );
    }
  }
  if (hasBearerToken) {
    const token = authHeader?.split(' ')[1] ?? '';
    try {
      const user = await authService.verifyToken(token);
      c.set('user', user);
      await next();
    } catch (error) {
      return c.json(
        createErrorResponse('Invalid token', ResponseCode.UNAUTHORIZED),
        ResponseCode.UNAUTHORIZED
      );
    }
  }
};
