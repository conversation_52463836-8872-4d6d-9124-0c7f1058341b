import { MiddlewareHandler } from 'hono';
import { logger } from 'hono/logger';
import { cors } from 'hono/cors';

export const loggerMiddleware = logger();

export const corsMiddleware = cors({
  origin: '*',
  allowHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  maxAge: 86400,
});

// Optimized OPTIONS handler for direct response
export const optionsHandler = (c: any) => {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key',
      'Access-Control-Max-Age': '86400',
    },
  });
};

export const errorHandler: MiddlewareHandler = async (c, next) => {
  try {
    await next();
  } catch (err) {
    console.error('Error:', err);
    return c.json(
      {
        message: 'Internal Server Error',
        error: err instanceof Error ? err.message : String(err),
      },
      500
    );
  }
}; 