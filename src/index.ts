/**
 * Welcome to Cloudflare Workers! This is your first worker.
 *
 * - Run `npm run dev` in your terminal to start a development server
 * - Open a browser tab at http://localhost:8787/ to see your worker in action
 * - Run `npm run deploy` to publish your worker
 *
 * Bind resources to your worker in `wrangler.jsonc`. After adding bindings, a type definition for the
 * `Env` object can be regenerated with `npm run cf-typegen`.
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import api from './routes/api';
import { QueueMessage, QueueService } from './services/queue';

// Export Durable Objects
export { UserLock } from './durable-objects/UserLock';

// Define your environment bindings
interface Env {
	ASSETS: { fetch: typeof fetch };
	QUEUE: Queue;
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	datatool: R2Bucket;
	USER_LOCK: DurableObjectNamespace;
}

const app = new Hono<{ Bindings: Env }>();

// Optimize OPTIONS requests - handle them directly without processing through middleware chain
app.options('*', (c) => {
	return new Response(null, {
		status: 204,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key',
			'Access-Control-Max-Age': '86400', // 24 hours
		},
	});
});

// Apply middleware
app.use('*', cors({
	origin: '*',
	allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
	allowHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
}));
app.use('*', async (c, next) => {
	await next();
	if (!c.res.headers.get('X-API-Version')) {
		c.res.headers.set('X-API-Version', '1.0');
	}
});

// Mount API routes
app.route('/api', api);

// Handle SPA routing - serve index.html for all non-API routes
app.get('*', async (c) => {
	const url = new URL(c.req.url);
	
	try {
		// First try to serve the exact asset
		const response = await c.env.ASSETS.fetch(url);
		
		// If the response is not found and it's not requesting an asset file
		// (like CSS, JS, images, etc.), serve index.html for client-side routing
		if (response.status === 404 && !url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|json)$/)) {
			return c.env.ASSETS.fetch(new URL('/index.html', url.origin));
		}
		
		return response;
	} catch (error) {
		// If there's an error, serve index.html for client-side routing
		if (!url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|json)$/)) {
			return c.env.ASSETS.fetch(new URL('/index.html', url.origin));
		}
		return c.notFound();
	}
});

// 404 handler
app.notFound((c) => {
	return c.json({ message: 'Not Found' }, 404);
});

// 队列消费者处理逻辑
export default {
	fetch: app.fetch,
	async queue(batch: MessageBatch<QueueMessage>, env: Env): Promise<void> {
		const queueService = new QueueService(env);
		
		// 处理批量消息 - 并行处理所有消息
		const promises = batch.messages.map(async (message) => {
			try {
				console.log(`开始处理消息: ${message.id}`);
				await queueService.processMessage(message.body);
				console.log(`消息处理成功: ${message.id}`);
			} catch (error) {
				console.error(`消息处理失败: ${message.id}`, error);
				// 记录错误但不抛出，确保其他消息处理不受影响
				// 如果需要重试特定消息，可以在这里添加重试逻辑
			}
		});
		
		// 并行等待所有消息处理完成，任何一个消息的失败不会影响其他消息的处理
		await Promise.all(promises);
	}
};
