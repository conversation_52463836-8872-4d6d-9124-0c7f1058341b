import { Context, MiddlewareHandler } from 'hono';
import { sendResponse, sendErrorResponse, ResponseCode, InsufficientCreditsError } from './response';
import { ZodError, z } from 'zod';

type HandlerFunction<T = any> = (c: Context) => Promise<T> | T;
type ValidatedContext<T> = Context & { validatedData: T };

export class ApiResponseHandler {
  static wrapHandler<T>(
    handler: HandlerFunction<T>,
    options: {
      successMessage?: string;
      errorMessage?: string;
      successCode?: typeof ResponseCode[keyof typeof ResponseCode];
      errorCode?: typeof ResponseCode[keyof typeof ResponseCode];
    } = {}
  ): MiddlewareHandler {
    return async (c: Context) => {
      try {
        const result = await handler(c);
        return sendResponse(
          c,
          result,
          options.successMessage,
          options.successCode
        );
      } catch (error: unknown) {
        // Handle custom InsufficientCreditsError
        if (error instanceof InsufficientCreditsError) {
          return sendErrorResponse(
            c,
            error.message,
            error.code as typeof ResponseCode[keyof typeof ResponseCode],
            error
          );
        }

        return sendErrorResponse(
          c,
          options.errorMessage || (error instanceof Error ? error.message : 'Internal Server Error'),
          options.errorCode,
          error
        );
      }
    };
  }

  static wrapWithZod<T extends z.ZodType>(
    schema: T,
    handler: (c: ValidatedContext<z.infer<T>>) => Promise<any>,
    options: {
      successMessage?: string;
      errorMessage?: string;
      successCode?: typeof ResponseCode[keyof typeof ResponseCode];
      errorCode?: typeof ResponseCode[keyof typeof ResponseCode];
    } = {}
  ): MiddlewareHandler {
    return async (c: Context) => {
      try {
        const body = await c.req.json().catch(() => ({})); // 支持空 body
        const params = c.req.param(); // 获取 path param
        const query = c.req.query(); // 获取 ?xxx=xxx 的值
        const validatedData = await schema.parseAsync({ ...body, ...params, ...query });
        const result = await handler({ ...c, validatedData } as ValidatedContext<z.infer<T>>);
        return sendResponse(
          c,
          result,
          options.successMessage,
          options.successCode
        );
      } catch (error: unknown) {
        if (error instanceof ZodError) {
          return sendErrorResponse(
            c,
            error.errors[0].message,
            ResponseCode.BAD_REQUEST,
            { issues: error.errors }
          );
        }

        // Handle custom InsufficientCreditsError
        if (error instanceof InsufficientCreditsError) {
          return sendErrorResponse(
            c,
            error.message,
            error.code as typeof ResponseCode[keyof typeof ResponseCode],
            error
          );
        }

        return sendErrorResponse(
          c,
          options.errorMessage || (error instanceof Error ? error.message : 'Internal Server Error'),
          options.errorCode,
          error
        );
      }
    };
  }
} 