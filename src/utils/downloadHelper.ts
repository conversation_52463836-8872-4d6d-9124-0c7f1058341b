import { Context } from 'hono';
import { ApiResponseHandler } from './apiHandler';

// 媒体代理处理类
export class DownloadHelper {
	// 媒体类型映射
	private static MEDIA_TYPES = {
		'.mp4': 'video/mp4',
		'.m4s': 'video/mp4',
		'.m4v': 'video/mp4',
		'.webm': 'video/webm',
		'.jpg': 'image/jpeg',
		'.jpeg': 'image/jpeg',
		'.png': 'image/png',
		'.gif': 'image/gif',
		'.webp': 'image/webp',
	};

	// 密钥
	private static SECRET_KEY = '6m9VM01vDwl9yz7L';

	// 生成签名
	static async generateSignature(url: string, expire: number): Promise<string> {
		const message = `${url}&&${expire}`;
		const encoder = new TextEncoder();
		const key = await crypto.subtle.importKey('raw', encoder.encode(this.SECRET_KEY), { name: 'HM<PERSON>', hash: 'SHA-256' }, false, ['sign']);
		const signatureBuffer = await crypto.subtle.sign('HMAC', key, encoder.encode(message));
		const signatureArray = Array.from(new Uint8Array(signatureBuffer));
		return encodeURIComponent(signatureArray.map((b) => b.toString(16).padStart(2, '0')).join(''));
	}

	// 构建代理URL
	static async buildProxyUrl(
		url: string,
		expireAdd: number = 60 * 60,
		platform: string = '',
		ck: string = '',
		filename: string = ''
	): Promise<string> {
		const expire = Math.floor(Date.now() / 1000) + expireAdd;
		const signature = await this.generateSignature(url, expire);

		const baseUrl = 'https://data.snappdown.com/api/download/proxy?';
		const params = new URLSearchParams();
		params.append('url', url);
		params.append('signature', signature);
		params.append('expire', expire.toString());
		if (platform) {
			params.append('platform', platform);
		}
		if (ck) {
			params.append('ck', ck);
		}
		if (filename) {
			params.append('filename', filename);
		}

		return baseUrl + params.toString();
	}

	static async buildM3u8Url(url: string, expireAdd: number = 60 * 60): Promise<string> {
		const expire = Math.floor(Date.now() / 1000) + expireAdd;
		const signature = await this.generateSignature(url, expire);

		const baseUrl = 'https://snappdown.com/api/download/m3u8?';
		const params = new URLSearchParams();
		params.append('url', url);
		params.append('signature', signature);
		params.append('expire', expire.toString());

		return baseUrl + params.toString();
	}

	// 验证签名
	static async verifySignature(url: string, expire: number, signature: string) {
		// 验证签名是否过期
		// const now = Math.floor(Date.now() / 1000);
		// if (now > expire) {
		//   throw new Error("Expired signature");
		// }

		// 生成签名
		const message = `${url}&&${expire}`;
		const encoder = new TextEncoder();
		const key = await crypto.subtle.importKey('raw', encoder.encode(this.SECRET_KEY), { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
		const signatureBuffer = await crypto.subtle.sign('HMAC', key, encoder.encode(message));
		const signatureArray = Array.from(new Uint8Array(signatureBuffer));
		const genSignature = encodeURIComponent(signatureArray.map((b) => b.toString(16).padStart(2, '0')).join(''));

		// 验证签名
		if (genSignature !== signature) {
			throw new Error('Invalid signature');
		}
	}

	// 获取内容类型
	static getContentType(urlPath: string, defaultType: string = 'application/octet-stream'): string {
		const extension = urlPath.substring(urlPath.lastIndexOf('.')).toLowerCase();
		//@ts-ignore
		return this.MEDIA_TYPES[extension] || defaultType;
	}

	// 获取域名特定的请求头
	static getDomainSpecificHeaders(domain: string, platform?: string): Record<string, string> {
		if (domain.includes('qpic.cn')) {
			return {
				'Accept-Encoding': 'gzip, deflate, br, zstd',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				accept: '*/*',
				'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
				'cache-control': 'no-cache',
				pragma: 'no-cache',
				'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
				'sec-ch-ua-mobile': '?0',
				origin: 'https://mp.weixin.qq.com',
				'sec-ch-ua-platform': '"Windows"',
				referrer: 'https://mp.weixin.qq.com/',
				'sec-fetch-dest': 'video',
				'sec-fetch-mode': 'cors',
				'sec-fetch-site': 'cross-site',
			};
		}

		// 微博CDN域名的特殊处理
		if (domain.includes('weibocdn.com') || domain.includes('sinaimg.cn')) {
			return {
				accept: '*/*',
				'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
				'cache-control': 'no-cache',
				origin: 'https://weibo.com',
				pragma: 'no-cache',
				priority: 'u=1, i',
				referer: 'https://weibo.com/',
				'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
				'sec-ch-ua-mobile': '?0',
				'sec-ch-ua-platform': '"Windows"',
				'sec-fetch-dest': 'empty',
				'sec-fetch-mode': 'cors',
				'sec-fetch-site': 'cross-site',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
			};
		}

		// 新片场CDN域名的特殊处理
		if (domain.includes('xpccdn.com')) {
			return {
				accept: '*/*',
				'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
				'cache-control': 'no-cache',
				origin: 'https://www.xinpianchang.com',
				pragma: 'no-cache',
				priority: 'i',
				range: 'bytes=0-',
				referer: 'https://www.xinpianchang.com/',
				'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
				'sec-ch-ua-mobile': '?0',
				'sec-ch-ua-platform': '"Windows"',
				'sec-fetch-dest': 'video',
				'sec-fetch-mode': 'cors',
				'sec-fetch-site': 'cross-site',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
			};
		}

		// AcFun域名的特殊处理
		if (domain.includes('acfun.cn')) {
			return {
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36',
				Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
				'Accept-Language': 'en-us,en;q=0.5',
				'Sec-Fetch-Mode': 'navigate',
				Referer: 'https://www.acfun.cn/',
			};
		}

		// B站视频域名的特殊处理
		if (domain.includes('bilivideo') || platform == 'bilibili') {
			return {
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.19 Safari/537.36',
				Accept: 'text/html,application/xhtml+xml,application/xml;q\u003d0.9,*/*;q\u003d0.8',
				'Accept-Language': 'en-us,en;q\u003d0.5',
				'Sec-Fetch-Mode': 'navigate',
				Referer: 'https://www.bilibili.com',
			};
		}

		// 西瓜视频域名的特殊处理
		if (domain.includes('ixigua')) {
			return {
				'Accept-Encoding': 'gzip, deflate, br, zstd',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				referer: 'https://m.ixigua.com/',
			};
		}
		if (domain.includes('cntv')) {
			return {
				'Accept-Encoding': 'gzip, deflate, br, zstd',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				origin: 'https://tv.cctv.com',
				referer: 'https://tv.cctv.com/',
			};
		}

		// B站静态资源域名的特殊处理
		if (domain.includes('hdslb.com')) {
			return {
				'Accept-Encoding': 'gzip, deflate, br, zstd',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				referer: 'https://cool.bilibili.com/',
			};
		}

		// 抖音视频域名的特殊处理
		if (domain.includes('douyinvod.com')) {
			return {
				'Accept-Encoding': 'gzip, deflate, br, zstd',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				referer: 'https://www.douyin.com/',
			};
		}
		if (domain.includes('tiktok.com')) {
			return {
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				referer: 'https://www.tiktok.com/',
			};
		}
		// tandou静态资源域名的特殊处理
		if (domain.includes('tangdou.com')) {
			return {
				'Accept-Encoding': 'gzip, deflate, br, zstd',
				'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
				referer: 'https://m.tangdou.com/',
			};
		}
		if (platform == 'newsSina') {
			return {
				accept: '*/*',
				'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
				'cache-control': 'no-cache',
				pragma: 'no-cache',
				priority: 'i',
				referer: 'https://news.sina.com.cn',
				'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
				'sec-ch-ua-mobile': '?0',
				'sec-ch-ua-platform': '"Windows"',
				'sec-fetch-dest': 'video',
				'sec-fetch-mode': 'no-cors',
				'sec-fetch-site': 'cross-site',
				'sec-fetch-storage-access': 'active',
				'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
			};
		}

		return {
			'Accept-Encoding': 'gzip, deflate, br, zstd',
			'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
		};
	}

	// 获取缓存控制头
	static getCacheControlHeader(contentType: string): string {
		if (contentType.startsWith('video/')) {
			return 'public, max-age=86400'; // 视频缓存1天
		} else if (contentType.startsWith('image/')) {
			return 'public, max-age=604800'; // 图片缓存7天
		}
		return 'public, max-age=3600'; // 默认缓存1小时
	}

	// 处理请求
	static async handleRequest(c: Context) {
		const url = decodeURIComponent(c.req.query('url') || '');
		const signature = decodeURIComponent(c.req.query('signature') || '');
		const expire = decodeURIComponent(c.req.query('expire') || '');
		const platform = decodeURIComponent(c.req.query('platform') || '');
		const filename = decodeURIComponent(c.req.query('filename') || '');
		try {
			const ck = decodeURIComponent(c.req.query('ck') || '');

			// 参数验证
			if (!url) throw new Error('missing_url');
			if (!signature) throw new Error('missing_signature');
			if (!expire) throw new Error('missing_expire');

			// 签名验证  特殊处理
			try {
				await this.verifySignature(c.req.query('url') || '', parseInt(expire), signature);
			} catch (error) {
				await this.verifySignature(url, parseInt(expire), signature);
			}

			// 解析URL和域名
			const parsedUrl = new URL(url);
			const domain = parsedUrl.hostname;

			// 基础请求头
			const baseHeaders = this.getDomainSpecificHeaders(domain, platform);
			if (ck) {
				try {
					// 解码base64并解析JSON
					const cookiesData = JSON.parse(atob(ck));
					// 构建cookie字符串
					const cookieString = Object.entries(cookiesData)
						.map(([key, value]) => `${key}=${value}`)
						.join('; ');
					if (cookieString) {
						//@ts-ignore
						baseHeaders['Cookie'] = cookieString;
					}
				} catch (error) {
					console.error('Failed to parse cookies:', error);
				}
			}
			// 检查是否有Range头，如果有则传递
			const rangeHeader = c.req.header('Range');
			if (rangeHeader) {
				//@ts-ignore
				baseHeaders['Range'] = rangeHeader;
			}
			// 域名特定请求头
			// 发送请求
			const response = await fetchWithProxy(url, {
				method: 'GET',
				headers: {
					...baseHeaders,
				},
			});

			if (!response.ok) {
				throw new Error(`Failed to fetch media: ${response.statusText}`);
			}

			// 确定内容类型
			const contentType = this.getContentType(parsedUrl.pathname, response.headers.get('Content-Type') || 'application/octet-stream');

			// 设置缓存策略
			const cacheControl = this.getCacheControlHeader(contentType);

			// 生成文件名
			let downloadFilename = filename;
			if (!downloadFilename) {
				// 从URL中提取文件名
				const urlPath = parsedUrl.pathname;
				downloadFilename = urlPath.split('/').pop() || 'download';
				
				// 如果没有扩展名，根据content-type添加
				if (!downloadFilename.includes('.')) {
					if (contentType.includes('video/mp4')) {
						downloadFilename += '.mp4';
					} else if (contentType.includes('audio/mp4') || contentType.includes('audio/m4a')) {
						downloadFilename += '.m4a';
					} else if (contentType.includes('audio/')) {
						downloadFilename += '.mp3';
					} else if (contentType.includes('image/jpeg')) {
						downloadFilename += '.jpg';
					} else if (contentType.includes('image/png')) {
						downloadFilename += '.png';
					} else if (contentType.includes('image/webp')) {
						downloadFilename += '.webp';
					}
				}
			}

			// 准备响应头
			const responseHeaders = new Headers({
				'Content-Type': 'application/octet-stream', // 强制下载
				'Content-Disposition': `attachment; filename="${encodeURIComponent(downloadFilename)}"`, // 强制下载并设置文件名
				'Cache-Control': 'no-cache', // 禁用缓存以确保下载
				'Accept-Ranges': 'bytes', // 支持范围请求，允许快进
			});
			// 复制原始响应的内容长度和范围信息
			const contentLength = response.headers.get('content-length');
			if (contentLength) {
				responseHeaders.set('Content-Length', contentLength);
			}

			// 如果是部分内容响应(206)，复制Content-Range头
			if (response.status === 206) {
				const contentRange = response.headers.get('content-range');
				if (contentRange) {
					responseHeaders.set('Content-Range', contentRange);
				}
			}

			// 添加CORS头，允许跨域访问
			responseHeaders.set('Access-Control-Allow-Origin', '*');
			responseHeaders.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
			responseHeaders.set('Access-Control-Allow-Headers', 'Range');
			responseHeaders.set('Access-Control-Expose-Headers', 'Content-Length, Content-Range');

			// 返回响应，保持原始状态码
			return new Response(response.body, {
				status: response.status,
				headers: responseHeaders,
			});
		} catch (error) {
			console.error('Media proxy error:', error);

			return c.json(
				{
					code: 500,
					data: null,
					success: false,
					message: (error as Error).message,
					timestamp: Date.now(),
				},
				500
			);
		}
	}

	static async handleM3u8Request(c: any) {
		let url = decodeURIComponent(c.req.query('url') || '');
		const signature = decodeURIComponent(c.req.query('signature') || '');
		const expire = decodeURIComponent(c.req.query('expire') || '');

		try {
			// 参数验证
			if (!url) throw new Error('missing_url');
			if (!signature) throw new Error('missing_signature');
			if (!expire) throw new Error('missing_expire');

			// 签名验证
			try {
				await this.verifySignature(c.req.query('url'), parseInt(expire), signature);
			} catch (error) {
				await this.verifySignature(url, parseInt(expire), signature);
			}

			// 解析URL和域名
			const parsedUrl = new URL(url);
			const domain = parsedUrl.hostname;

			// 确定是否需要特殊代理
			let useProxy = true;
			if (domain.includes('netease.com')) {
				useProxy = true;
			}

			// 基础请求头
			const headers = this.getDomainSpecificHeaders(domain);
			const response = await fetchWithProxy(url, {
				method: 'GET',
				headers,
			});
			if (!response.ok) {
				throw new Error(`Failed to fetch m3u8: ${response.statusText}`);
			}

			// 获取m3u8文本内容
			const m3u8Content = await response.text();

			// 解析m3u8内容
			const lines = m3u8Content.split('\n');
			const tsUrls: string[] = [];
			const proxyTsUrls: string[] = [];

			// 处理每一行，找出TS文件URL
			for (let i = 0; i < lines.length; i++) {
				const line = lines[i].trim();

				// 跳过注释和空行
				if (line.startsWith('#') || line === '') continue;

				// 处理TS文件URL
				let fullTsUrl = line;

				// 如果是相对路径，转换为绝对路径
				if (!line.startsWith('http://') && !line.startsWith('https://')) {
					// 获取m3u8的基础URL
					const baseUrl = url.substring(0, url.lastIndexOf('/') + 1);
					fullTsUrl = new URL(line, baseUrl).href;
				}

				tsUrls.push(fullTsUrl);

				// 为每个TS文件创建代理URL
				const proxyUrl = await this.buildProxyUrl(fullTsUrl, 60 * 60);
				proxyTsUrls.push(proxyUrl);
			}
			// 返回结果
			return {
				playlist: m3u8Content,
				proxy_ts_urls: proxyTsUrls,
				ts_urls: tsUrls,
			};
		} catch (error) {
			console.error('M3U8 proxy error:', error);

			return c.json(
				{
					code: 500,
					data: null,
					success: false,
					//@ts-ignore
					message: getErrorMessage(error.message, lang),
					timestamp: Date.now(),
				},
				200
			);
		}
	}
}

export async function fetchWithProxy(url: string, options?: RequestInit) {
	try {
		const response = await fetch(url, options);
		// 验证媒体类型
		return response;
	} catch (error) {
		//@ts-ignore
		throw new Error(`Proxy request failed: ${error.message}`);
	}
}
