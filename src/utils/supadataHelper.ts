/**
 * Supadata AI YouTube API 封装
 * 文档: https://supadata.ai/documentation/youtube/video
 */

// API 配置
const SUPADATA_BASE_URL = 'https://api.supadata.ai/v1';

// 通用请求配置
interface SupadataRequestOptions {
  apiKey: string;
  timeout?: number;
}

// 通用响应接口
interface SupadataResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// YouTube Video 相关接口
interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  duration: number;
  channel: {
    id: string;
    name: string;
  };
  tags: string[];
  thumbnail: string;
  uploadDate: string;
  viewCount: number;
  likeCount: number;
  transcriptLanguages: string[];
}

// YouTube Channel 接口
interface YouTubeChannel {
  id: string;
  name: string;
  description: string;
  subscriberCount: number;
  videoCount: number;
  thumbnail: string;
  banner?: string;
}

// YouTube Playlist 接口
interface YouTubePlaylist {
  id: string;
  title: string;
  description: string;
  videoCount: number;
  viewCount: number;
  lastUpdated: string;
  channel: {
    id: string;
    name: string;
  };
}

// Transcript 接口
interface YouTubeTranscriptSegment {
  text: string;
  offset: number;
  duration: number;
  lang: string;
}

interface YouTubeTranscriptResponse {
  content: string | YouTubeTranscriptSegment[];
  lang: string;
  availableLangs: string[];
}

// Translation 接口
interface YouTubeTranslationResponse {
  content: string | YouTubeTranscriptSegment[];
  lang: string;
}

// Channel Videos 响应接口
interface YouTubeChannelVideosResponse {
  videoIds: string[];
  shortIds: string[];
  liveIds: string[];
}

// Playlist Videos 响应接口
interface YouTubePlaylistVideosResponse {
  videoIds: string[];
  shortIds: string[];
  liveIds: string[];
}

// Batch 请求接口
interface BatchTranscriptRequest {
  videoIds?: string[];
  playlistId?: string;
  channelId?: string;
  limit?: number;
  lang?: string;
  text?: boolean;
}

interface BatchVideoRequest {
  videoIds?: string[];
  playlistId?: string;
  channelId?: string;
  limit?: number;
}

interface BatchJobResponse {
  jobId: string;
}

interface BatchResultResponse {
  status: 'queued' | 'active' | 'completed' | 'failed';
  results?: Array<{
    videoId: string;
    transcript?: YouTubeTranscriptResponse;
    video?: YouTubeVideo;
    errorCode?: string;
  }>;
  stats?: {
    total: number;
    succeeded: number;
    failed: number;
  };
  completedAt?: string;
}

/**
 * 通用 API 请求函数
 */
async function makeSupadataRequest<T>(
  endpoint: string,
  options: SupadataRequestOptions,
  params: Record<string, any> = {}
): Promise<SupadataResponse<T>> {
  try {
    const url = new URL(`${SUPADATA_BASE_URL}${endpoint}`);
    
    // 添加查询参数
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'x-api-key': options.apiKey,
        'Accept': 'application/json',
        'User-Agent': 'DataMiner/1.0'
      },
      signal: options.timeout ? AbortSignal.timeout(options.timeout) : undefined
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as T;
    return { success: true, data };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`Supadata API request failed for ${endpoint}:`, errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Supadata AI YouTube API 封装类
 */
export class SupadataYouTubeAPI {
  private apiKey: string;
  private timeout: number;

  constructor(timeout: number = 30000) {
    this.apiKey = "sd_c9e88aa361a84110508a75a07f1f3f8b";
    this.timeout = timeout;
  }

  private getRequestOptions(): SupadataRequestOptions {
    return {
      apiKey: this.apiKey,
      timeout: this.timeout
    };
  }

  /**
   * 获取 YouTube 视频元数据
   * @param videoId YouTube 视频 ID 或 URL
   * @returns Promise<SupadataResponse<YouTubeVideo>>
   */
  async getVideo(videoId: string): Promise<SupadataResponse<YouTubeVideo>> {
    return makeSupadataRequest<YouTubeVideo>(
      '/youtube/video',
      this.getRequestOptions(),
      { id: videoId }
    );
  }

  /**
   * 获取 YouTube 视频转录
   * @param videoId YouTube 视频 ID 或 URL
   * @param lang 转录语言代码 (可选)
   * @param text 是否返回纯文本格式 (可选)
   * @param chunkSize 转录片段最大字符数 (可选)
   * @returns Promise<SupadataResponse<YouTubeTranscriptResponse>>
   */
  async getTranscript(
    videoId: string,
    lang?: string,
    text?: boolean,
    chunkSize?: number
  ): Promise<SupadataResponse<YouTubeTranscriptResponse>> {
    const params: Record<string, any> = { url: videoId };
    if (lang) params.lang = lang;
    if (text !== undefined) params.text = text;
    if (chunkSize) params.chunkSize = chunkSize;
    
    return makeSupadataRequest<YouTubeTranscriptResponse>(
      '/youtube/transcript',
      this.getRequestOptions(),
      params
    );
  }

  /**
   * 获取 YouTube 视频翻译
   * @param videoId YouTube 视频 ID 或 URL
   * @param lang 目标语言代码
   * @param text 是否返回纯文本格式 (可选)
   * @param chunkSize 转录片段最大字符数 (可选)
   * @returns Promise<SupadataResponse<YouTubeTranslationResponse>>
   */
  async getTranslation(
    videoId: string,
    lang: string,
    text?: boolean,
    chunkSize?: number
  ): Promise<SupadataResponse<YouTubeTranslationResponse>> {
    const params: Record<string, any> = {
      url: videoId,
      lang: lang
    };
    if (text !== undefined) params.text = text;
    if (chunkSize) params.chunkSize = chunkSize;
    
    return makeSupadataRequest<YouTubeTranslationResponse>(
      '/youtube/transcript/translation',
      this.getRequestOptions(),
      params
    );
  }

  /**
   * 获取 YouTube 频道信息
   * @param channelId YouTube 频道 ID 或 URL
   * @returns Promise<SupadataResponse<YouTubeChannel>>
   */
  async getChannel(channelId: string): Promise<SupadataResponse<YouTubeChannel>> {
    return makeSupadataRequest<YouTubeChannel>(
      '/youtube/channel',
      this.getRequestOptions(),
      { id: channelId }
    );
  }

  /**
   * 获取 YouTube 播放列表信息
   * @param playlistId YouTube 播放列表 ID 或 URL
   * @returns Promise<SupadataResponse<YouTubePlaylist>>
   */
  async getPlaylist(playlistId: string): Promise<SupadataResponse<YouTubePlaylist>> {
    return makeSupadataRequest<YouTubePlaylist>(
      '/youtube/playlist',
      this.getRequestOptions(),
      { id: playlistId }
    );
  }

  /**
   * 获取 YouTube 频道的视频列表
   * @param channelId YouTube 频道 ID 或 URL
   * @param limit 返回视频数量限制 (可选)
   * @param type 视频类型 (可选): all, video, short, live
   * @returns Promise<SupadataResponse<YouTubeChannelVideosResponse>>
   */
  async getChannelVideos(
    channelId: string,
    limit?: number,
    type?: 'all' | 'video' | 'short' | 'live'
  ): Promise<SupadataResponse<YouTubeChannelVideosResponse>> {
    const params: Record<string, any> = { id: channelId };
    if (limit) params.limit = limit;
    if (type) params.type = type;
    
    return makeSupadataRequest<YouTubeChannelVideosResponse>(
      '/youtube/channel/videos',
      this.getRequestOptions(),
      params
    );
  }

  /**
   * 获取 YouTube 播放列表的视频列表
   * @param playlistId YouTube 播放列表 ID 或 URL
   * @param limit 返回视频数量限制 (可选)
   * @returns Promise<SupadataResponse<YouTubePlaylistVideosResponse>>
   */
  async getPlaylistVideos(
    playlistId: string,
    limit?: number
  ): Promise<SupadataResponse<YouTubePlaylistVideosResponse>> {
    const params: Record<string, any> = { id: playlistId };
    if (limit) params.limit = limit;
    
    return makeSupadataRequest<YouTubePlaylistVideosResponse>(
      '/youtube/playlist/videos',
      this.getRequestOptions(),
      params
    );
  }

  /**
   * 批量获取视频转录
   * @param request 批量转录请求
   * @returns Promise<SupadataResponse<BatchJobResponse>>
   */
  async batchTranscript(request: BatchTranscriptRequest): Promise<SupadataResponse<BatchJobResponse>> {
    try {
      const response = await fetch(`${SUPADATA_BASE_URL}/youtube/transcript/batch`, {
        method: 'POST',
        headers: {
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'DataMiner/1.0'
        },
        body: JSON.stringify(request),
        signal: this.timeout ? AbortSignal.timeout(this.timeout) : undefined
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json() as BatchJobResponse;
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Supadata batch transcript request failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 批量获取视频元数据
   * @param request 批量视频请求
   * @returns Promise<SupadataResponse<BatchJobResponse>>
   */
  async batchVideo(request: BatchVideoRequest): Promise<SupadataResponse<BatchJobResponse>> {
    try {
      const response = await fetch(`${SUPADATA_BASE_URL}/youtube/video/batch`, {
        method: 'POST',
        headers: {
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'DataMiner/1.0'
        },
        body: JSON.stringify(request),
        signal: this.timeout ? AbortSignal.timeout(this.timeout) : undefined
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json() as BatchJobResponse;
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Supadata batch video request failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取批量任务结果
   * @param jobId 任务ID
   * @returns Promise<SupadataResponse<BatchResultResponse>>
   */
  async getBatchResult(jobId: string): Promise<SupadataResponse<BatchResultResponse>> {
    return makeSupadataRequest<BatchResultResponse>(
      `/youtube/batch/${jobId}`,
      this.getRequestOptions()
    );
  }
}

/**
 * 创建 Supadata YouTube API 实例的便捷函数
 * @param timeout 请求超时时间（毫秒）
 * @returns SupadataYouTubeAPI 实例
 */
export function createSupadataAPI(timeout?: number): SupadataYouTubeAPI {
  return new SupadataYouTubeAPI(timeout);
}

/**
 * 默认 API 实例（需要在环境变量中设置 SUPADATA_API_KEY）
 */
export const defaultSupadataAPI = (() => {
  return new SupadataYouTubeAPI();
})();

// 导出类型定义
export type {
  YouTubeVideo,
  YouTubeChannel,
  YouTubePlaylist,
  YouTubeTranscriptSegment,
  YouTubeTranscriptResponse,
  YouTubeTranslationResponse,
  YouTubeChannelVideosResponse,
  YouTubePlaylistVideosResponse,
  BatchTranscriptRequest,
  BatchVideoRequest,
  BatchJobResponse,
  BatchResultResponse,
  SupadataResponse,
  SupadataRequestOptions
};