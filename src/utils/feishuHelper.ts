import { Context } from "hono";

/**
 * 飞书消息助手
 * 用于向飞书机器人发送消息
 */
export class FeishuHelper {
  // 飞书机器人API地址
  private static ROBOT_API =
    "https://open.feishu.cn/open-apis/bot/v2/hook/416c56e4-99e2-4ab5-8203-1723d657a4d5";
  //飞书错误消息的地址
  private static ROBOT_ERROR_API =  "https://open.feishu.cn/open-apis/bot/v2/hook/fce1499d-9176-444b-8093-3319bc273204";

  /**
   * 创建飞书消息卡片数据
   * @param c Context对象
   * @param msg 消息内容
   * @param title 消息标题
   * @param isError 是否为错误消息
   * @returns 飞书消息卡片数据
   */
  private static createCardData(
    c: Context,
    msg: string,
    title: string,
    isError: boolean = false
  ) {
    const env = c.env?.ENV || 'unknown';
    const titlePrefix = isError ? '🚨【错误】' : '📢【通知】';

    return {
      msg_type: "interactive",
      card: {
        config: {
          wide_screen_mode: true,
        },
        header: {
          title: {
            tag: "plain_text",
            content: `${titlePrefix}【${env}】${title}`,
          },
          template: isError ? "red" : "blue", // 错误消息使用红色主题
        },
        elements: [
          {
            tag: "div",
            text: {
              tag: "lark_md",
              content: msg,
            },
          },
          ...(isError ? [{
            tag: "hr"
          }, {
            tag: "div",
            text: {
              tag: "plain_text",
              content: `时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`,
            },
          }] : [])
        ],
      },
    };
  }

  /**
   * 发送消息到飞书（通用方法）
   * @param apiUrl 飞书机器人API地址
   * @param c Context对象
   * @param msg 消息内容
   * @param title 消息标题
   * @param isError 是否为错误消息
   * @returns Promise<Response>
   */
  private static async sendMessage(
    apiUrl: string,
    c: Context,
    msg: string,
    title: string,
    isError: boolean = false
  ): Promise<Response> {
    if (!msg) {
      throw new Error("msg cannot be empty");
    }

    const data = this.createCardData(c, msg, title, isError);

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        console.error(`Failed to send Feishu message: ${response.status} ${response.statusText}`);
      }

      return response;
    } catch (error) {
      console.error('Error sending Feishu message:', error);
      throw error;
    }
  }

  /**
   * 发送普通消息到飞书
   * @param c Context对象
   * @param msg 消息内容
   * @param title 消息标题
   * @returns Promise<Response>
   */
  static async sendMsg(
    c: Context,
    msg: string,
    title: string = "系统通知"
  ): Promise<Response> {
    return this.sendMessage(this.ROBOT_API, c, msg, title, false);
  }

  /**
   * 发送错误消息到飞书
   * @param c Context对象
   * @param error 错误对象或错误消息
   * @param title 消息标题
   * @param additionalInfo 额外信息
   * @returns Promise<Response>
   */
  static async sendErrorMsg(
    c: Context,
    error: Error | string,
    title: string = "系统错误",
    additionalInfo?: Record<string, any>
  ): Promise<Response> {
    let errorMsg: string;

    if (error instanceof Error) {
      errorMsg = `**错误信息:** ${error.message}\n\n**错误堆栈:**\n\`\`\`\n${error.stack || 'No stack trace available'}\n\`\`\``;
    } else {
      errorMsg = `**错误信息:** ${error}`;
    }

    // 添加额外信息
    if (additionalInfo) {
      errorMsg += `\n\n**额外信息:**\n`;
      for (const [key, value] of Object.entries(additionalInfo)) {
        errorMsg += `- **${key}:** ${JSON.stringify(value)}\n`;
      }
    }

    // 添加请求信息
    if (c.req) {
      errorMsg += `\n\n**请求信息:**\n`;
      errorMsg += `- **URL:** ${c.req.url}\n`;
      errorMsg += `- **Method:** ${c.req.method}\n`;
      errorMsg += `- **User-Agent:** ${c.req.header('User-Agent') || 'Unknown'}\n`;
    }

    return this.sendMessage(this.ROBOT_ERROR_API, c, errorMsg, title, true);
  }

  /**
   * 发送系统启动消息
   * @param c Context对象
   * @returns Promise<Response>
   */
  static async sendStartupMsg(c: Context): Promise<Response> {
    const msg = `🚀 **系统启动成功**\n\n服务已正常启动并运行中...`;
    return this.sendMsg(c, msg, "系统启动");
  }

  /**
   * 发送系统关闭消息
   * @param c Context对象
   * @returns Promise<Response>
   */
  static async sendShutdownMsg(c: Context): Promise<Response> {
    const msg = `🛑 **系统正在关闭**\n\n服务即将停止运行...`;
    return this.sendMsg(c, msg, "系统关闭");
  }
}
