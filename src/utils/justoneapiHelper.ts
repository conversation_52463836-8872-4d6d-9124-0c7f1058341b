/**
 * JustOneAPI Helper
 * 封装对 JustOneAPI 的调用
 */

export interface JustOneApiResponse<T = any> {
	code: number;
	message: string;
	data?: T;
}

/**
 * JustOneAPI 助手类
 */
export class JustOneApiHelper {
	private static readonly BASE_URL = 'https://api.justoneapi.com/api';
	// private static readonly BASE_URL = 'http://47.117.133.51:30015/api';
	private static readonly DEFAULT_TIMEOUT = 30000; // 30秒超时

	/**
	 * 通用 API 请求方法
	 * @param endpoint API 端点
	 * @param token API Token
	 * @param params 请求参数
	 * @param timeout 超时时间（毫秒）
	 * @returns Promise<JustOneApiResponse<T>>
	 */
	private static async makeRequest<T>(
		endpoint: string,
		params: Record<string, any> = {},
		timeout: number = this.DEFAULT_TIMEOUT
	): Promise<JustOneApiResponse<T>> {
		try {
			params['token'] = '1ZZMyJWC'
			const url = new URL(`${this.BASE_URL}${endpoint}`);

			// 添加查询参数
			Object.entries(params).forEach(([key, value]) => {
				if (value !== undefined && value !== null) {
					url.searchParams.append(key, String(value));
				}
			});
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), timeout);

			const response = await fetch(url.toString(), {
				method: 'GET',
				headers: {
					Accept: 'application/json',
					'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
				},
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			if (!response.ok) {
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}

			const data = await response.json();
			return data as JustOneApiResponse<T>;
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === 'AbortError') {
					throw new Error('请求超时');
				}
				throw new Error(`API请求失败: ${error.message}`);
			}
			throw new Error('未知错误');
		}
	}

	/**
	 * 获取小红书笔记详情
	 * @param noteId 笔记ID
	 * @param timeout 超时时间（毫秒）
	 * @returns Promise<JustOneApiResponse<XhsNoteDetail>>
	 */
	static async getXhsNoteDetail( noteId: string, timeout?: number): Promise<JustOneApiResponse<any>> {
		return this.makeRequest<any>('/xiaohongshu/get-note-detail/v8', { noteId }, timeout);
	}

	/**
	 * 从小红书URL中提取笔记ID
	 * @param url 小红书笔记URL
	 * @returns 笔记ID或null
	 */
	static async extractNoteIdFromUrl(url: string): Promise<string | null> {
		// 支持多种小红书URL格式
		//如果是短链
		if (url.includes('xhslink.com')) {
			const res = await fetch(url, {
				method: 'GET',
        redirect: 'manual', 
				headers: {
					accept:
						'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
					'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
					priority: 'u=0, i',
					'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
					'sec-ch-ua-mobile': '?0',
					'sec-ch-ua-platform': '"Windows"',
					'sec-fetch-dest': 'document',
					'sec-fetch-mode': 'navigate',
					'sec-fetch-site': 'none',
					'sec-fetch-user': '?1',
					'upgrade-insecure-requests': '1',
					'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
				},
			});
       const location = res.headers.get('location') ?? '';
       if (!location) {
         throw new Error(url + ' 未提取到笔记ID')
       }
      return await this.extractNoteIdFromUrl(location);
		}
		const patterns = [/explore\/([a-zA-Z0-9]+)/, /discovery\/item\/([a-zA-Z0-9]+)/, /\/([a-zA-Z0-9]{24})/, /noteId=([a-zA-Z0-9]+)/];

		for (const pattern of patterns) {
			const match = url.match(pattern);
			if (match && match[1]) {
				return match[1];
			}
		}

		return null;
	}
}

/**
 * 创建 JustOneAPI 实例的便捷函数
 * @returns JustOneApiHelper 类
 */
export function createJustOneApi(): typeof JustOneApiHelper {
	return JustOneApiHelper;
}
