/**
 * URL代理助手工具类
 * 用于构建代理请求的函数
 */
export class UrlProxyHelper {
    private static SECRET_KEY = "6m9VM01vDwl9yz7L";

    // 生成签名
    static async generateSignature(url: string, expire: number): Promise<string> {
        const message = `${url}&&${expire}`;
        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey(
            "raw",
            encoder.encode(this.SECRET_KEY),
            {name: "HMAC", hash: "SHA-256"},
            false,
            ["sign"]
        );
        const signatureBuffer = await crypto.subtle.sign(
            "HMAC",
            key,
            encoder.encode(message)
        );
        const signatureArray = Array.from(new Uint8Array(signatureBuffer));
        return encodeURIComponent(
            signatureArray.map((b) => b.toString(16).padStart(2, "0")).join("")
        );
    }

    // 构建代理URL
    static async buildProxyUrl(
        url: string,
        expireAdd: number = 60 * 60,
        platform: string = "",
        ck: string = "",
        parseUrl: string = ""
    ): Promise<string> {
        const expire = Math.floor(Date.now() / 1000) + expireAdd;
        const signature = await this.generateSignature(url, expire);

        const baseUrl = "https://snappdown.com/api/proxy/media?";
        const params = new URLSearchParams();
        params.append("url", url);
        params.append("signature", signature);
        params.append("expire", expire.toString());
        if (platform) {
            params.append("platform", platform);
        }
        if (ck) {
            params.append("ck", ck);
        }
        if (parseUrl) {
            params.append("parse_url", parseUrl);
        }

        return baseUrl + params.toString();
    }
}