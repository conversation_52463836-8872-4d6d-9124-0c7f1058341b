

// 轮询计数器，用于负载均衡
let currentEndpointIndex = 0;

// 重试配置
const RETRY_COUNT = 1; // 每个端点重试次数
const RETRY_DELAY = 1000; // 重试延迟（毫秒）

// AES解密配置
const AES_KEY = "6m9VM01vDwl9yz7L";

export interface ApiResponse<T = any> {
	code: number;
	message: string;
	success: boolean;
	data: T | null;
}

/**
 * 延迟函数
 * @param ms 延迟毫秒数
 */
const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

/**
 * AES解密方法
 * @param encryptedData Base64编码的加密数据
 * @returns Promise<any> 解密后的数据
 */
const aesDecrypt = async (encryptedData: string): Promise<any> => {
  try {
    // 将Base64编码的加密数据解析为二进制数据
    const rawData = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));

    // 提取IV（前16字节）
    const iv = rawData.slice(0, 16);
    if (iv.length !== 16) {
      throw new Error('无效的IV数据');
    }

    // 提取密文（从第17字节开始）
    const cipherText = rawData.slice(16);

    // 导入密钥
    const keyData = new TextEncoder().encode(AES_KEY);
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'AES-CBC', length: 128 },
      false,
      ['decrypt']
    );

    // 解密
    const decryptedBuffer = await crypto.subtle.decrypt(
      { name: 'AES-CBC', iv },
      cryptoKey,
      cipherText
    );

    // 将解密后的数据转换为字符串并解析为JSON
    const decryptedText = new TextDecoder().decode(decryptedBuffer);
    return JSON.parse(decryptedText);
  } catch (error) {
    console.error('解密失败:', error);
    throw new Error('数据解密失败');
  }
};

/**
 * 通用的API请求函数，支持多个端点的轮询调用和重试机制
 * @param path API路径
 * @param options 请求选项
 * @returns Promise<any> API响应
 */
export const dataToolApiRequest = async (
    path: string,
    options: RequestInit = {}
): Promise<ApiResponse> => {
    const errors: string[] = [];
    const endpoints = [
        // "http://127.0.0.1:9090/frontend",
        "https://api.datatool.vip/frontend",
        "https://datatool-parse-908378976325.us-east1.run.app", // google-cloud-east
        "https://datatool-parse-ferhpppydk.us-east-1.fcapp.run", // aliyun-east
        "https://datatool-cloud-tw-test-908378976325.asia-east1.run.app",
        "https://datatool-cloud-test-908378976325.us-east1.run.app",

        // "http://127.0.0.1:8080"
    ];
      for (let i = endpoints.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [endpoints[i], endpoints[j]] = [endpoints[j], endpoints[i]];
    }
    // 轮询负载均衡：从当前索引开始尝试所有端点
    for (let i = 0; i < endpoints.length; i++) {
        const endpointIndex = (currentEndpointIndex + i) % endpoints.length;
        const endpoint = endpoints[endpointIndex];
        
        // 对每个端点进行重试
        for (let retryCount = 0; retryCount <= RETRY_COUNT; retryCount++) {
            try {
                const url = `${endpoint}${path}`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                    },
                    ...options,
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json() as ApiResponse;
                if (!data.success) {
                    throw new Error(data.message ?? "请求失败");
                }
                if (data.code !== 200) {
                    throw new Error(data.message ?? `请求失败，code: ${data.code}`);
                }

                // 请求成功，更新轮询计数器到下一个端点
                currentEndpointIndex = (currentEndpointIndex + 1) % endpoints.length;

                // 判断是否为 /frontend 路由，如果是则需要解密data
                let resultData = data.data;
                if (endpoint.includes('/frontend') && resultData && typeof resultData === 'string') {
                    try {
                        resultData = await aesDecrypt(resultData);
                        // console.info('解密后的数据:', resultData);
                    } catch (decryptError) {
                        console.error('数据解密失败:', decryptError);
                        throw new Error('数据解密失败，请检查数据格式');
                    }
                }

                return resultData;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                const retryInfo = retryCount < RETRY_COUNT ? ` (重试 ${retryCount + 1}/${RETRY_COUNT + 1})` : ' (最终失败)';
                console.warn(`API request failed for ${endpoint}${retryInfo}:`, errorMessage);
                
                // 如果不是最后一次重试，等待后重试
                if (retryCount < RETRY_COUNT) {
                    await delay(RETRY_DELAY);
                } else {
                    // 最后一次重试失败，记录错误
                    errors.push(`${endpoint}: ${errorMessage} (重试${RETRY_COUNT}次后失败)`);
                }
            }
        }
    }

    throw new Error(`All API endpoints failed after retries.`);
};