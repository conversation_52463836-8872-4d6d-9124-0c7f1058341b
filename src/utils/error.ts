import { ZodError } from "zod";
import { createErrorResponse, ResponseCode } from "./response";
import { Context } from "hono";

export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export const createError = (message: string, statusCode: number) => {
  return new AppError(message, statusCode);
}; 

export const onError = (err: Error, c: Context) => {
  if (err instanceof ZodError) {
    return c.json(
      createErrorResponse(
        err.errors[0].message,         // 提示信息
        ResponseCode.BAD_REQUEST,
        null        // 包装在 data 字段下
      ),
      ResponseCode.BAD_REQUEST
    );
  }

  return c.json(
    createErrorResponse(
      err.message || 'Internal Server Error',
      ResponseCode.INTERNAL_ERROR
    ),
    ResponseCode.INTERNAL_ERROR
  );
}