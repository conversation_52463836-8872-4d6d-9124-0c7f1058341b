import { Context } from 'hono';

interface ApiResponse<T = any> {
  code: number;
  message: string;
  success: boolean;
  data: T | null;
}

export function createResponse<T>(
  data: T | null = null,
  message: string = 'Success',
  code: number = 200,
  success: boolean = true
): ApiResponse<T> {
  return {
    code,
    message,
    success,
    data,
  };
}

export function createErrorResponse(
  message: string = 'Error',
  code: number = 500,
  data: any = null
): ApiResponse {
  return {
    code,
    message,
    success: false,
    data,
  };
}

// Common response codes
export const ResponseCode = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
  INSUFFICIENT_CREDITS: 4001, // 自定义错误码：积分不足
} as const;

type StatusCode = typeof ResponseCode[keyof typeof ResponseCode];

// Custom error class for credit exhaustion
export class InsufficientCreditsError extends Error {
  public readonly code: number;

  constructor(message: string = '您的 Credits 已耗尽，请前往 https://data.snappdown.com 充值后使用') {
    super(message);
    this.name = 'InsufficientCreditsError';
    this.code = ResponseCode.INSUFFICIENT_CREDITS;
  }
}

// Helper function to send response
export function sendResponse<T>(
  c: Context,
  data: T | null = null,
  message: string = 'Success',
  code: StatusCode = ResponseCode.SUCCESS
) {
  return c.json(createResponse(data, message, code), 200);
}

// Helper function to send error response
export function sendErrorResponse(
  c: Context,
  message: string = 'Error',
  code: StatusCode = ResponseCode.INTERNAL_ERROR,
  data: any = null
) {
  console.info(data, 'data');
  // Map custom error codes to appropriate HTTP status codes
  return c.json(createErrorResponse(message, code, null), 500);
}