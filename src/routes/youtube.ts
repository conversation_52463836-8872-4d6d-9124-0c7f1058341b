import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { YouTubeService } from '../services/youtube';
import { CreditsService } from '../services/credits';
import { SupadataYouTubeAPI } from '../utils/supadataHelper';
import {
	getYouTubeUserInfoSchema,
	getYouTubeUserVideosSchema,
	getYouTubeVideoInfoSchema,
	getYouTubeVideoSchema,
	getYouTubeChannelSchema,
	getYouTubeTranscriptSchema,
	getYouTubeTranslationSchema
} from '../validators/youtube';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
};

const youtube = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 获取YouTube用户信息
youtube.post(
	'/user/info',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getYouTubeUserInfoSchema, async (c) => {
		const youTubeService = new YouTubeService(c);
		const { url } = c.validatedData;
		return (await youTubeService.getUserInfo(url));
	})
);

// 获取YouTube用户视频/Shorts列表
youtube.post(
	'/user/videos',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getYouTubeUserVideosSchema, async (c) => {
		const youTubeService = new YouTubeService(c);
		return (await youTubeService.getUserContentByType(c.validatedData))?.entries;
	})
);

// 获取YouTube视频详情
youtube.post(
	'/video/info',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getYouTubeVideoInfoSchema, async (c) => {
		const youTubeService = new YouTubeService(c);
		const { url } = c.validatedData;
		return (await youTubeService.getVideoInfo(url));
	})
);

// ===== 新增的 Supadata API 代理接口 =====

// 获取YouTube视频元数据 (代理 supadataHelper.getVideo)
youtube.post(
	'/video',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getYouTubeVideoSchema, async (c) => {
		const { id } = c.validatedData;
		const user = c.get('user');
		const creditsService = new CreditsService(c);
		const supadataAPI = new SupadataYouTubeAPI();

		// 检查用户积分
		await creditsService.checkUserCreditByType(user.id, 'youtube_video_info_deduct');

		// 构建缓存键
		const cacheKey = `youtube_supadata_video_${id}`;

		// 尝试从缓存获取
		const cached = await c.env.datatool_kv.get(cacheKey);
		if (cached) {
			await creditsService.deductUserCredit(
				user.id,
				'youtube_video_info_deduct',
				'YouTube视频元数据获取',
				user.id
			);
			return JSON.parse(cached);
		}

		// 调用 Supadata API
		const result = await supadataAPI.getVideo(id);

		if (result.success && result.data) {
			// 缓存结果（缓存1小时）
			await c.env.datatool_kv.put(cacheKey, JSON.stringify(result.data), { expirationTtl: 3600 });

			// 扣除积分
			await creditsService.deductUserCredit(
				user.id,
				'youtube_video_info_deduct',
				'YouTube视频元数据获取',
				user.id
			);

			return result.data;
		} else {
			throw new Error(result.error || 'Failed to get video data');
		}
	})
);

// 获取YouTube频道信息 (代理 supadataHelper.getChannel)
youtube.post(
	'/channel',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getYouTubeChannelSchema, async (c) => {
		const { channel_id } = c.validatedData;
		const user = c.get('user');
		const creditsService = new CreditsService(c);
		const supadataAPI = new SupadataYouTubeAPI();

		// 检查用户积分
		await creditsService.checkUserCreditByType(user.id, 'youtube_user_info_deduct');

		// 构建缓存键
		const cacheKey = `youtube_supadata_channel_${channel_id}`;

		// 尝试从缓存获取
		const cached = await c.env.datatool_kv.get(cacheKey);
		if (cached) {
			await creditsService.deductUserCredit(
				user.id,
				'youtube_user_info_deduct',
				'YouTube频道信息获取',
				user.id
			);
			return JSON.parse(cached);
		}

		// 调用 Supadata API
		const result = await supadataAPI.getChannel(channel_id);

		if (result.success && result.data) {
			// 缓存结果（缓存1小时）
			await c.env.datatool_kv.put(cacheKey, JSON.stringify(result.data), { expirationTtl: 3600 });

			// 扣除积分
			await creditsService.deductUserCredit(
				user.id,
				'youtube_user_info_deduct',
				'YouTube频道信息获取',
				user.id
			);

			return result.data;
		} else {
			throw new Error(result.error || 'Failed to get channel data');
		}
	})
);

export default youtube;