import { Hono } from 'hono';
import { ApiResponseHandler } from '../utils/apiHandler';
import { EmailService } from '../services/email';
import { sendVerificationCodeSchema } from '../validators/email';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	BREVO_API_KEY: string;
};

const emailRouter = new Hono<{ Bindings: Bindings }>();

// 发送验证码
emailRouter.post('/send-code', 
	ApiResponseHandler.wrapWithZod(sendVerificationCodeSchema, async (c) => {
		const { email } = c.validatedData;

		const emailService = new EmailService(c);
		const result = await emailService.sendVerificationCode(email);
		return {
			message: result.message
		};
	})
);



export { emailRouter as emailRoutes };
export default emailRouter;