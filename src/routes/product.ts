import { Hono } from 'hono';
import { authMiddleware, User } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { D1Database } from '@cloudflare/workers-types';
import { ProductService } from '../services/product';
import { createProductSchema, deleteProductSchema, getProductDetailSchema, getProductListSchema, updateProductSchema } from '../validators/product';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

export const product = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 获取商品列表
product.post(
  '/list',
  ApiResponseHandler.wrapWithZod(
    getProductListSchema,
    async (c) => {
      const productService = new ProductService(c);
      return await productService.getProductList(c.validatedData);
    }
  )
);

// 创建商品
product.post(
  '/create',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    createProductSchema,
    async (c) => {
      const productService = new ProductService(c);
      return await productService.createProduct(c.validatedData);
    }
  )
);

// 更新商品
product.put(
  '/:id',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    updateProductSchema,
    async (c) => {
      const productService = new ProductService(c);
      return await productService.updateProduct(c.validatedData);
    
    }
  )
);

// 获取商品详情
product.get(
  '/:id',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getProductDetailSchema,
    async (c) => {
      const productService = new ProductService(c);
      return await productService.getProductById(c.validatedData.id);
    }
  )
);

export default product;