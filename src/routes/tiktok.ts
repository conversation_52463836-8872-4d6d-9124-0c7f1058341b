import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { getTikTokUserInfoSchema, getTikTokUserPostsSchema, getTikTokUserFansSchema, getTikTokVideoDetailSchema } from '../validators/tiktok';
import { TikTokService } from '../services/tiktok';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
};

const tiktok = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 获取TikTok用户信息
tiktok.post(
	'/user/info',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getTikTokUserInfoSchema, async (c) => {
		const tikTokService = new TikTokService(c);
		return (await tikTokService.getUserInfo(c.validatedData));
	})
);

// 获取TikTok用户发布的作品
tiktok.post(
	'/user/posts',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getTikTokUserPostsSchema, async (c) => {
		const tikTokService = new TikTokService(c);
		return (await tikTokService.getUserPosts(c.validatedData));
	})
);

tiktok.post(
	'/user/fans',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getTikTokUserFansSchema, async (c) => {
		const tikTokService = new TikTokService(c);
		return (await tikTokService.getUserFans(c.validatedData));
	})
);

tiktok.post(
    '/video/detail',
    authMiddleware,
    ApiResponseHandler.wrapWithZod(getTikTokVideoDetailSchema, async (c) => {
        const tikTokService = new TikTokService(c);
        return (await tikTokService.getVideoDetail(c.validatedData));
    })
);

export default tiktok;