import { Hono } from 'hono';
import { authMiddleware, User } from '../../middleware/auth';
import { CreditTypeService } from '../../services/creditType';
import { ApiResponseHandler } from '../../utils/apiHandler';
import { createCreditTypeSchema, updateCreditTypeSchema, creditTypeIdSchema } from '../../validators/creditType';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

const creditTypes = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 获取所有积分类型
creditTypes.get('/', 
  ApiResponseHandler.wrapHandler(async (c) => {
    const creditTypeService = new CreditTypeService(c);
    const creditTypes = await creditTypeService.getAllCreditTypes();
    return creditTypes;
  }, {
    successMessage: '获取积分类型列表成功',
    errorMessage: '获取积分类型列表失败'
  })
);

// 获取单个积分类型
creditTypes.get('/:id', 
  ApiResponseHandler.wrapWithZod(creditTypeIdSchema, async (c) => {
    const creditTypeService = new CreditTypeService(c);
    const creditType = await creditTypeService.getCreditTypeById(c.validatedData.id);
    if (!creditType) {
      throw new Error('积分类型不存在');
    }
    return creditType;
  }, {
    successMessage: '获取积分类型成功',
    errorMessage: '获取积分类型失败'
  })
);

// 创建积分类型
creditTypes.post('/', 
  authMiddleware,
  ApiResponseHandler.wrapWithZod(createCreditTypeSchema, async (c) => {
    const creditTypeService = new CreditTypeService(c);
    const newCreditType = await creditTypeService.createCreditType(c.validatedData);
    return newCreditType;
  }, {
    successMessage: '创建积分类型成功',
    errorMessage: '创建积分类型失败'
  })
);

// 更新积分类型
creditTypes.put('/:id', 
  authMiddleware,
  ApiResponseHandler.wrapWithZod(updateCreditTypeSchema.merge(creditTypeIdSchema), async (c) => {
    const { id, ...updateData } = c.validatedData;
    const creditTypeService = new CreditTypeService(c);
    const updatedCreditType = await creditTypeService.updateCreditType(id, updateData);
    if (!updatedCreditType) {
      throw new Error('积分类型不存在');
    }
    return updatedCreditType;
  }, {
    successMessage: '更新积分类型成功',
    errorMessage: '更新积分类型失败'
  })
);

export default creditTypes;