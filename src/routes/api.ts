import { Hono } from 'hono';
import { User } from '../middleware/auth';
import authRoutes from './auth';
import xhsRoutes from './xhs';
import adminRoutes from './admin';
import taskRoutes from './task';
import queueRoutes from './queue';
import mediaRoutes from './media';
import dyRoutes from './dy';
import tikTokRoutes from './tiktok';
import youtubeRoutes from './youtube';
import kuaishouRoutes from './kuaishou';
import { product as productRoutes } from './product';
import { stripe as stripeRoutes } from './stripe';
import configRoutes from './config';
import downloadRoutes from './download';
import { emailRoutes } from './email';
import browserRoutes from './browser';
// 定义环境变量类型
type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	QUEUE: Queue;
};

const api = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// Add API prefix middleware for handling responses
api.use('*', async (c, next) => {
	await next();
	if (!c.res.headers.get('X-API-Version')) {
		c.res.headers.set('X-API-Version', '1.0');
	}
});

// Mount route groups
api.route('/auth', authRoutes);
api.route('/xhs', xhsRoutes);
api.route('/admin', adminRoutes);
api.route('/task', taskRoutes);
api.route('/queue', queueRoutes);
api.route('/media', mediaRoutes);
api.route('/dy', dyRoutes);
api.route('/tiktok', tikTokRoutes);
api.route('/youtube', youtubeRoutes);
api.route('/kuaishou', kuaishouRoutes);
api.route('/product', productRoutes);
api.route('/stripe', stripeRoutes);
api.route('/config', configRoutes);
api.route('/download', downloadRoutes);
api.route('/email', emailRoutes);
api.route('/browser', browserRoutes);

export default api;
