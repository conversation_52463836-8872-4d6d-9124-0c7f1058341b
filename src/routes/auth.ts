import { Hono } from 'hono';
import { authMiddleware, User } from '../middleware/auth';
import { ResponseCode } from '../utils/response';
import { loginSchema } from '../validators/auth';
import { AuthService } from '../services/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { D1Database } from '@cloudflare/workers-types';
import { getUserCreditRecordSchema } from '../validators/user';
import { CreditsService } from '../services/credits';
import { CreditTypeService } from '../services/creditType';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

const auth = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// Login route
auth.post(
  '/login',
  ApiResponseHandler.wrapWithZod(
    loginSchema,
    async (c) => {
      const authService = new AuthService(c);
      const result = await authService.login(c.validatedData);
      return result;
    },
    {
      errorMessage: 'Invalid credentials',
      errorCode: ResponseCode.UNAUTHORIZED
    }
  )
);

// Protected route example
auth.get(
  '/me',
  authMiddleware,
  ApiResponseHandler.wrapHandler((c) => {
    const user = c.get('user');
    return { user };
  })
);

auth.post(
  '/credit/record',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getUserCreditRecordSchema,
    async (c) => {
      const creditsService = new CreditsService(c);
      const user = c.get('user');
      if (user.role === 'admin' && c.validatedData.username) {
        const authService = new AuthService(c);
        const adminUser = await authService.getUserByUsername(c.validatedData.username);
        if (!adminUser) {
          throw new Error('Admin user not found');
        }
        return await creditsService.getUserCreditRecord(adminUser.id, c.validatedData);
      }
      return await creditsService.getUserCreditRecord(user.id, c.validatedData);
    }
  )
)

// 获取积分变更类型列表
auth.get(
  '/credit/types',
  ApiResponseHandler.wrapHandler(async (c) => {
    const creditTypeService = new CreditTypeService(c);
    const creditTypes = await creditTypeService.getAllCreditTypes();
    const types = creditTypes.map((creditType: typeof import('../models/schema').creditTypeModel.$inferSelect) => ({
      value: creditType.type_key,
      label: creditType.type_name
    }));
    return types;
  })
);

// 刷新API Key
auth.post(
  '/refresh-api-key',
  authMiddleware,
  ApiResponseHandler.wrapHandler(async (c) => {
    const user = c.get('user');
    const authService = new AuthService(c);
    const newApiKey = await authService.refreshApiKey(user.id);
    
    return {
      api_key: newApiKey,
      message: 'API Key 已成功更新'
    };
  })
);

export default auth;