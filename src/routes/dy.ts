import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { getXtVideoInfo, getDouyinUserVideos, getDouyinVideoComments, getDouyinCommentReplies } from '../validators/dy';
import { DyService } from '../services/dy';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
};

const dy = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

dy.post(
	'/xt/info',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getXtVideoInfo, async (c) => {
		const dyService = new DyService(c);
		return await dyService.getVideoInfo(c.validatedData);
	})
);

dy.post(
	'/video/info',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getXtVideoInfo, async (c) => {
		const dyService = new DyService(c);
		return await dyService.getDyVideoInfo(c.validatedData);
	})
);

dy.post(
	'/detail',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getXtVideoInfo, async (c) => {
		const dyService = new DyService(c);
		return await dyService.getDyVideoInfo(c.validatedData);
	})
);

dy.post(
	'/user/videos',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getDouyinUserVideos, async (c) => {
		const dyService = new DyService(c);
		return await dyService.getUserVideos(c.validatedData);
	})
);

dy.post(
	'/video/comments',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getDouyinVideoComments, async (c) => {
		const dyService = new DyService(c);
		return await dyService.getVideoComments(c.validatedData);
	})
);

dy.post(
	'/comment/replies',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getDouyinCommentReplies, async (c) => {
		const dyService = new DyService(c);
		return await dyService.getCommentReplies(c.validatedData);
	})
);

export default dy;
