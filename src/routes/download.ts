import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { DownloadHelper } from '../utils/downloadHelper';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	QUEUE: Queue;
};

const download = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 使用DownloadHelper的代理下载API
download.get(
	'/proxy',
	async (c) => {
		return await DownloadHelper.handleRequest(c);
	}
);

// M3U8代理下载API
download.get(
	'/m3u8',
	authMiddleware,
	async (c) => {
		return await DownloadHelper.handleM3u8Request(c);
	}
);

export default download;