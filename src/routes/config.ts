import { Hono } from 'hono';
import { ApiResponseHandler } from '../utils/apiHandler';
import { createConfig } from '../config';
import { D1Database } from '@cloudflare/workers-types';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

const config = new Hono<{ Bindings: Bindings }>();

// 获取公开配置信息
config.get(
  '/public',
  ApiResponseHandler.wrapHandler((c) => {
    const appConfig = createConfig(c.env);
    
    // 只返回前端需要的公开配置信息
    return {
      stripe: {
        publishKey: appConfig.stripe.publishKey
      },
      google: {
        clientId: c.env.GOOGLE_CLIENT_ID,
        clientSecret: c.env.GOOGLE_CLIENT_SECRET
      }
    };
  })
);

export default config;