import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { getKuaishouVideoDetailSchema, getKuaishouCommentsSchema, getKuaishouRepliesSchema } from '../validators/kuaishou';
import { KuaishouService } from '../services/kuaishou';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
};

const kuaishou = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 获取快手视频详情
kuaishou.post(
	'/video/detail',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getKuaishouVideoDetailSchema, async (c) => {
		const kuaishouService = new KuaishouService(c);
		return await kuaishouService.getVideoDetail(c.validatedData);
	})
);

// 获取快手评论列表
kuaishou.post(
	'/comments',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getKuaishouCommentsSchema, async (c) => {
		const kuaishouService = new KuaishouService(c);
		return await kuaishouService.getComments(c.validatedData);
	})
);

// 获取快手回复列表
kuaishou.post(
	'/replies',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getKuaishouRepliesSchema, async (c) => {
		const kuaishouService = new KuaishouService(c);
		return await kuaishouService.getReplies(c.validatedData);
	})
);

export default kuaishou;
