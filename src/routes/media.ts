import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { parseQuerySchema } from '../validators/media';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

const media = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

media.post(
  '/parse',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    parseQuerySchema,
    async (c) => {
      const res = await fetch("https://snappdown.com/api/parse", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Secret":"datatool-hono"
        },
        body: JSON.stringify({
          url: c.validatedData.url,
        }),
      });
      return await res.json();
    }
  )
);

export default media; 