import { Hono } from 'hono';
import { authMiddleware, User } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { D1Database } from '@cloudflare/workers-types';
import { getUserListSchema, updateUserCreditSchema, updateUserStatusSchema, updateUserRemarkSchema } from '../validators/admin';
import { AdminService } from '../services/admin';
import { createUserSchema } from '../validators/user';
import creditTypesRoutes from './admin/creditTypes';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

const admin = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// Login route
admin.post(
  '/users',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getUserListSchema,
    async (c) => {
      const adminService = new AdminService(c);
      const { users, total } = await adminService.getUserList(c.validatedData);
      return {
        users,
        total,
      };
    }
  )
);

admin.post(
  '/users/create',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    createUserSchema,
    async (c) => {
      const adminService = new AdminService(c);
      return await adminService.createUser(c.validatedData);
    }
  )
);

admin.put('/users/:id/status', authMiddleware, ApiResponseHandler.wrapWithZod(
  updateUserStatusSchema,
  async (c) => {
    const adminService = new AdminService(c);
    await adminService.updateUserStatus(c.validatedData);
    return { success: true };
  }
));

admin.put('/users/:id/credit', authMiddleware, ApiResponseHandler.wrapWithZod(
  updateUserCreditSchema,
  async (c) => {
    const adminService = new AdminService(c);
    await adminService.updateUserCredit(c.validatedData);
    return { success: true };
  }
));

admin.put('/users/:id/remark', authMiddleware, ApiResponseHandler.wrapWithZod(
  updateUserRemarkSchema,
  async (c) => {
    const adminService = new AdminService(c);
    await adminService.updateUserRemark(c.validatedData);
    return { success: true };
  }
));

// 积分类型路由
admin.use('/credit-types/*', authMiddleware);
admin.route('/credit-types', creditTypesRoutes);

export default admin;