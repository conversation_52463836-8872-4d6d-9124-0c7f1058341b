import { Hono } from 'hono';
import { authMiddleware, User } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { D1Database } from '@cloudflare/workers-types';
import { StripeService } from '../services/stripe';
import { getPaymentStatusSchema, createPaymentIntentSchema } from '../validators/stripe';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	STRIPE_SECRET_KEY: string;
	STRIPE_WEBHOOK_SECRET: string;
	STRIPE_SUCCESS_URL: string;
	STRIPE_CANCEL_URL: string;
};

export const stripe = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// 获取支付会话状态
stripe.post(
	'/payment-status',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(getPaymentStatusSchema, async (c) => {
		const validatedData = c.validatedData;

		const stripeService = new StripeService(c);
		return await stripeService.getPaymentStatus(validatedData.paymentIntent);
	})
);

// 创建Payment Intent（用于嵌入式支付）
stripe.post(
	'/create-payment-intent',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(createPaymentIntentSchema, async (c) => {
		const stripeService = new StripeService(c);
		const result = await stripeService.createPaymentIntent(c.validatedData);
		return result;
	})
);

// 获取用户付款记录
stripe.get(
	'/payment-history',
	authMiddleware,
	ApiResponseHandler.wrapHandler(async (c) => {
		const stripeService = new StripeService(c);
		const result = await stripeService.getPaymentHistory();
		return result;
	})
);

// Stripe Webhook处理（不需要认证）
stripe.post('/webhook', async (c) => {
	try {
		const body = await c.req.text();
		console.info(body)
		const signature = c.req.header('stripe-signature');
		
		if (!signature) {
			throw new Error('Missing stripe-signature header');
		}

		const stripeService = new StripeService(c);
		const result = await stripeService.handleWebhook(body, signature);

		return c.json({ success: true, data: result });
	} catch (error: any) {
		console.error('Webhook处理失败:', error);
		return c.json({ success: false, error: error.message }, 400);
	}
});

export default stripe;
