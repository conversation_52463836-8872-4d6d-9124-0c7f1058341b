import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { User, authMiddleware } from '../middleware/auth';
import { BrowserService } from '../services/browser';
import { renderRequestSchema, renderTypesQuerySchema } from '../validators/browser';
import { withZod } from '../utils/withZod';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	QUEUE: Queue;
	BROWSER?: Fetcher;
	miner_browser: Fetcher;
};

const browser = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// Optimize OPTIONS requests for browser routes
browser.options('*', (c) => {
	return new Response(null, {
		status: 204,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key',
			'Access-Control-Max-Age': '86400',
		},
	});
});

// 启用CORS
browser.use('*', cors({
	origin: '*',
	allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
	allowHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
}));

// 网页渲染API
browser.post('/render',
	//  authMiddleware,
	  withZod('json', renderRequestSchema), async (c) => {
	try {
		// 调用浏览器服务
		const browserService = new BrowserService(c);
		const response = await browserService.handleRequest(c.req.valid('json'));

		return response;
	} catch (error) {
		console.error('浏览器渲染失败:', error);
		return c.json(
			{
				code: 500,
				message: '渲染失败',
				success: false,
				data: null,
			},
			500
		);
	}
});

// 获取支持的渲染类型
browser.get('/render-types', withZod('query', renderTypesQuerySchema), async (c) => {
	const { detailed } = c.req.valid('query');

	const renderTypes = [
		{
			type: 'pdf',
			name: 'PDF',
			description: '便携式文档格式，适合打印和分享',
			options: detailed
				? {
						format: { type: 'enum', values: ['A4', 'A3', 'A5', 'Letter', 'Legal', 'Tabloid'], default: 'A4' },
						landscape: { type: 'boolean', default: false },
						printBackground: { type: 'boolean', default: true },
						margin: { type: 'object', properties: ['top', 'bottom', 'left', 'right'] },
				  }
				: ['format', 'landscape', 'printBackground', 'margin'],
		},
		{
			type: 'html',
			name: 'HTML',
			description: '超文本标记语言，保留完整网页结构',
			options: detailed
				? {
						includeStyles: { type: 'boolean', default: true },
						cleanContent: { type: 'boolean', default: false },
				  }
				: ['includeStyles', 'cleanContent'],
		},
		{
			type: 'markdown',
			name: 'Markdown',
			description: '轻量级标记语言，适合文档编写',
			options: detailed
				? {
						headingStyle: { type: 'enum', values: ['atx', 'setext'], default: 'atx' },
						codeBlockStyle: { type: 'enum', values: ['fenced', 'indented'], default: 'fenced' },
				  }
				: ['headingStyle', 'codeBlockStyle'],
		},
	];

	return c.json({ renderTypes });
});

export default browser;
