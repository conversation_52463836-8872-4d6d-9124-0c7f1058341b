import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { createResponse } from '../utils/response';
import { queueSendSchema } from '../validators/queue';

type Bindings = {
	datatool_kv: KVNamespace;
	datatool_d1: D1Database;
	QUEUE: Queue;
};

const queue = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

queue.post(
	'/send',
	authMiddleware,
	ApiResponseHandler.wrapWithZod(queueSendSchema, async (c) => {
		// 创建消息对象
		const messageObj = {
			id: crypto.randomUUID(),
			content: JSON.stringify(c.validatedData.message),
			timestamp: Date.now(),
			user: c.get('user').username,
		};

		// 发送消息到队列
		await c.env.QUEUE.send(messageObj);

		return messageObj;
	})
);

export default queue;