import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { topicSearchSchema, topicNotesSchema, creatorCookieSchema } from '../validators/topic';
import { TopicService } from '../services/topic';
import { ApiResponseHandler } from '../utils/apiHandler';
import { getNoteDetailSchema, getUserNotesSchema, getNoteInfoSchema } from '../validators/xhs';
import { XhsService } from '../services/xhs';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
};

const xhs = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

// Topic search route
xhs.post(
  '/topic/search',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    topicSearchSchema,
    async (c) => {
      const topicService = new TopicService(c);
      return await topicService.searchTopics(c.validatedData);
    }
  )
);

// Topic notes route
xhs.post(
  '/topic/notes',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    topicNotesSchema,
    async (c) => {
      const topicService = new TopicService(c);
      return await topicService.getTopicNotes(c.validatedData);
    }
  )
);

// Creator cookie refresh route
xhs.post(
  '/creator/refresh',
  ApiResponseHandler.wrapWithZod(
    creatorCookieSchema,
    async (c) => {
      return await c.env.datatool_kv.put('xhs_creator_cookie', c.validatedData.cookie);
    }
  )
);

xhs.post(
  '/ad/refresh',
  ApiResponseHandler.wrapWithZod(
    creatorCookieSchema,
    async (c) => {
      return await c.env.datatool_kv.put('xhs_ad_sso', c.validatedData.cookie);
    }
  )
);

xhs.post(
  '/user/notes',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getUserNotesSchema,
    async (c) => {
      const xhsService = new XhsService(c);
      return await xhsService.getUserNotes(c.validatedData);
    }
  )
);

xhs.post(
  '/pgy/detail',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getNoteDetailSchema,
    async (c) => {
      const xhsService = new XhsService(c);
      return await xhsService.getPgyNoteDetail(c.validatedData);
    }
  )
);

xhs.post(
  '/ad/detail',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getNoteDetailSchema,
    async (c) => {
      const xhsService = new XhsService(c);
      return await xhsService.getAdNoteDetail(c.validatedData);
    }
  )
);

xhs.post(
  '/note/business',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getNoteDetailSchema,
    async (c) => {
      const xhsService = new XhsService(c);
      return await xhsService.getNoteBusinessData(c.validatedData);
    }
  )
);

xhs.post(
  '/note/info',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    getNoteInfoSchema,
    async (c) => {
      const xhsService = new XhsService(c);
      return await xhsService.getNoteInfo(c.validatedData);
    }
  )
);

export default xhs;