import { Hono } from 'hono';
import { User, authMiddleware } from '../middleware/auth';
import { ApiResponseHandler } from '../utils/apiHandler';
import { TaskService } from '../services/task';
import { taskCreateSchema, taskInfoSchema, taskQuerySchema, taskUpdateSchema } from '../validators/task';

type Bindings = {
  datatool_kv: KVNamespace;
  datatool_d1: D1Database;
  datatool: R2Bucket;
  QUEUE: Queue;
};

const task = new Hono<{ Variables: { user: User }; Bindings: Bindings }>();

task.post(
  '/create',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    taskCreateSchema,
    async (c) => {
      const taskService = new TaskService(c);
      return await taskService.create(c.validatedData);
    }
  )
);

task.post(
  '/update',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    taskUpdateSchema,
    async (c) => {
      const taskService = new TaskService(c);
      return await taskService.update(c.validatedData);
    }
  )
);

task.get(
  '/info',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    taskInfoSchema,
    async (c) => {
      const taskService = new TaskService(c);
      return await taskService.info(c.validatedData.task_id);
    }
  )
);

task.post(
  '/query',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    taskQuerySchema,
    async (c) => {
      const taskService = new TaskService(c);
      return await taskService.query(c.validatedData);
    }
  )
);
task.delete(
  '/delete',
  authMiddleware,
  ApiResponseHandler.wrapWithZod(
    taskInfoSchema,
    async (c) => {
      const taskService = new TaskService(c);
      return await taskService.delete(c.validatedData.task_id);
    }
  )
);

export default task;
