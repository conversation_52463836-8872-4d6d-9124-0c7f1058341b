import { DurableObject } from 'cloudflare:workers';

/**
 * UserLock Durable Object - 为用户积分操作提供分布式锁
 * 每个用户ID对应一个独立的Durable Object实例
 */
export class UserLock extends DurableObject {
  private locks: Map<string, { locked: boolean; queue: Array<() => void> }> = new Map();

  /**
   * 获取锁
   * @param lockKey 锁的键值，通常是操作类型
   * @param timeoutMs 超时时间（毫秒）
   * @returns Promise<boolean> 是否成功获取锁
   */
  async acquireLock(lockKey: string, timeoutMs: number = 30000): Promise<boolean> {
    return new Promise((resolve) => {
      const lockInfo = this.locks.get(lockKey) || { locked: false, queue: [] };
      
      if (!lockInfo.locked) {
        // 锁未被占用，直接获取
        lockInfo.locked = true;
        this.locks.set(lockKey, lockInfo);
        resolve(true);
        return;
      }

      // 锁已被占用，加入等待队列
      const timeoutId = setTimeout(() => {
        // 超时，从队列中移除
        const index = lockInfo.queue.indexOf(resolver);
        if (index > -1) {
          lockInfo.queue.splice(index, 1);
        }
        resolve(false);
      }, timeoutMs);

      const resolver = () => {
        clearTimeout(timeoutId);
        resolve(true);
      };

      lockInfo.queue.push(resolver);
      this.locks.set(lockKey, lockInfo);
    });
  }

  /**
   * 释放锁
   * @param lockKey 锁的键值
   */
  async releaseLock(lockKey: string): Promise<void> {
    const lockInfo = this.locks.get(lockKey);
    if (!lockInfo || !lockInfo.locked) {
      return;
    }

    if (lockInfo.queue.length > 0) {
      // 有等待的请求，将锁传递给下一个
      const nextResolver = lockInfo.queue.shift()!;
      nextResolver();
    } else {
      // 没有等待的请求，释放锁
      lockInfo.locked = false;
      this.locks.set(lockKey, lockInfo);
    }
  }

  /**
   * 处理HTTP请求
   */
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const action = url.pathname.split('/').pop();
    const lockKey = url.searchParams.get('lockKey') || 'default';

    try {
      switch (action) {
        case 'acquire': {
          const timeoutMs = parseInt(url.searchParams.get('timeout') || '30000');
          const acquired = await this.acquireLock(lockKey, timeoutMs);
          return new Response(JSON.stringify({ success: acquired }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }

        case 'release': {
          await this.releaseLock(lockKey);
          return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }

        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
}

export default UserLock;