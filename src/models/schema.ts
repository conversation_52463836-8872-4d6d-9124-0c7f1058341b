import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// 用户表
export const userModel = sqliteTable('t_user', {
	id: integer('id').primaryKey(),
	username: text('username'),
	password: text('password'),
	remark: text('remark'),
	api_key: text('api_key').notNull(),
	role: text('role').notNull().default('user'),
	status: integer('status').notNull().default(1),
	last_login_at: text('last_login_at'),
	created_at: integer('created_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	updated_at: integer('updated_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
});

// 商品表
export const productModel = sqliteTable('t_product', {
	id: integer('id').primaryKey(),
	stripe_product_id: text('stripe_product_id').unique(),
	stripe_price_id: text('stripe_price_id').unique(),
	is_active: integer('is_active').notNull().default(1),
	credits: integer('credits').notNull().default(0),
	price: real('price').notNull().default(0),
	currency: text('currency').notNull().default('CNY'),
	description: text('description'),
	name: text('name').notNull(),
	created_at: text('created_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	updated_at: text('updated_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
});

export const userCreditModel = sqliteTable('t_user_credit', {
	id: integer('id').primaryKey(),
	user_id: integer('user_id').notNull(),
	credit: integer('credit').notNull().default(0),
	created_at: integer('created_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	updated_at: integer('updated_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
});

// 积分变更记录
export const userCreditRecordModel = sqliteTable('t_user_credit_record', {
	id: integer('id').primaryKey(),
	user_id: integer('user_id').notNull(),
	change_before: integer('change_before').notNull(),
	change_after: integer('change_after').notNull(),
	change_credit: integer('change_credit').notNull(),
	change_type: text('change_type').notNull(),
	change_reason: text('change_reason').notNull(),
	act_user_id: integer('act_user_id').notNull().default(0),
	created_at: integer('created_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	updated_at: integer('updated_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
});

export const taskModel = sqliteTable('t_task', {
	id: integer('id').primaryKey(),
	task_id: text('task_id').notNull(),
	task_type: text('task_type').notNull(),
	status: text('status').notNull().default('pending'),
	sub_status: text('sub_status').default('pending'),
	language: text('language').notNull(),
	percent: integer('percent').notNull().default(0),
	country_code: text('country_code'),
	created_at: text('created_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	updated_at: text('updated_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	error: text('error'),
	object_key: text('object_key'),
	result: text('result'),
	media_url: text('media_url'),
	proxy_media_url: text('proxy_media_url'),
	model: text('model'),
	extra: text('extra'),
});


// 积分类型配置表
export const creditTypeModel = sqliteTable('t_credit_type', {
	id: integer('id').primaryKey(),
	type_key: text('type_key').notNull().unique(), // 积分类型键值，如 'xhs_topic_deduct'
	type_name: text('type_name').notNull(), // 积分类型名称，如 '小红书话题扣除'
	credit_amount: integer('credit_amount').notNull().default(0), // 积分数量
	operation_type: text('operation_type').notNull().default('deduct'), // 操作类型：add 或 deduct
	description: text('description'), // 描述
	created_at: integer('created_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
	updated_at: integer('updated_at')
		.notNull()
		.default(sql`CURRENT_TIMESTAMP`),
});

export const StripeCheckoutModel = sqliteTable('t_stripe_checkout', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  status: text('status').notNull().default('open'), // open complete expired
  paymentStatus: text('payment_status').notNull(),
  amount: integer('amount').notNull(),
  paymentIntent: text('payment_intent').notNull().default(''), // stripe paymentIntent Id
  currency: text('currency').notNull(), // 币种
  expiresAt: integer('expires_at').notNull(),
  paymentMethodTypes: text('payment_method_types').notNull(), // 支付方式
  paymentMethodDetails: text('payment_method_details'), // 实际支付方式详情
  sessionData: text('session_data').notNull(), // session完整数据
  metaData: text('meta_data').notNull(), // meta_data
  ipAddress: text('ip_address').notNull().default(''), // 客户端IP地址
  userId: integer('user_id').notNull().default(0), // 用户ID
  remark: text('remark'),
  isHandled: integer('is_handled', { mode: 'boolean' }).notNull().default(false), // 是否成功处理事件
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
